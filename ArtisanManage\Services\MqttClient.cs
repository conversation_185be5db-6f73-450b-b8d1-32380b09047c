﻿using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Client.Options;
using MQTTnet.Extensions.ManagedClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace ArtisanManage.Services
{
    public class MqttHelper
    {
        public static List<MyMqttClient> g_lstMqttClient = new List<MyMqttClient>();
        public static async Task<string> SendMessage(string msg,string topic)
        {
            //String parentTopic = "PaySuccess";
            // String clientId = "GID_PaySuccessServer1";// "GID_PaySuccess@@@server1";  
            MyMqttClient myClient = null;
            lock (g_lstMqttClient)
            {
                foreach (var cli in g_lstMqttClient)
                {
                    if (!cli.isBusy)
                    {
                        myClient = cli;
                        break;
                    }
                }
                if (myClient == null)
                {
                    myClient = new MyMqttClient(new MqttFactory().CreateManagedMqttClient());
                    g_lstMqttClient.Add(myClient);
                }
                myClient.isBusy = true;
            }
            string timestamp = CommonTool.GetTimeStamp();
            string brokerUrl = "ahnnwws.iot.gz.baidubce.com";
            //  logger.Info($"in notifyPayment, mark2");
            if (!myClient.client.IsStarted)
            {
                string userName = GetUserName(timestamp);
                string passWord = GetPassword(timestamp);
                Console.WriteLine(userName);
                Console.WriteLine(passWord);
                var options = new MqttClientOptions()
                {
                    ClientId = "Server_" + Guid.NewGuid().ToString()
                    // ClientId = clientId
                };
                options.ChannelOptions = new MqttClientTcpOptions()
                {
                    Server = brokerUrl,
                    Port = 1883
                    // TlsOptions=new MqttClientTlsOptions {SslProtocol= System.Security.Authentication.SslProtocols.None }
                };
                options.Credentials = new MqttClientCredentials()
                {
                    Username = userName,
                    Password =Encoding.UTF8.GetBytes(passWord)
                };
                options.ProtocolVersion = MQTTnet.Formatter.MqttProtocolVersion.V311;
                options.CleanSession = true;
                // options.KeepAlivePeriod = TimeSpan.FromSeconds(60);
                // options.KeepAliveSendInterval = TimeSpan.FromSeconds(10);

                var mOptions = new ManagedMqttClientOptionsBuilder()
                .WithAutoReconnectDelay(TimeSpan.FromSeconds(1))
                .WithClientOptions(options)
                .Build();
                await myClient.client.StartAsync(mOptions);
            }
          
            MqttApplicationMessage mqttMsg = new MqttApplicationMessage
            {
                Topic = topic,
                Payload = Encoding.UTF8.GetBytes(msg),
                Retain = false,
                QualityOfServiceLevel = MQTTnet.Protocol.MqttQualityOfServiceLevel.AtMostOnce,
            };
            await myClient.client.PublishAsync(mqttMsg);

            return "";
        }

        public static string GetUserName(string timestamp)
        {

            //{adp_type}@{IoTCoreId}|{DeviceKey}|{timestamp}|{algorithm_type}
            var adp_type = "thingidp";
            var IoTCoreId = "ahnnwws";
            var DeviceKey = "saleOrderNotify2";
            timestamp = timestamp.Substring(0, 10);
            var algorithm_type = "MD5";
            return @$"{adp_type}@{IoTCoreId}|{DeviceKey}|{timestamp}|{algorithm_type}";
    }
        public static string GetPassword(string timestamp)
        {
            Console.WriteLine(CommonTool.GetMD5_32("saleOrderNotify2&1752249664&MD5jtkkdPcDthpxOwdl"));
            //{adp_type}@{IoTCoreId}|{DeviceKey}|{timestamp}|{algorithm_type}
            var device_secret = "jtkkdPcDthpxOwdl";
            var device_key = "saleOrderNotify2";
            timestamp = timestamp.Substring(0, 10);
            var algorithm_type = "MD5";
            return CommonTool.GetMD5_32(@$"{device_key}&{timestamp}&{algorithm_type}{device_secret}");
   
        }
       
    }
    
    public class MyMqttClient
    {
        public MyMqttClient(IManagedMqttClient initClient)
        {
            client = initClient;
        }
        public IMqttClient client1;
        public IManagedMqttClient client;
        public bool isBusy = false;
    }
}
