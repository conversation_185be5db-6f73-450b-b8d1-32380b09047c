@page
@model ArtisanManage.Pages.Sheets.BuySheetSuggestModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" style="overflow:hidden;">
<head>

    <partial name="_QueryPageHead" model="Model.PartialViewModel" />

    <script type="text/javascript">
        window.g_operKey = '@Html.Raw(Model.OperKey)';

             var newCount = 1;

    	    var itemSource = {};
    	    $(document).ready(function () {
                @Html.Raw(Model.m_showFormScript)
               
                @Html.Raw(Model.m_createGridScript) 

                $("#gridItems").on("cellclick", function (event) {
                    return
                    var args = event.args;
                    var item_id = args.row.bounddata.item_id;
                    var item_name = args.row.bounddata.item_name;
                    var startDay = $('#startDay').jqxDateTimeInput('val');
                    var endDay = $('#endDay').jqxDateTimeInput('val');
                   
                    

                    var url = `Report/SalesDetail?sheetType=${sheetType}&item_id=${item_id}&item_name=${item_name}&startDay=${startDay}&endDay=${endDay}`;
                    
                    var title = '销售明细表';
                    if (sheetType=="xd") title = '订单明细表'
                    if (args.datafield == "item_name" && item_id) {
                        if (sup_name) url += `&supcust_id=${supcust_id}&sup_name=${sup_name}`;
                        if (seller_name) url += `&seller_id=${seller_id}&oper_name=${seller_name}`;
                        if (saleWay) url += `&saleWay=${saleWay}&sale_ways=${sale_ways}`;
                        if (remark) url += `&remark=${remark}`;
                        if (brand_name) url += `&brand_id=${brand_id}&brand_name=${brand_name}`;
                      

                 
                        window.parent.newTabPage(title, `${url}`);
                    }
                    if (args.datafield == "disc_amount" && item_id) {
                        if (sup_name) url += `&supcust_id=${supcust_id}&sup_name=${sup_name}`;
                        if (seller_name) url += `&seller_id=${seller_id}&oper_name=${seller_name}`;
                        if (saleWay) url += `&saleWay=${saleWay}&sale_ways=${sale_ways}`;
                        if (remark) url += `&remark=${remark}`;
                        if (brand_name) url += `&brand_id=${brand_id}&brand_name=${brand_name}`;
                        url += `&now_disc_amount_status=yes` ;
                        debugger
                        window.parent.newTabPage(title, `${url}`);
                    }

                    
                    
                });

                // 表格渲染完成后，计算日销量和预计可销售天数
                $("#gridItems").on('bindingcomplete', function (event) {
                    setTimeout(function() {
                        calculateDailySalesAndEstimatedDays();
                    }, 200);
                });

                QueryData();

            });
     

        function btnSelectItems_click() {
            debugger
            var rows =window.gridData_gridItems.localRows  
            var num = 0;
            var checkedRows = []
            for (var i in rows) {
                var row=rows[i]
                if (row.buy_qty) {
                    checkedRows.push({ item_id: row.item_id, item_name: row.item_name, quantity: row.buy_qty, unit_no: row.buy_unit_no, real_price: row.cost_price, last_time_price: row.last_buy_qty });
                    row.buy_qty = "";
                    num++;
                }
            }
            if (num == 0) {
                bw.toast("请输入采购数量")
                return;
            }
            var msg = {
                msgHead: 'BuySuggestView', action: 'selectMulti', checkedRows: checkedRows
            }
            window.parent.postMessage(msg, '*');           
            //$('#gridItems').jqxGrid('updatebounddata')
        }

        function btnQuery_click() {
            // 校验采购周期的输入
            var purchaseCycleValue = $('#purchase_cycle').jqxInput('val') || '30';
            var purchaseCycle = parseFloat(purchaseCycleValue);
            if (isNaN(purchaseCycle) || purchaseCycle < 1 || purchaseCycle > 365 || !Number.isInteger(purchaseCycle)) {
                bw.toast("采购周期必须是1-365之间的整数");
                return;
            }
            QueryData();
        }
        
        function calculateDailySalesAndEstimatedDays() {
            var rows = window.gridData_gridItems.localRows;

            //计算查询的天数
            var startDay = $('#startDay').jqxDateTimeInput('val');
            var endDay = $('#endDay').jqxDateTimeInput('val');
            if (!startDay || !endDay) return;

            var startDate = new Date(startDay);
            var endDate = new Date(endDay);
            var daysDiff = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
            if (daysDiff <= 0) daysDiff = 1; // 避免除以零

            // 获取采购周期
            var purchaseCycleValue = $('#purchase_cycle').jqxInput('val') || '30';
            var purchaseCycle = parseFloat(purchaseCycleValue);

            for (var i in rows) {
                var row = rows[i];
                
                //获取单位相关数据
                var targetUnitNo = row.buy_unit_no;
                var bUnitFactor = parseFloat(row.b_unit_factor) || 1;
                var mUnitFactor = parseFloat(row.m_unit_factor) || 1;
                var sUnitFactor = parseFloat(row.s_unit_factor) || 1;
                var bUnitNo = row.b_unit_no || '';
                var mUnitNo = row.m_unit_no || '';
                var sUnitNo = row.s_unit_no || '';

                // 获取总销量
                var xQuantityB = parseFloat(row.x_quantity_b) || 0;
                var xQuantityM = parseFloat(row.x_quantity_m) || 0;
                var xQuantityS = parseFloat(row.x_quantity_s) || 0;

                // 将总销量转换为小单位总数
                var totalSalesInSmallUnit = (xQuantityB * bUnitFactor) + (xQuantityM * mUnitFactor) + xQuantityS;

                var convertedSalesQty = 0;
                // 将总销量转换为目标单位
                if (targetUnitNo === bUnitNo && bUnitFactor > 0) {
                    convertedSalesQty = totalSalesInSmallUnit / bUnitFactor;
                } else if (targetUnitNo === mUnitNo && mUnitFactor > 0) {
                    convertedSalesQty = totalSalesInSmallUnit / mUnitFactor;
                } else {
                    convertedSalesQty = totalSalesInSmallUnit;
                    if (!targetUnitNo) {
                        targetUnitNo = sUnitNo;
                    }
                }

                // 近期日销量
                var dailySales = Math.round((convertedSalesQty / daysDiff) * 10000) / 10000; // 保留4位小数
                row.daily_sales_qty = dailySales.toString() + targetUnitNo;

                // 获取库存
                var stockQty = 0;
                var stockQtyRaw = parseFloat(row.stock_qty) || 0;

                // 将库存转换为目标单位
                if (targetUnitNo === bUnitNo && bUnitFactor > 0) {
                    stockQty = stockQtyRaw / bUnitFactor;
                } else if (targetUnitNo === mUnitNo && mUnitFactor > 0) {
                    stockQty = stockQtyRaw / mUnitFactor;
                } else {
                    stockQty = stockQtyRaw;
                }

                // 预计可销售天数
                var estimatedDays = 0;
                if (dailySales > 0) {
                    estimatedDays = Math.round((stockQty / dailySales) * 100) / 100; // 保留2位小数
                    if (estimatedDays <= 0) estimatedDays = 0;
                }
                row.estimated_sales_days = estimatedDays.toString();

                // 建议采购数 = 近期日销量 * 采购周期 - 库存
                var suggestedPurchaseQty = 0;
                if (dailySales > 0) {
                    suggestedPurchaseQty = (dailySales * purchaseCycle) - stockQty;
                    suggestedPurchaseQty = Math.ceil(suggestedPurchaseQty);
                    if (suggestedPurchaseQty <= 0) {
                        suggestedPurchaseQty = 0;
                    }
                }

                // 保存建议采购数
                row.buy_qty = suggestedPurchaseQty > 0 ? suggestedPurchaseQty.toString() + targetUnitNo : '';

            }

            // 更新表格
            try {
                $('#gridItems').jqxGrid('refresh');
            } catch (e) {
                console.log('表格更新失败:', e);
            }
        }

    </script>
</head>

<body style="overflow:hidden">
  

    <div style="display:flex;margin-top:20px;align-items:center;">
        <div id="divHead" class="headtail" style="width:calc(100% - 110px);">

            <div style="float:none;height:0px; clear:both;"></div>

        </div>
        
        <button onclick="btnQuery_click()" style="margin-right:20px;margin-top:30px;">查询</button>
        <button onclick="btnSelectItems_click()" style="margin-right:20px;margin-top:30px;">添加</button>
    </div>
    
    <div id="gridItems"></div>  
    <div id="divRowCount"><div>共<label id="rows_count">0</label>行</div></div> 
        

    <div id="popClient" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择客户</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
    <div id="popItem" style="display:none">
        <div id="clientCaption" style="height:30px;background-color:#fff; text-align:center;"><span style="font-size:20px;">选择商品</span></div>
        <div style="overflow:hidden;"> </div>
    </div>
</body>
</html>