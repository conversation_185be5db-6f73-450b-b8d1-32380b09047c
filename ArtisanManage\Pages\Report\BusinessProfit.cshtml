@page
@model ArtisanManage.CwPages.Report.BusinessProfitModel
@{
    Layout = null;
}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>BusinessProfit</title>
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>
    <script src="~/js/jQuery.print.js"></script>
    <script  src="~/js/jszip.min.js"></script>
    <script  src="~/js/FileSaver.js"></script>
    <script  src="~/js/excel-gen.js"></script>
    <script  src="~/js/demo.page.js"></script>
    <style>
        * {
            font-family: "微软雅黑"
        }

        body {

        }
        [v-cloak] {
          display: none;
        }

        ::-webkit-scrollbar {
            width: 16px;
            height: 16px;
            background-color: #fff;

        }

        ::-webkit-scrollbar-track {
            background-color: #fff;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 7px;
            -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
            background-color: #dddddd;
        }

        ::-webkit-scrollbar-corner {
            background-color: black;
        }

        #pages {
            display: flex;
            flex-direction: column;
            height: 90vh;
            cursor:pointer;
        }

        .pages_title {
            width: 100%;
            font-weight: 500;
            font-size: 25px;
            text-align: center;
            margin-top: 5px;
            padding-bottom:10px;
        }

        .pages_query {
            display: flex;
            width: 100%;
            margin-left: 100px;
            margin-right: 20px;
            align-items: center;
        }

        .query_item {
            display: flex;
            align-items: center;
            margin-right: 15px;
           
            margin-left:10px;
            height: 40px; /* 固定高度确保一致性 */
        }
        
        /* 确保标签垂直居中 */
        .query_item_label, 
        .query_item > div:first-child {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            height: 100%;
          
            margin: 0;
            padding: 0;
        }
        
        /* 调整输入框样式确保垂直居中 */
        .query_item input,
        .query_item .el-select {
            height: 32px;
            display: flex;
            align-items: center;
        }
        
        /* 调整复选框容器的垂直对齐 */
        .query_item .el-checkbox {
            display: flex;
            align-items: center;
            height: 100%;
        }
        
        /* 确保复选框标签垂直居中 */
        .el-checkbox__label {
            display: flex;
            align-items: center;
            height: 100%;
        }
        .query_item input {
            padding-top: 0px;
            padding-right: 0px;
            height: 35px;
            
            border: none;
            border-bottom: 1px #ddd solid;
            border-radius: 0;
            text-align: center;
        }
        .query_item input:active {

            border-bottom: 1px #ddd solid ;

        }
        .el-select__caret.el-input__icon.el-icon-arrow-up{
            position:absolute;
            right:0px;
            top:9px;
        }
        input[type="datetime-local"] {
            width: 180px !important;
        }
        .el-select {
            display: flex;
            align-items: center;
        }
        .el-date-editor.el-input, .el-date-editor.el-input__inner {
            width: 210px;
        }
        .item_input {
            width: 100px;
            height: 50%;
            z-index: 0;
            position: relative;
            border-style: none none solid;
            border-bottom: 1px solid #c7c7c7;
            border-radius: 0px;
            margin-left: 10px;


        }


        .pages_content {
            height: 50%;
            padding: 0 20px;
            margin-top:20px;
        }

        .level_1 {
            font-weight: bold;
            background-color: #f5f5f5;
        }
        .level_2 {
            font-weight: normal;
            padding-left: 20px;
        }
        .level_3 {
            font-weight: normal;
            padding-left: 40px;
        }
        .level_4 {
            font-weight: normal;
            padding-left: 60px;
        }
        .level_5 {
            font-weight: normal;
            padding-left: 80px;
        }
        .level_6 {
            font-weight: normal;
            padding-left: 100px;
        }
        .tree-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            text-align: center;
            margin-right: 5px;
            cursor: pointer;
            line-height: 16px;
            font-size: 14px;
            vertical-align: middle;
        }
        .tree-icon-placeholder {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 5px;
            vertical-align: middle;
        }
        .link {
           color:blue;
        }

        .pages_content table {
            width: 800px;
            border-width: 0;
            border-collapse: collapse;
            border: 1px solid #ebeef5;
            margin: 0 auto;
        }

        .pages_content table tbody {
            display: block;

            overflow-x: hidden;
            height: 80%;
        }
        @@media(max-height:700px) {
        .pages_content table tbody {
            display: block;

            overflow-x: hidden;

        }
         }
        .pages_content table thead, .pages_content tbody tr {
            display: table;
            width: 100%;
            table-layout: fixed;
        }

        .pages_content table thead {
            width: 100%;
        }

        .pages_content table thead th:nth-child(1), .pages_content table thead th:nth-child(5), .pages_content table tbody td:nth-child(1), .pages_content table tbody td:nth-child(5) {
            width: 50%
        }
        .pages_content table thead th:nth-child(2), .pages_content table thead th:nth-child(6), .pages_content table tbody td:nth-child(2), .pages_content table tbody td:nth-child(6) {
            width: 50%;
            text-align: center
        }
        .pages_content table thead th:nth-child(3), .pages_content table thead th:nth-child(7), .pages_content table tbody td:nth-child(3), .pages_content table tbody td:nth-child(7) {
            width: 20%
        }
        .pages_content table thead th:nth-child(4), .pages_content table thead th:nth-child(8), .pages_content table tbody td:nth-child(4), .pages_content table tbody td:nth-child(8) {
            width: 12.5%
        }
        .pages_content table tbody td:nth-child(3), .pages_content table tbody td:nth-child(7), .pages_content table tbody td:nth-child(4), .pages_content table tbody td:nth-child(8) {
            text-align: right;
        }
        @*高度*@
        .pages_content table thead th, .pages_content table tbody td {
            min-height: 40px;
            line-height: 40px;
        }
        @*边框*@
        .pages_content table thead th, .pages_content table tbody td {
            border-bottom: 1px solid #ebeef5;
            border-right: 1px solid #ebeef5;
        }
            .pages_content table thead th:last-child {
                border-right: 0;
            }
        @*背景*@
        .pages_content table tbody tr:nth-child(odd) {
            background: #fafafa;
        }
        .pages_content table tbody tr:hover {
            background-color: #f5f7fa;
        }

        .pages_content table thead th, .pages_content table tbody td {
            padding: 0 15px;
        }

        .main-button {
            margin-left: 30px;
            margin-top: 7px;
            background-color: #f2f2f2;
            border-style: solid;
            border-color: #e6e6e6;
            border-width: 1px;
            border-radius: 5px;
            width: 72px;
            height: 32px;
            background-color: #ffcccc;
            border-color: #ffcccc;
        }
       
        .main-button:hover {
            background-color: #eebbbb;
            border-color: #eebbbb;
        }

        .main-button:active {
            background-color: #ddaaaa;
        }
        .el-input__icon {
            position:absolute;
            left:0px;
            top:0px;
        }

        .tree-icon {
            display: inline-block;
            width: 16px;
            text-align: center;
            margin-right: 5px;
            cursor: pointer;
        }
        .child-subject {
            padding-left: 20px;
        }

        /* 添加金额列右对齐样式 */
        .pages_content table tbody td:nth-child(2),
        .pages_content table tbody td:nth-child(3),
        .pages_content table thead th:nth-child(2),
        .pages_content table thead th:nth-child(3) {
            text-align: right;
        }
        
        /* 确保表头也右对齐 */
        .pages_content table thead th:nth-child(2),
        .pages_content table thead th:nth-child(3) {
            padding-right: 15px;
        }
        
        /* 为金额单元格添加右侧内边距 */
        .pages_content table tbody td:nth-child(2),
        .pages_content table tbody td:nth-child(3) {
            padding-right: 15px;
        }
        
        /* 保持链接样式但仍然右对齐 */
        .link {
            color: blue;
            text-align: right;
            display: block;
        }
    </style>

</head>
<body>
    <div id="root" >

        <div id="pages" class="" ref="pages" v-cloak>
            <div class="pages_title">经营利润表</div>

            <div id="page_top" style="margin-left:0px">

                <div class="pages_query">
                    <div class="query_item">
                        <div class="query_item_label" style="text-align:right;">从</div>
                        <el-date-picker type="datetime" 
                               v-model="startDateProxy" 
                               format="yyyy-MM-dd HH:mm"
                               value-format="yyyy-MM-dd'T'HH:mm"
                               class="form-control" 
                               style="width:180px;">
                    </div>
                    <div class="query_item">
                        <div class="query_item_label" style="text-align:right;">到</div>
                        <el-date-picker type="datetime" 
                               v-model="endDateProxy" 
                               format="yyyy-MM-dd HH:mm"
                               value-format="yyyy-MM-dd'T'HH:mm"
                               class="form-control" 
                               style="width:180px;">
                    </div>

                    <div class="query_item">
                        <div class="query_item_label" style="width:45px;text-align:right;">部门</div>
                        <el-select v-model="selectDepartId" clearable placeholder="全部" style="width: 90px;">
                            <el-option v-for="item in depart" :key="item.id" :label="item.value" :value="item.id"> </el-option>
                        </el-select>
                    </div>

                    <div class="query_item">
                        <div class="query_item_label" style="width:50px;text-align:right;">业务员</div>
                        <el-select v-model="selectOperId" clearable placeholder="全部" filterable style="width: 90px;">
                            <el-option label="全部" value=""></el-option>
                            <el-option v-for="item in filteredSellers" :key="item.id" :label="item.value" :value="item.id"> </el-option>
                        </el-select>
                    </div>
                  
                    <div class="query_item">
                        <el-checkbox v-model="checked">核算盘点</el-checkbox>
                    </div>
                    
                    <div class="query_item">
                        <button class="main-button" @@click="queryData">查询</button>
                        <button class="main-button" @@click="print" style="margin-left: 10px;">导出</button>
                    </div>
                </div>





            <div class="pages_content">
                <table id="test_table">
                    <thead>
                        <tr>
                            <th class="level_1">项目</th>
                            <th class="level_1">金额</th>
                            <th class="level_1">欠款</th>
                            @*<th class="level_4">{{StartDay+' '+EndDay}}</th>*@
                        </tr>
                    </thead>

                    <tbody ref="tbodyRef">
                        <tr>
                            <td class="level_1">销售收入</td>
                            <td class="link" @@click="toSalesSummary()" ref="closingBalance_1">{{toMoney(parseFloat(incomeAndcost[0].income))||'0'}}</td>
                            <td>{{toMoney(parseFloat(saleArrears))}}</td>
                        </tr>


                        <tr>
                            <td class="level_1">成本合计</td>
                            <td class="link" ref="closingBalance_1">{{(toMoney(toMoney(incomeAndcost[0].cost) +toMoney(incomeAndcost[0].free_cost_gift)+toMoney(incomeAndcost[0].free_cost_change)+toMoney(incomeAndcost[0].free_cost_display)+toMoney(incomeAndcost[0].free_cost_other)))||'0'}}</td>
                            <td></td>
                        </tr>
                        <tr v-if="incomeAndcost[0].cost">
                            <td class="level_2">销售成本</td>
                            <td class="link" @@click="toSalesSummary()" ref="closingBalance_1">{{toMoney(incomeAndcost[0].cost)||'0'}}</td>
                            <td></td>
                        </tr>
                        <tr v-if="incomeAndcost[0].free_cost_change">
                            <td class="level_2">赠品成本(兑奖)</td>
                            <td class="link" @@click="toSalesSummary()" ref="closingBalance_1">{{toMoney(incomeAndcost[0].free_cost_change)||'0'}}</td>
                            <td></td>
                        </tr>
                        <tr v-if="incomeAndcost[0].free_cost_display">
                            <td class="level_2">赠品成本(陈列)</td>
                            <td class="link" @@click="toSalesSummary()" ref="closingBalance_1">{{toMoney(incomeAndcost[0].free_cost_display)||'0'}}</td>
                            <td></td>
                        </tr>
                        <tr v-if="incomeAndcost[0].free_cost_gift">
                            <td class="level_2">赠品成本(赠品)</td>
                            <td class="link" @@click="toSalesSummary()" ref="closingBalance_1">{{toMoney(incomeAndcost[0].free_cost_gift)||'0'}}</td>
                            <td></td>
                        </tr>
                        <tr v-if="incomeAndcost[0].free_cost_other">
                            <td class="level_2" title="赠品备注不包含“赠品”、“兑奖”、“陈列”">赠品成本(其它)</td>
                            <td class="link" @@click="toSalesSummary()" ref="closingBalance_1">{{toMoney(incomeAndcost[0].free_cost_other)||'0'}}</td>
                            <td></td>
                        </tr>

                        <tr>
                            <td class="level_1">销售利润</td>
                            <td class="link" @@click="toSalesSummary()" ref="closingBalance_1">{{toMoney(parseFloat(incomeAndcost[0].income - incomeAndcost[0].cost-incomeAndcost[0].free_cost ))||'0'}}</td>
                            <td></td>
                        </tr>


                        <tr>
                            <td class="level_1">优惠合计</td>
                            <td class="link" ref="closingBalance_1">{{(toMoney(Number(skDiscAmount) - Number(fkDiscAmount) + Number(incomeAndcost[0].dsc) + Number(buydisk[0].amount) ))||'0'}}</td>
                            <td></td>
                        </tr>
                        <tr v-if="incomeAndcost[0].dsc">
                            <td class="level_2">销售优惠</td>
                            <td class="link" @@click="toSaleSheetView()" ref="closingBalance_1">{{toMoney(incomeAndcost[0].dsc)||'0'}}</td>
                            <td></td>
                        </tr>
                        <tr v-if="skDiscAmount!=0">
                            <td class="level_2">收款优惠</td>
                            <td class="link" @@click="toGetArrearView()" ref="closingBalance_1">{{toMoney(skDiscAmount)||'0'}}</td>
                            <td></td>
                        </tr>
                        <tr v-if="buydisk[0].amount">
                            <td class="level_2">采购优惠</td>
                            <td class="link" @@click="toBuyDiskView()" ref="closingBalance_1">{{toMoney(buydisk[0].amount)||'0'}}</td>
                            <td></td>
                        </tr>
                        <tr v-if="fkDiscAmount!=0">
                            <td class="level_2">付款单优惠</td>
                            <td class="link" @@click="toPayArrearView()" ref="closingBalance_1">{{toMoney(-fkDiscAmount)||'0'}}</td>
                            <td></td>
                        </tr>

                        <tr>
                            <td class="level_1">其他收入合计</td>
                            <td class="link" @@click="toTotal(otherincome_detail,otherincome_amout)" ref="closingBalance_1">{{toMoney(otherincome_amout)||'0'}}</td>
                            <td>{{toMoney(parseFloat(otherIncomeArrears))}}</td>
                        </tr>
                        <tr v-for="(item,index) in otherincome_detail" :key="item.sub_name">

                            <td class="level_3" ref="closingBalance_1">{{item.sub_name}}</td>
                            <td class="link" @@click="toSheet(item.sub_id,item.sub_name,toMoney(item.detail))" ref="closingBalance_1">{{toMoney(item.detail)}}</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="level_1">费用合计</td>
                            <td class="link" @@click="toTotal(fee_detail,fee_amout)" ref="closingBalance_1">{{toMoney(fee_amout)||'0'}}</td>
                            <td>{{toMoney(parseFloat(feeArrears))}}</td>
                        </tr>

                        <!-- 使用树形结构显示费用科目 -->
                        <template v-for="subject in feeSubjectsTree">
                            <subject-tree-item 
                                :key="subject.sub_id"
                                :subject="subject"
                                :expanded-subjects="expandedSubjects"
                                @@toggle="toggleSubject"
                                @@click-sheet="toSheet">
                            </subject-tree-item>
                        </template>

                        <tr>
                            <td class="level_1">仓库盈亏合计</td>
                            <td class="link" ref="closingBalance_1">
                                {{ checked
                                ? toMoney(parseFloat(inventory[0].cost_amount - bs[0].cost_amount))  
                                : toMoney(bs[0].cost_amount) }}
                            </td>
                            <td></td>
                        </tr>


                        <tr v-if="inventory[0].cost_amount && checked ">
                            <td class="level_2">盘点盈亏</td>
                            <td class="link" @@click="toInventoryView()" ref="closingBalance_1">{{toMoney(inventory[0].cost_amount)||'0'}}</td>
                            <td></td>
                        </tr>
                        <tr v-if="bs[0].cost_amount">
                            <td class="level_2">报损</td>
                            <td class="link" @@click="toBSView()" ref="closingBalance_1">{{toMoney(bs[0].cost_amount)||'0'}}</td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="level_1">收欠款</td>
                            
                            <td class="link" @@click="toGetArrearView()">{{toMoney(parseFloat(gottenArrears))}}</td>
                            <td></td>
                        </tr>
                        @* <tr v-if=" checked"> *@
                        @*     <td class="level_1">净利润</td> *@
                        @*     <td ref="closingBalance_1">{{toMoney(parseFloat(incomeAndcost[0].income - skDiscAmount + fkDiscAmount -incomeAndcost[0].dsc- incomeAndcost[0].cost-incomeAndcost[0].free_cost +otherincome_amout-fee_amout - bs[0].cost_amount - inventory[0].cost_amount*(-1)))||'0'}}</td> *@
                        @*     <td></td> *@
                        @* </tr> *@
                        @* <tr v-if=" !checked"> *@
                        @*     <td class="level_1">净利润</td> *@
                        @*     <td ref="closingBalance_1">{{toMoney(parseFloat(incomeAndcost[0].income - skDiscAmount + fkDiscAmount -incomeAndcost[0].dsc- incomeAndcost[0].cost-incomeAndcost[0].free_cost +otherincome_amout-fee_amout - bs[0].cost_amount))||'0'}}</td> *@
                        @*     <td></td> *@
                        @* </tr> *@
                        <!-- 净利润行 -->
                        <tr>
                            <td class="level_1">净利润</td>
                            <td class="link">{{ netProfit }}</td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    </div>
      <script> 
        var g_operKey = '@Model.OperKey';
    </script>
    <script>
        // 定义递归组件
        Vue.component('subject-tree-item', {
            template: '#subject-tree-item-template',
            props: {
                subject: Object,
                expandedSubjects: Object
            },
            computed: {
                hasChildren() {
                    return this.subject.children && this.subject.children.length > 0;
                },
                isExpanded() {
                    return this.expandedSubjects[this.subject.sub_id];
                }
            },
            methods: {
                toMoney(val) {
                    if (val === null || val === undefined) return '0.00';
                    // 确保转换为数字类型
                    const num = parseFloat(val);
                    if (isNaN(num)) return '0.00';
                    // 格式化为固定两位小数
                    return num.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
                }
            }
        });

        var vm = new Vue({
            el: '#root',
            data() {
                // 获取当前日期的格式化字符串
                const today = new Date();
                const year = today.getFullYear();
                const month = String(today.getMonth() + 1).padStart(2, '0');
                const day = String(today.getDate()).padStart(2, '0');
                
                // 格式化为HTML datetime-local输入所需的格式 (YYYY-MM-DDTHH:MM)
                const startDay = `${year}-${month}-01T00:00`; // 当月第一天
                const endDay = `${year}-${month}-${day}T23:59`; // 今天
                
                return {
                    // 日期相关
                    StartDay: startDay,
                    EndDay: endDay,
                    
                    // 查询条件
                    selectOperId: '',
                    selectDepartId: '',
                    seller: [],
                    depart: [],
                    checked: false,
                    
                    // 数据存储
                    incomeAndcost: [{ 
                        income: 0, cost: 0, dsc: 0, 
                        free_cost_gift: 0, free_cost_change: 0, 
                        free_cost_display: 0, free_cost_other: 0,
                        free_cost: 0
                    }],
                    inventory: [{ cost_amount: 0 }],
                    bs: [{ cost_amount: 0 }],
                    buydisk: [{ amount: 0 }],
                    fee_detail: [],
                    otherincome_detail: [],
                    
                    // 金额数据
                    skDiscAmount: 0,
                    fkDiscAmount: 0,
                    fee_amout: 0,
                    otherincome_amout: 0,
                    
                    // 欠款数据
                    saleArrears: 0,
                    feeArrears: 0,
                    otherIncomeArrears: 0,
                    gottenArrears: 0,
                    
                    // 树状结构相关
                    expandedSubjects: {}
                }
            },
            methods: {
                // 格式化金额
                toMoney(val) {
                    /*
                    if (val === null || val === undefined) return '0.00';
                    // 确保转换为数字类型
                    const num = parseFloat(val);
                    if (isNaN(num)) return '0.00';
                    // 格式化为固定两位小数
                    return num.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
                    */
                   return toMoney(val)
                },
                
                // 获取部门路径
                getDepartPath() {
                    if (!this.selectDepartId) return '';
                    const dept = this.depart.find(d => d.id === this.selectDepartId);
                    return dept ? dept.path : '';
                },
                
                // 获取部门名称
                getDepartName() {
                    if (!this.selectDepartId) return '';
                    const dept = this.depart.find(d => d.id === this.selectDepartId);
                    return dept ? dept.value : '';
                },
                
                // 获取业务员名称
                getSellerName() {
                    if (!this.selectOperId) return '';
                    const seller = this.seller.find(s => s.id === this.selectOperId);
                    return seller ? seller.value : '';
                },
                
                // 获取业务员数据
                getSellerId() {
                    $.ajax({
                        url: '/api/BusinessProfit/GetSellerId',
                        type: 'get',
                        data: { operKey: g_operKey },
                        dataType: 'json',
                        success: (res) => {
                            if (res.result === 'OK') {
                                this.seller = res.seller || [];
                                this.depart = res.depart || [];
                            } else {
                                console.error('获取业务员数据失败:', res);
                            }
                        },
                        error: (err) => {
                            console.error('获取业务员数据异常:', err);
                        }
                    });
                },
                
                // 查询数据
                queryData() {
                    const param = {
                        operKey: g_operKey,
                        startTime: this.StartDay,
                        endTime: this.EndDay,
                        operId: this.selectOperId,
                        departPath: this.getDepartPath(),
                        checked: this.checked
                    };
                    
                    $.ajax({
                        url: '/api/BusinessProfit/GetSubjectBanlance',
                        type: 'get',
                        data: param,
                        dataType: 'json',
                        success: (response) => {
                            if (response.result === 'OK') {
                                // 处理收入成本数据
                                if (response.incomeAndcost && response.incomeAndcost.length > 0) {
                                    this.incomeAndcost = response.incomeAndcost;
                                }
                                
                                // 处理库存数据
                                if (response.inventory && response.inventory.length > 0) {
                                    this.inventory = response.inventory;
                                }
                                
                                // 处理BS数据
                                if (response.bs && response.bs.length > 0) {
                                    this.bs = response.bs;
                                }
                                
                                // 处理购买折扣数据
                                if (response.buydisk && response.buydisk.length > 0) {
                                    this.buydisk = response.buydisk;
                                }
                                
                                // 处理费用明细
                                this.fee_detail = response.fee_detail || [];
                                console.log('加载的费用明细:', this.fee_detail);
                                // 检查过滤后的费用明细
                                const filteredFeeDetail = this.fee_detail.filter(item => 
                                    item.sub_code > 10 && 
                                    item.sub_name != '全部' && 
                                    !item.sub_name.includes('损益类')
                                );
                                console.log('过滤后的费用明细:', filteredFeeDetail);
                                
                                // 处理其他收入明细
                                this.otherincome_detail = response.otherincome_detail || [];
                                
                                // 处理折扣金额
                                if (response.skdiscamount && response.skdiscamount.length > 0) {
                                    const skDisc = response.skdiscamount.find(r => r.sheet_type === 'SK');
                                    const fkDisc = response.skdiscamount.find(r => r.sheet_type === 'FK');
                                    if (skDisc) {
                                        this.skDiscAmount = skDisc.amount || 0;
                                    }
                                    if (fkDisc) {
                                        this.fkDiscAmount = fkDisc.amount || 0;
                                    }
                                }
                                
                                // if (response.fkdiscamount && response.fkdiscamount.length > 0) {
                                //     const fkDisc = response.fkdiscamount.find(r => r.sheet_type === 'FK');
                                //     if (fkDisc) {
                                //         this.fkDiscAmount = fkDisc.amount || 0;
                                //     }
                                // }
                                
                                // 处理欠款数据
                                this.saleArrears = response.saleArrears ? response.saleArrears.left_amount : 0;
                                this.feeArrears = response.feeArrears ? response.feeArrears.left_amount : 0;
                                this.otherIncomeArrears = response.otherIncomeArrears ? response.otherIncomeArrears.left_amount : 0;
                                this.gottenArrears = response.gottenArrears ? response.gottenArrears.gotten_amount : 0;
                                
                                // 计算合计金额
                                this.calculateTotals();
                            } else {
                                console.error('API返回错误:', response);
                            }
                        },
                        error: (xhr, textStatus, errorThrown) => {
                            console.error('API请求异常:', textStatus, errorThrown);
                        }
                    });
                },
                
                // 计算合计金额
                calculateTotals() {
                    // 计算其他收入合计
                    let sum1 = 0;
                    if (this.otherincome_detail && this.otherincome_detail.length > 0) {
                        this.otherincome_detail.forEach(item => {
                            sum1 += Number(item.detail || 0);
                        });
                    }
                    this.otherincome_amout = sum1;
                    
                    // 计算费用合计
                    let sum2 = 0;
                    if (this.fee_detail && this.fee_detail.length > 0) {
                        this.fee_detail.forEach(item => {
                            sum2 += Number(item.detail || 0);
                        });
                    }
                    this.fee_amout = sum2;
                },
                
                // 格式化开始日期
                formatStartDate() {
                    console.log('格式化开始日期:', this.StartDay);
                },
                
                // 格式化结束日期
                formatEndDate() {
                    console.log('格式化结束日期:', this.EndDay);
                },
                
                // 打印/导出Excel
                print() {
                    // 使用ExcelGen库导出表格为Excel
                    let excel = new ExcelGen({
                        "src_id": "test_table",
                        "show_header": true
                    });
                    
                    // 获取文件名（包含日期范围）
                    const startDate = this.StartDay.split('T')[0];
                    const endDate = this.EndDay.split('T')[0];
                    const fileName = `经营利润表(${startDate}至${endDate}).xlsx`;
                    
                    // 生成并下载Excel文件
                    excel.generate(fileName);
                },
                
                // 打印页面
                printPage() {
                    console.log('打印页面功能待实现');
                },
                
                // 跳转到销售汇总
                toSalesSummary() {
                    const startDay = this.StartDay.replace('T', ' ');
                    const endDay = this.EndDay.replace('T', ' ');
                    
                    let url = `Report/SalesSummaryByItem?sheetType=x&startDay=${startDay}&endDay=${endDay}&byHappenTime=true`;
                    if (this.selectOperId) {
                        let sellerName = this.getSellerName();
                        url += `&seller_id=${this.selectOperId}&seller_name=${sellerName}`;
                    }
                    
                    window.parent.newTabPage("销售汇总(商品)", url);
                },
                
                // 切换科目展开状态
                toggleSubject(subId) {
                    this.$set(this.expandedSubjects, subId, !this.expandedSubjects[subId]);
                },
                
                // 跳转到明细表
                toSheet(subId, subName, amt, subContent = null) {
                    const sub_ids = []; const sub_names = []
                    if (subContent && subContent.children && subContent.children.length > 0) {
                        subContent.children.forEach(child => {
                            sub_ids.push(child.sub_id);
                            sub_names.push(child.sub_name);
                            if (child.children && child.children.length > 0) {
                                child.children.forEach(grandChild => {
                                    sub_ids.push(grandChild.sub_id);
                                    sub_names.push(grandChild.sub_name);
                                });
                            }
                        });
                    }
                    subId += sub_ids.join(',');
                    subName += sub_names.join(',');
                    let url = `Report/FeeOutDetail?&startDay=${this.StartDay}&endDay=${this.EndDay}&sub_id=${subId}&sub_name=${subName}&byHappenTime=true`;
                    if (this.selectOperId) {
                        let sellerName = this.getSellerName();
                        url += `&seller_id=${this.selectOperId}&seller_name=${sellerName}`;
                    }
                    
                    if (amt) {
                        window.parent.newTabPage("收入支出明细表", url);
                    }
                },
                
                // 跳转到盘点视图
                toInventoryView() {
                    console.log('跳转到盘点视图');
                },
                
                // 跳转到BS视图
                toBSView() {
                    console.log('跳转到BS视图');
                },
                
                // 跳转到收欠款视图
                toGetArrearView() {
                    let url = `Sheets/GetArrearsSheetView?startDay=${this.StartDay}&endDay=${this.EndDay}&byHappenTime=true&status=approved`;
                    if (this.selectOperId) {
                        let sellerName = this.getSellerName();
                        url += `&seller_id=${this.selectOperId}&seller_name=${sellerName}`;
                    }
                    if (this.selectDepartId) {
                        let path = this.getDepartPath();
                        let departName = this.getDepartName();
                        url += `&path='${path}'&depart_path=${this.selectDepartId}&depart_path_label=${departName}`;
                    }
                    window.parent.newTabPage("查收款单", url);
                }
            },
            computed: {
                // 中间代理，绑定给 el-date-picker
                startDateProxy: {
                    get() {
                        return this.StartDay ? new Date(this.StartDay) : null
                    },
                    set(val) {
                        this.StartDay = val
                    }
                },
                endDateProxy: {
                    get() {
                        return this.EndDay ? new Date(this.EndDay) : null
                    },
                    set(val) {
                        this.EndDay = val
                    }
                },

                // 过滤后的业务员列表
                filteredSellers() {
                    if (!this.selectDepartId) return this.seller;
                    
                    return this.seller.filter(s => {
                        return s.depart_path && s.depart_path.includes('/' + this.selectDepartId + '/');
                    });
                },
                
                // 费用科目树
                feeSubjectsTree() {
                    console.log('计算feeSubjectsTree，原始数据:', this.fee_detail);
                    
                    // 如果fee_detail为空，返回空数组
                    if (!this.fee_detail || this.fee_detail.length === 0) {
                        return [];
                    }
                    
                    // 创建一个映射，用于快速查找科目
                    const subjectMap = {};
                    
                    // 首先过滤掉不需要的高级科目
                    const filteredFeeDetail = this.fee_detail.filter(item => {
                        // 排除 sub_code = 0 的科目（全部）
                        if (item.sub_code === 0 || item.sub_code === '0') {
                            return false;
                        }
                        
                        // 排除 sub_code < 10 的科目
                        if (item.sub_code && parseInt(item.sub_code) < 10) {
                            return false;
                        }
                        
                        // 排除名称中包含"损益类"的科目
                        if (item.sub_name && item.sub_name.includes('损益类')) {
                            return false;
                        }
                        
                        // 排除名称为"全部"的科目
                        if (item.sub_name && item.sub_name === '全部') {
                            return false;
                        }
                        
                        return true;
                    });
                    
                    console.log('过滤后的费用科目:', filteredFeeDetail);
                    
                    // 使用过滤后的数据创建映射
                    filteredFeeDetail.forEach(item => {
                        // 确保每个项都有一个唯一的ID
                        if (item && item.sub_id) {
                            // 确保detail是数值类型
                            const detail = parseFloat(item.detail) || 0;
                            subjectMap[item.sub_id] = { ...item, detail, children: [], level: 2 }; // 默认顶级科目为level 2
                        }
                    });
                    
                    // 构建树状结构
                    const rootSubjects = [];
                    filteredFeeDetail.forEach(item => {
                        if (!item || !item.sub_id) return;
                        
                        // 检查parent_id是否存在且不为空，且父科目在过滤后的列表中
                        if (!item.parent_id || item.parent_id === '0' || item.parent_id === 0 || !subjectMap[item.parent_id]) {
                            // 顶级科目或父科目被过滤掉的科目
                            rootSubjects.push(subjectMap[item.sub_id]);
                        } else {
                            // 子科目，添加到父科目的children数组
                            subjectMap[item.parent_id].children.push(subjectMap[item.sub_id]);
                            // 设置子科目的level为父科目的level + 1
                            subjectMap[item.sub_id].level = subjectMap[item.parent_id].level + 1;
                        }
                    });
                    
                    // 递归计算父科目的金额（自下而上）
                    const calculateParentAmount = (subject) => {
                        if (!subject.children || subject.children.length === 0) {
                            return subject.detail || 0;
                        }
                        
                        let totalAmount = parseFloat(subject.detail) || 0;
                        
                        // 如果有子科目，累加子科目的金额
                        subject.children.forEach(child => {
                            totalAmount += calculateParentAmount(child);
                        });
                        
                        // 更新父科目的金额
                        subject.detail = totalAmount;
                        
                        return totalAmount;
                    };
                    
                    // 计算每个根科目的总金额
                    rootSubjects.forEach(subject => {
                        calculateParentAmount(subject);
                    });
                    
                    console.log('构建的树状结构(含合计):', rootSubjects);
                    return rootSubjects;
                },
                
                // 净利润计算
                netProfit() {
                    // 确保所有数据存在，避免访问未定义的属性
                    const incomeData = this.incomeAndcost[0] || {};
                    const bsData = this.bs[0] || {};
                    const inventoryData = this.inventory[0] || {};
                    const cgDiscAmount= this.buydisk[0] || {};

                    // 转换所有变量为数值，处理可能的字符串或空值
                    const income = parseFloat(incomeData.income) || 0;
                    const cgDisc = parseFloat(cgDiscAmount.amount) || 0;
                    const skDisc = parseFloat(this.skDiscAmount) || 0;
                    const fkDisc = parseFloat(this.fkDiscAmount) || 0;
                    const dsc = parseFloat(incomeData.dsc) || 0;
                    const cost = parseFloat(incomeData.cost) || 0;
                    const freeCost = parseFloat(incomeData.free_cost) || 0;
                    const otherIncome = parseFloat(this.otherincome_amout) || 0;
                    const fee = parseFloat(this.fee_amout) || 0;
                    const bsCost = parseFloat(bsData.cost_amount) || 0;
                    const inventoryCost = parseFloat(inventoryData.cost_amount) || 0;

                    // 根据 checked 状态调整库存成本计算
                    let adjustment = 0;
                    if (this.checked) {
                        adjustment = -inventoryCost; // 相当于 inventoryCost * (-1)
                    }

                    // 计算净利润，采购优惠是负值，所以直接减去而不是加上
                    const result = income - cgDisc - skDisc + fkDisc - dsc - cost - freeCost
                        + otherIncome - fee - bsCost - adjustment;

                    // 使用 toMoney 格式化，处理可能的 NaN
                    return this.toMoney(result) || '0';
                }
            },
            watch: {
                // 监听 selectDepartId 的变化
                selectDepartId(newVal) {
                    // 当 selectDepartId 变化时，将 selectOperId 重置为空
                    this.selectOperId = '';
                }
            },
            created() {
                this.getSellerId();
            },
            mounted() {
                // 初始化查询
                this.queryData();
            }
        });
    </script>
   
    <script type="text/x-template" id="subject-tree-item-template">
        <tbody>
            <!-- 当前科目 -->
            <tr>
                <td :class="'level_' + (subject.level || 2)" :style="{ paddingLeft: (subject.level - 1) * 20 + 'px' }">
                    <span v-if="hasChildren" class="tree-icon" @@click.stop="$emit('toggle', subject.sub_id)">
                        {{ isExpanded ? '▼' : '►' }}
                    </span>
                    <span v-else class="tree-icon-placeholder"></span>
                    {{ subject.sub_name }}
                </td>
                <td class="link money-cell" @@click="$emit('click-sheet', subject.sub_id, subject.sub_name, toMoney(subject.detail), subject)" ref="closingBalance_1">
                    {{toMoney(subject.detail)}}
                </td>
                <td></td>
            </tr>
            
            <!-- 子科目 (递归) -->
            <template v-if="isExpanded && hasChildren">
                <subject-tree-item 
                    v-for="child in subject.children" 
                    :key="child.sub_id"
                    :subject="Object.assign({}, child, { level: (subject.level || 2) + 1 })"
                    :expanded-subjects="expandedSubjects"
                    @@toggle="$emit('toggle', $event)"
                    @@click-sheet="$emit('click-sheet', $event, $event2, $event3)">
                </subject-tree-item>
            </template>
        </tbody>
    </script>
   
</body>
</html>
