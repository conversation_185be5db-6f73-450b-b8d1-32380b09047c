﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;

namespace ArtisanManage.Pages.BaseInfo
{
    public class SalesSummaryByClientModel : PageQueryModel
    { 
        public SalesSummaryByClientModel(CMySbCommand cmd) : base(Services.MenuId.salesSummaryByClient)
        {
            this.UsePostMethod = true;
            this.cmd = cmd;
            this.PageTitle = "销售汇总(客户)";
            CanQueryByApproveTime = true;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time+sd.happen_time",ForQuery=true, CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time+sd.happen_time",ForQuery=true,   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }"
                }},
                {"status",new DataItem(){FldArea="divHead",Title="单据状态",Checkboxes=true,LabelFld = "status_name",ButtonUsage = "list",CompareOperator="=",Value="approved",Label="已审核",
                        Source = @"[{v:'normal',l:'正常单据',condition:""sm.red_flag is null""},
                                   {v:'unapproved',l:'未审核',condition:""sm.approve_time is null""},
                                   {v:'approved',l:'已审核',condition:""sm.approve_time is not null and sm.red_flag is null""}]"


                }},
                {"trade_type",new DataItem(){FldArea="divHead",Checkboxes=true, Title="交易类型",ButtonUsage = "list",CompareOperator="=",Value="",Label="",
                        Source = @"[{v:'ALL',l:'所有',condition:""true""},
                                   {v:'XT',l:'销退',condition:""coalesce(trade_type,'X') in ('X','T','XD','TD')""},
                                   {v:'DH',l:'定货还货',condition:""trade_type='DH'""},
                                   {v:'JH',l:'借还货',condition:""trade_type in ('J','H')""},
                                   {v:'CL',l:'陈列兑付',condition:""trade_type ='CL'""},
                                   {v:'HH',l:'换货',condition:""trade_type in ('HR','HC')""}]"
                }},
                {"arrears_status",new DataItem(){FldArea="divHead",Title="欠款情况",Hidden=true, Checkboxes=true, ButtonUsage = "list",CompareOperator="=",
                    Source = @"[{v:'cleared',l:'已结清',condition:""abs(total_amount-paid_amount-disc_amount)<0.1""},
                                 {v:'uncleared',l:'未结清',condition:""abs(total_amount-paid_amount-disc_amount)>0.1""},
                                 {v:'all',l:'所有',condition:""true""}]"
                }},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客户名称",Checkboxes=true,LabelFld="sup_name",ButtonUsage="event",CompareOperator="=",DropDownWidth = "200",SqlFld="sm.supcust_id",
                SqlForOptions=CommonTool.selectSupcust } },
             
                {"group_id",new DataItem(){Title="渠道",Checkboxes=true,FldArea="divHead", LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_group",
                    SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},
                {"rank_id",new DataItem(){Title="等级",Checkboxes=true, FldArea="divHead",LabelFld="rank_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_rank",
                    SqlForOptions ="select rank_id as v,rank_name as l from info_supcust_rank"}},
                {"department_id",new DataItem(){Title="所属部门",Hidden=true,TreePathFld="department_path", FldArea="divHead",LabelFld="department_id_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=false,DropDownWidth="150", CompareOperator="=",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                {"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="sd.item_id",DropDownWidth="300",
              SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode   }},
                {"other_class",new DataItem(){Title="类别",FldArea="divHead",Checkboxes=true,MaxRecords="1000",LabelFld="class_name",CtrlType="jqxDropDownTree",TreePathFld="other_class",MumSelectable=true,CompareOperator="like",
                   SqlForOptions=CommonTool.selectClasses} },
 
			    {"brand_id", CommonTool.GetDataItem("brand_id", new DataItemChange(){SqlFld="item_brand"})},

				{"branch_id",new DataItem(){Title="仓库",FldArea="divHead",Checkboxes=true,LabelFld="branch_name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.branch_id",
                SqlForOptions=CommonTool.selectBranch } },
                {"seller_id",new DataItem(){Title="业务员",FldArea="divHead",Checkboxes=true, LabelFld="seller_name",ButtonUsage="list",CompareOperator="=",SqlFld="seller_id",SqlForOptions=CommonTool.selectSellers } },
                   // SqlForOptions ="select oper_id as v,oper_name as l from info_operator"}},
                {"make_brief",new DataItem(){Title="整单备注",FldArea="divHead",CompareOperator="ilike" } },
                {"remark",new DataItem(){Title="行备注",SqlFld="sd.remark",Checkboxes=true, FldArea="divHead",Hidden=false, ButtonUsage = "list",SqlForOptions="select brief_text as v,brief_text as l from info_sheet_detail_brief", CompareOperator="ilike" } },
                {"depart_path",new DataItem(){Title="部门", FldArea="divHead",LabelFld="",ButtonUsage="list", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", TreePathFld="depart_path",CompareOperator="like",LikeWrapper="/",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
              /*
				{"other_region",new DataItem(){Title="片区",FldArea="divHead",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", CompareOperator="like",Checkboxes=true,
					SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by order_index , region_id "
				}},*/

                 // 如果tree_path 和key(other_region)不同，在检索语句出现的是jqxDropDownTree的value,如果相同出现的是jqxDropDownTree的treePath，设置CompareOperator为like配合path的部分检索
                {"other_region",new DataItem(){Title="片区",FldArea="divHead",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", CompareOperator="like",Checkboxes=true,
					SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by order_index , region_id "
				}},
				{"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'3',l:'预设进价'},{v:'2',l:'加权平均价'},{v:'1',l:'预设成本'},{v:'4',l:'最近平均进价'}]", CompareOperator="=" }},
				{"queryTimeAccord",new DataItem(){
					FldArea="divHead", Title="时间类型", LabelFld = "time_status_name", ButtonUsage = "list",
					CompareOperator="=",Value="byHappenTime",Label="交易时间",ForQuery=false, AutoRemember=true,
					Source = @"[
                                {v:'byHappenTime',l:'交易时间'},
                                {v:'byMakeTime',l:'制单时间'},
                                {v:'byApproveTime',l:'审核时间'},
                                {v:'byCheckedTime',l:'交账时间'},
                                {v:'bySendTime',l:'送货时间'},
                                {v:'byClearArrearsTime',l:'欠款结清时间'}
                               ]"
				}},
				{"senders_id",new DataItem(){FldArea="divHead",Title="送货员",Checkboxes=true, LabelFld="senders_name",ButtonUsage="list",SqlForOptions=CommonTool.selectSenders,CompareOperator="like"}},//SqlForOptions="select oper_id as v,oper_name as l,py_str as z from info_operator"
                {"saleWay",new DataItem(){FldArea="divHead",Title="销售方式",ForQuery=true,LabelFld="sale_ways",ButtonUsage="list",Value="all",Label="所有", AfterGroup=true,
                    Source = "[{v:'sale',l:'销售',condition:\"sum(case when sd.sub_amount<>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)<>'0'\"},{v:'return',l:'退货',condition:\"sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)<>'0'\"},{v:'free',l:'赠品',condition:\"sum(case when sd.sub_amount=0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)<>'0'\"},{v:'all',l:'所有',condition:'1=1'}]", CompareOperator="=",Checkboxes=true } },
                {"showRebateProfit",new DataItem(){FldArea="divHead",Title="显示补差后利润",CtrlType="jqxCheckBox",Hidden=true,ForQuery=false,Value="false"}},
                {"sheetType",new DataItem(){Title="",FldArea="divHead",Hidden=true,ForQuery=false,HideOnLoad = true} }
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true, Sortable=true,
                     PageByOverAgg = false,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"supcust_id",     new DataItem(){Title="客户",SqlAreaToPlace="", Sortable=true,SqlFld="", Width="150",Hidden=true}},
                       {"sup_name",     new DataItem(){Title="客户名称",SqlAreaToPlace="SALE",Pinned=true, Sortable=true, Linkable=true, Width="150"}},
                       {"mobile", new DataItem(){Title="客户电话", SqlAreaToPlace="SALE", Sortable=true, SqlFld="sc.mobile", Width="150",Hidden=true  }},
                       {"sup_addr", new DataItem(){Title="客户地址", SqlAreaToPlace="SALE", Sortable=true,  Width="150",Hidden=true }},

                       {"x_quantity_b",   new DataItem(){Title="销售量(大)",SqlAreaToPlace="SALE", Sortable=true, CellsAlign="right",  Width="150",SqlFld="round(sum(case when sd.inout_flag*quantity<0 then quantity*sd.unit_factor/b_unit_factor else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"t_quantity_b",   new DataItem(){Title="退货量(大)",SqlAreaToPlace="SALE", Sortable=true, CellsAlign="right",  Width="150",SqlFld="round(sum(case when sd.inout_flag*quantity>0 then quantity*sd.unit_factor/b_unit_factor else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"x_quantity_s",   new DataItem(){Title="销售量(小)",SqlAreaToPlace="SALE", Sortable=true, CellsAlign="right",  Width="150",SqlFld="round(sum(case when sd.inout_flag*quantity<0 then quantity*sd.unit_factor else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"t_quantity_s",   new DataItem(){Title="退货量(小)",SqlAreaToPlace="SALE", Sortable=true, CellsAlign="right",  Width="150",SqlFld="round(sum(case when sd.inout_flag*quantity>0 then quantity*sd.unit_factor else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"net_quantity", new DataItem(){Title="净销量",SqlAreaToPlace="SALE", Sortable=true,  CellsAlign="right",  Width="150",SqlFld="round(sum(quantity*sd.unit_factor*inout_flag*(-1))::numeric,2)",ShowSum=true,Hidden=true}},



                       
                       {"rebate_quantity",   new DataItem(){Title="补差数量", SqlAreaToPlace="SALE",Sortable=true,  CellsAlign="right",  Width="100",SqlFld="round(sum(case when coalesce(rebate_price, 0)<>0 then quantity*sd.unit_factor else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"weight", new DataItem(){Title="销售重量(kg)",SqlAreaToPlace="SALE", Sortable=true, CellsAlign="right", Width="10%", SqlFld="round(sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.inout_flag*(-1)*mu.weight else 0 end)::numeric,3)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }, ShowSum=true, Hidden=true,
                       } },
                       {"return_weight", new DataItem(){Title="退货重量(kg)",SqlAreaToPlace="SALE", Sortable=true, CellsAlign="right", Width="100", SqlFld="round(sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.inout_flag*mu.weight else 0 end)::numeric,3)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       } },
                       {"x_amount",     new DataItem(){Title="销售金额",SqlAreaToPlace="SALE", Sortable=true, CellsAlign="right",  Width="150",SqlFld="round(sum(case when sd.inout_flag*quantity<0 then inout_flag*(-1)*sub_amount else 0 end)::numeric,2)",ShowSum=true}},
                       {"t_amount",     new DataItem(){Title="退货金额",SqlAreaToPlace="SALE", Sortable=true, CellsAlign="right",  Width="150",SqlFld="round(sum(case when sd.quantity*sd.inout_flag>0 then sd.sub_amount*sd.inout_flag else 0 end)::numeric,2)",ShowSum=true}},
                       {"net_amount",   new DataItem(){Title="销售净额",SqlAreaToPlace="SALE", Sortable=true, CellsAlign="right",  Width="150",SqlFld="COALESCE(round(sum(inout_flag*(-1)*sub_amount)::numeric,2),0)",ShowSum=true}},
                       {"net_rebate_amount",   new DataItem(){Title="销售净额(补差后)",SqlAreaToPlace="SALE", Sortable=true,  Hidden = true, CellsAlign="right",  Width="150",SqlFld="COALESCE(round(sum(inout_flag*(-1)*sub_amount - coalesce(rebate_price, 0)*quantity*sd.unit_factor)::numeric,2),0)",ShowSum=true}},
                       {"now_disc_amount",  new DataItem(){Title="优惠金额",SqlAreaToPlace="SALE", Sortable=true, CellsAlign="right",  Width="150",SqlFld="round(sum(distinct (round(money_inout_flag*now_disc_amount::numeric,6)::text || sm.sheet_id::text||'1')::numeric),2)",ShowSum=true}},
					   {"disc_amount",  new DataItem(){Title="累计优惠",SqlAreaToPlace="SALE", Sortable=true,Hidden=true, CellsAlign="right",  Width="150",SqlFld="round(sum(distinct (round(money_inout_flag*disc_amount::numeric,6)::text || sm.sheet_id::text||'1')::numeric),2)",ShowSum=true}},

					   {"now_pay_amount",  new DataItem(){Title="实收金额",SqlAreaToPlace="SALE", Sortable=true, CellsAlign="right",  Width="150",SqlFld="round(sum(distinct (round(money_inout_flag*now_pay_amount::numeric,6)::text || sm.sheet_id::text||'1')::numeric),2)",ShowSum=true}},
					   {"paid_amount",  new DataItem(){Title="累计实收",SqlAreaToPlace="SALE", Sortable=true, Hidden=true, CellsAlign="right",  Width="150",SqlFld="round(sum(distinct (round(money_inout_flag*paid_amount::numeric,6)::text || sm.sheet_id::text||'1')::numeric),2)",ShowSum=true}},
					   {"now_left_amount",  new DataItem(){Title="欠款金额",SqlAreaToPlace="SALE", Sortable=true, CellsAlign="right",  Width="150",SqlFld="round(sum(distinct (round(money_inout_flag*(total_amount-now_pay_amount-now_disc_amount)::numeric,6)::text || sm.sheet_id::text||'1')::numeric),2)",ShowSum=true}},
					   {"left_amount",  new DataItem(){Title="尚欠金额",SqlAreaToPlace="SALE", Sortable=true,Hidden=true, CellsAlign="right",  Width="150",SqlFld="round(sum(distinct (round(money_inout_flag*(total_amount-paid_amount-disc_amount)::numeric,6)::text || sm.sheet_id::text||'1')::numeric),2)",ShowSum=true}},

                      
                         /*{"profit_cut_disc",       new DataItem(){Title="利润(减优惠)", CellsAlign="right",    Width="8%", Sortable=true,ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; }}},
                         {"profit_rate_cut_dis",new DataItem(){ Title = "利润率(减优惠)(%)", Sortable=true,CellsAlign = "right",Width = "7%",ShowAvg = true,
                             FuncGetSumValue = (sumColumnValues) =>
                             {
                                 string s_profit_hasfree =sumColumnValues["profit"];
                                 string s_net_amount =sumColumnValues["net_amount"];

                                 double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
                                 double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                                 string rate="";
                                 if (net_amount != 0)
                                 {
                                     rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
                                 }
                                 return rate;
                             }}
                          },*/


                         /*{"cost_amount_hasfree",  new DataItem(){Title="成本(含赠)", Sortable=true,  CellsAlign="right", Width="15%",SqlFld="",ShowSum=true}},
                         {"profit_hasfree",  new DataItem(){Title="利润(含赠)", Sortable=true,  CellsAlign="right", Width="15%",ShowSum=true}},
                         {"profit_rate_hasfree",new DataItem(){Title = "利润率(%)(含赠)", Sortable=true,CellsAlign = "right", Width = "7%",ShowAvg = true,
                         FuncGetSumValue = (sumColumnValues) =>
                             {
                                 string s_profit_hasfree =sumColumnValues["profit_hasfree"];
                                 string s_net_amount =sumColumnValues["net_amount"];

                                 double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
                                 double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                                 string rate="";
                                 if (net_amount != 0)
                                 {
                                     rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
                                 }
                                 return rate;
                             }
                         }},
                         {"free_cost_amount",new DataItem(){Title="赠品成本",CellsAlign="right",Width="8%",SqlFld="",ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; }} },
                         {"cost_amount",  new DataItem(){Title="成本", Sortable=true,  CellsAlign="right",  Width="15%",SqlFld="",ShowSum=true}},
                         {"profit",  new DataItem(){Title="利润", Sortable=true,  CellsAlign="right",  Width="15%",ShowSum=true}},
                         {"profit_rate",new DataItem(){Title = "利润率(%)", Sortable=true,CellsAlign = "right",Width = "7%",ShowAvg = true,
                         FuncGetSumValue = (sumColumnValues) =>
                             {
                                 string s_profit_hasfree =sumColumnValues["profit"];
                                 string s_net_amount =sumColumnValues["net_amount"];

                                 double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
                                 double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                                 string rate="";
                                 if (net_amount != 0)
                                 {
                                     rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
                                 }
                                 return rate;
                             }
                         }},*/
                       
                       {"fee_out_amount",   new DataItem(){Title="费用总额", Sortable=true, CellsAlign="right",  Width="100",ShowSum=true}},
                       {"fee_sale_rate", new DataItem(){
                           Title="费销比(%)", 
                           Sortable=true, 
                           CellsAlign="right",  
                           Width="100",
                           Hidden=true,
                           RelyColumns="fee_out_amount,net_amount",
                           FuncGetValueFromRowData=(colName, row) => {
                               string feeOutAmount = row["fee_out_amount"];
                               string netAmount = row["net_amount"];
                               
                               if (string.IsNullOrEmpty(feeOutAmount) || string.IsNullOrEmpty(netAmount)) {
                                   return "";
                               }
                               
                               decimal fee = CPubVars.ToDecimal(feeOutAmount);
                               decimal sales = CPubVars.ToDecimal(netAmount);
                               
                               if (sales == 0) {
                                   return "";
                               }
                               
                               decimal ratio = fee / sales * 100;
                               return CPubVars.FormatMoney(ratio, 2) + "%";
                           },
                           FuncGetSumValue = (sumColumnValues) => {
                               string totalFee = sumColumnValues["fee_out_amount"];
                               string totalSales = sumColumnValues["net_amount"];
                               
                               if (string.IsNullOrEmpty(totalFee) || string.IsNullOrEmpty(totalSales)) {
                                   return "";
                               }
                               
                               decimal fee = CPubVars.ToDecimal(totalFee);
                               decimal sales = CPubVars.ToDecimal(totalSales);
                               
                               if (sales == 0) {
                                   return "";
                               }
                               
                               decimal ratio = fee / sales * 100;
                               return CPubVars.FormatMoney(ratio, 2) + "%";
                           }
                       }},
                       {"fee_out_amount_cl",   new DataItem(){Title="陈列总额", Sortable=true, CellsAlign="right",  Width="100",ShowSum=true}},
                       {"fee_sale_rate_with_cl", new DataItem(){
                           Title="费销比(含陈列)(%)", 
                           Sortable=true, 
                           CellsAlign="right",  
                           Width="150",
                           Hidden=true,
                           RelyColumns="fee_out_amount,fee_out_amount_cl,net_amount",
                           FuncGetValueFromRowData=(colName, row) => {
                               string feeOutAmount = row["fee_out_amount"];
                               string feeOutAmountCl = row["fee_out_amount_cl"];
                               string netAmount = row["net_amount"];
                               
                               if (string.IsNullOrEmpty(feeOutAmount) || string.IsNullOrEmpty(netAmount) || string.IsNullOrEmpty(feeOutAmountCl)) {
                                   return "";
                               }
                               
                               decimal fee = CPubVars.ToDecimal(feeOutAmount);
                               decimal feeCl = CPubVars.ToDecimal(feeOutAmountCl);
                               decimal sales = CPubVars.ToDecimal(netAmount);
                               
                               if (sales == 0) {
                                   return "";
                               }
                               
                               decimal ratio = (fee + feeCl) / sales * 100;
                               return CPubVars.FormatMoney(ratio, 2) + "%";
                           },
                           FuncGetSumValue = (sumColumnValues) => {
                               string totalFee = sumColumnValues["fee_out_amount"];
                               string totalFeeCl = sumColumnValues["fee_out_amount_cl"];
                               string totalSales = sumColumnValues["net_amount"];
                               
                               if (string.IsNullOrEmpty(totalFee) || string.IsNullOrEmpty(totalSales) || string.IsNullOrEmpty(totalFeeCl)) {
                                   return "";
                               }
                               
                               decimal fee = CPubVars.ToDecimal(totalFee);
                               decimal feeCl = CPubVars.ToDecimal(totalFeeCl);
                               decimal sales = CPubVars.ToDecimal(totalSales);
                               
                               if (sales == 0) {
                                   return "";
                               }
                               
                               decimal ratio = (fee + feeCl) / sales * 100;
                               return CPubVars.FormatMoney(ratio, 2) + "%";
                           }
                       }},
                       {"income_amount",   new DataItem(){Title="其他收入总额", Sortable=true, CellsAlign="right",  Width="150",ShowSum=true}},
                       {"net_profit_hasfree",   new DataItem(){ProfitRelated=true, Title="纯利润(含赠)",SqlFld="sale_profit_hasfree + COALESCE(income_amount,0) - COALESCE(fee_out_amount,0)", Sortable=true, CellsAlign="right",  Width="200",ShowSum=true}},
                       {"net_profit",   new DataItem(){ProfitRelated=true, Title="纯利润",SqlFld="sale_profit + COALESCE(income_amount,0) - COALESCE(fee_out_amount,0)", Sortable=true,  CellsAlign="right", Width="80", ShowSum=true}},
                        { "preget_balance",new DataItem(){ Title="总预收款金额",SqlFld="ss.total_balance", Sortable=true,Hidden=true, CellsAlign="right",  Width="150",ShowSum=true} },
                        { "needget_money",new DataItem(){ Title="总应收款金额",SqlFld="ss.balance", Sortable=true,Hidden=true, CellsAlign="right",  Width="150",ShowSum=true} },
                        {"fee_profit_ratio", new DataItem(){
                            Title="费润比(%)", 
                            Sortable=true, 
                            CellsAlign="right",  
                            Width="100",
                            Hidden=true,
                            RelyColumns="fee_out_amount,profit",
                            FuncGetValueFromRowData=(colName, row) => {
                                string feeOutAmount = row["fee_out_amount"];
                                string profit = row["profit"];
                                
                                if (string.IsNullOrEmpty(feeOutAmount) || string.IsNullOrEmpty(profit)) {
                                    return "";
                                }
                                
                                decimal fee = CPubVars.ToDecimal(feeOutAmount);
                                decimal profitValue = CPubVars.ToDecimal(profit);
                                
                                if (profitValue == 0) {
                                    return "";
                                }
                                
                                decimal ratio = fee / profitValue * 100;
                                return CPubVars.FormatMoney(ratio, 2) + "%";
                            },
                            FuncGetSumValue = (sumColumnValues) => {
                                string totalFee = sumColumnValues["fee_out_amount"];
                                string totalProfit = sumColumnValues["profit"];
                                
                                if (string.IsNullOrEmpty(totalFee) || string.IsNullOrEmpty(totalProfit)) {
                                    return "";
                                }
                                
                                decimal fee = CPubVars.ToDecimal(totalFee);
                                decimal profitValue = CPubVars.ToDecimal(totalProfit);
                                
                                if (profitValue == 0) {
                                    return "";
                                }
                                
                                decimal ratio = fee / profitValue * 100;
                                return CPubVars.FormatMoney(ratio, 2) + "%";
                            }
                        }},
                     },
                     QueryFromSQL=@"

    FROM 
    (
        SELECT sm.supcust_id,~AREA_SALE,
          COALESCE (
          (
              round(sum(inout_flag*(-1)*sub_amount)::numeric,2)
              -sum(case when row_index = 1 then inout_flag*(-1)*coalesce(disc_amount,0) else 0 end)
              -round(sum(quantity*sd.unit_factor*inout_flag*(-1)*~VAR_cost_price_fld)::numeric,2)
              +round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*~VAR_cost_price_fld else 0 end))::numeric,2)
              ),0
          ) sale_profit,
          COALESCE 
          (
              round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-
              sum(case when row_index = 1 then inout_flag*(-1)*coalesce(disc_amount,0) else 0 end)-
              round(sum(quantity*sd.unit_factor*inout_flag*(-1)*~VAR_cost_price_fld)::numeric,2)
               ,0
           ) sale_profit_hasfree,total_balance,balance

        FROM  ~mainTable sm  
        LEFT JOIN ~detailTable sd on sd.sheet_id = sm.sheet_id and sd.company_id = ~COMPANY_ID
        LEFT JOIN info_item_prop ip on sd.item_id = ip.item_id and ip.company_id = ~COMPANY_ID
        LEFT JOIN info_supcust sc on sm.supcust_id = sc.supcust_id and sc.company_id = ~COMPANY_ID
        LEFT JOIN info_operator io on sm.seller_id = io.oper_id and io.company_id = ~COMPANY_ID
        LEFT JOIN info_item_multi_unit mu on mu.item_id = sd.item_id and unit_type = 's' and mu.company_id = ~COMPANY_ID
        LEFT JOIN info_item_brand b on b.brand_id = ip.item_brand and b.company_id = ~COMPANY_ID
        LEFT JOIN arrears_balance a on a.company_id=~COMPANY_ID and a.supcust_id=sm.supcust_id
        LEFT JOIN
        (
            
            select    pr.supcust_id,
                      sum(balance) as total_balance
            FROM
                     prepay_balance pr
            WHERE company_id=~COMPANY_ID
            GROUP BY
                     pr.supcust_id

        ) pb on sm.supcust_id = pb.supcust_id
        LEFT JOIN
        (
              SELECT us.item_id,us.unit_no s_unit_no, us.barcode s_barcode,
                                 ub.unit_no b_unit_no, ub.barcode b_barcode,  ub.unit_factor b_unit_factor,
                                 um.unit_no m_unit_no, um.barcode m_barcode,  um.unit_factor m_unit_factor
               FROM      info_item_multi_unit us
               LEFT JOIN info_item_multi_unit ub on us.item_id=ub.item_id and ub.unit_type='b' and ub.company_id=~COMPANY_ID  
               LEFT JOIN info_item_multi_unit um on us.item_id=um.item_id and um.unit_type='m' and um.company_id=~COMPANY_ID  
               WHERE us.company_id=~COMPANY_ID  and us.unit_type='s'
        ) t
        on sd.item_id=t.item_id
        where sm.company_id= ~COMPANY_ID and ~QUERY_CONDITION ~VAR_IS_DEL
        group by sm.supcust_id,sup_name,pb.total_balance,a.balance,sc.sup_addr,sc.mobile
    ) ss

    LEFT JOIN
    (
        SELECT a.supcust_id fee_supcust_id,
	        COALESCE (round( SUM (CASE WHEN sheet_type = 'ZC' THEN  fee_sub_amount ELSE 0 END) :: NUMERIC, 2 ) , 0 ) fee_out_amount ,
        COALESCE (round( SUM (CASE WHEN sheet_type = 'ZC'and sub_name like '%陈列%' THEN  fee_sub_amount ELSE 0 END) :: NUMERIC, 2 ) , 0 ) fee_out_amount_cl ,
	        COALESCE (round( SUM (CASE WHEN sheet_type = 'SR' THEN  fee_sub_amount ELSE 0 END) :: NUMERIC, 2 ) , 0 ) income_amount
        FROM
	        sheet_fee_out_main a
        LEFT JOIN
			sheet_fee_out_detail b on b.company_id=~COMPANY_ID and a.sheet_id=b.sheet_id
		LEFT JOIN
			cw_subject c on c.company_id=~COMPANY_ID and b.fee_sub_id=c.sub_id
        WHERE a.happen_time >= '~VAR_startDay' AND a.happen_time <='~VAR_endDay' AND a.company_id = ~COMPANY_ID AND red_flag IS NULL 
        GROUP BY a.supcust_id 
    ) fee ON ss.supcust_id = fee.fee_supcust_id

 
",
                     //QueryGroupBySQL = " group by sm.supcust_id,sup_name ,income_amount,fee_out_amount",
                     QueryOrderSQL=" order by sup_name"
                  }
                } 
            };
			var origCols = Grids["gridItems"].Columns;
			var cols = SalesSummaryByItemModel.GetProfitColumns(origCols,true, "SALE");
           
            foreach (var k in cols)
            {
                origCols.Add(k.Key, k.Value);
            }
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            
            SalesSummaryByItemModel.SetProfitColumns(this);
            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;

           
                  var cost_price_type = DataItems["cost_price_type"].Value;
                  var costPrice = "sd.cost_price_buy";
                  switch (cost_price_type)
                  {
                      case "3"://预设进价
                          costPrice = "sd.cost_price_buy";
                          break;
                      case "2"://加权价
                          costPrice = "sd.cost_price_avg";
                          break;
                      case "1"://预设成本
                          costPrice = "sd.cost_price_prop";
                          break;
                      case "4"://最近平均进价
                          costPrice = "sd.cost_price_recent";
                          break;
                  }
                  //Console.WriteLine("Client页面的OnQueryConditionGot方法获取的costPrice:" + costPrice);
                  var columns = Grids.GetValueOrDefault("gridItems").Columns;

            SQLVariables["cost_price_fld"] = costPrice;
            var sheetType = DataItems["sheetType"].Value;
            this.SQLVariables["IS_DEL"] = "";
            if(sheetType.ToLower() == "xd")
            {
                this.SQLVariables["IS_DEL"] = "and coalesce(sm.is_del, false) = false";
            }
	 

            /*
            columns["profit_cut_disc"].SqlFld = $"round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-sum(case when row_index = 1 then inout_flag*(-1)*coalesce(disc_amount,0) else 0 end)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2)+round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2)";
                  columns["profit_rate_cut_dis"].SqlFld = @$"
      round ( (
          (
              (
                  round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) - SUM ( CASE WHEN row_index = 1 THEN inout_flag * ( - 1 ) * COALESCE ( disc_amount, 0 ) ELSE 0 END ) - round( SUM ( quantity * sd.unit_factor * inout_flag * ( - 1 ) *{ costPrice } ) :: NUMERIC, 2 ) + round(
              SUM ( ( CASE WHEN sub_amount = 0 AND trade_type != 'J' THEN - quantity * sd.unit_factor * inout_flag *{ costPrice } ELSE 0 END ) ) :: NUMERIC,
          2 
      ) 
      ) * 100 
      ) / (
          CASE

                  WHEN round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) <> 0 THEN
                  round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) ELSE NULL 
              END 
              ) :: NUMERIC
          ):: NUMERIC, 2)


      "

                  ;




                  columns["cost_amount_hasfree"].SqlFld = $"round( SUM ( CASE WHEN trade_type !='J' THEN quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} ELSE 0 END ) :: NUMERIC, 2 ) ";
                  columns["profit_hasfree"].SqlFld = $"round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-sum(case when row_index = 1 then inout_flag*(-1)*coalesce(disc_amount,0) else 0 end)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2)";
                  columns["profit_rate_hasfree"].SqlFld = @$"
      round ( (
          (
              (round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-sum(case when row_index = 1 then inout_flag*(-1)*coalesce(disc_amount,0) else 0 end)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2)	)*100
      )
      /
          (
              case when round(sum(sub_amount*inout_flag*(-1))::numeric,2) <>0 
                   then round(sum(sub_amount*inout_flag*(-1))::numeric,2)
                          else null 
              end
              ) :: NUMERIC
          ):: NUMERIC, 2)

      ";
                  columns["free_cost_amount"].SqlFld = $"round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2) ";
                  columns["cost_amount"].SqlFld = $"round( SUM ( CASE WHEN trade_type !='J' THEN quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} ELSE 0 END ) :: NUMERIC, 2 )-round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2)";
                  columns["profit"].SqlFld = $"round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-sum(case when row_index = 1 then inout_flag*(-1)*coalesce(disc_amount,0) else 0 end)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2)+round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2)";
                  columns["profit_rate"].SqlFld = @$"
      round ( (
          (
              (round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-sum(case when row_index = 1 then inout_flag*(-1)*coalesce(disc_amount,0) else 0 end)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2)+round(sum((case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end))::numeric,2))*100
          ) /
          (
              case when round(sum(inout_flag*(-1)*sub_amount)::numeric,2) <>0 
                   then round(sum(inout_flag*(-1)*sub_amount)::numeric,2) 
                          else null 
              end
              ) :: NUMERIC
          ):: NUMERIC, 2)

      ";

                 */

        }



        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
          SalesSummaryByItemModel.SetCostInfo(this); 
          

        }
     
        public async Task OnGet()
        { 
            await InitGet(cmd);
        }
 
    }

    [Route("api/[controller]/[action]")]
    public class SalesSummaryByClientController : QueryController
    { 
        public SalesSummaryByClientController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SalesSummaryByClientModel model = new SalesSummaryByClientModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
        /*这样写不够通用
        [HttpGet]
        public async Task<object> GetQueryRecords(string cost_price_type_name)
        {
            SalesSummaryByClientModel model = new SalesSummaryByClientModel(cmd);
            var columns = model.Grids.GetValueOrDefault("gridItems").Columns;
            var cost_price = columns.GetValueOrDefault("cost_price");
            var profit = columns.GetValueOrDefault("profit");

            if (cost_price_type_name == "加权平均成本")
            {
                cost_price.SqlFld = "round(sum(quantity*sd.unit_factor*money_inout_flag*sd.cost_price_avg)::numeric,2)";
                profit.SqlFld = "round((sum(sub_amount*money_inout_flag)-sum(quantity*sd.unit_factor*money_inout_flag*sd.cost_price_avg))::numeric,2)";
            }
            else if (cost_price_type_name == "进价")
            {
                cost_price.SqlFld = "round(sum(quantity*sd.unit_factor*money_inout_flag*mu.buy_price)::numeric,2)";
                profit.SqlFld = "round((sum(sub_amount*money_inout_flag)- COALESCE(sum(quantity*sd.unit_factor*money_inout_flag*mu.buy_price),0))::numeric,2)";
            }
            else
            {
                cost_price.SqlFld = "round(sum(quantity*sd.unit_factor*money_inout_flag*ip.cost_price_spec)::numeric,2) ";
                profit.SqlFld = "round((sum(money_inout_flag*sub_amount)-sum(quantity*sd.unit_factor*money_inout_flag*ip.cost_price_spec))::numeric,2)";
            }

            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }
        */


        [HttpPost]
        public async Task<object> GetQueryRecords([FromBody] dynamic data)
        {
            var main_table = "sheet_sale_main";
            var detail_table = "sheet_sale_detail";

            string sheetType = data.sheetType;

            if (sheetType == "xd")
            {
                main_table = "sheet_sale_order_main";
                detail_table = "sheet_sale_order_detail";
            }

            SalesSummaryByClientModel model = new SalesSummaryByClientModel(cmd);
            var sql = model.Grids["gridItems"].QueryFromSQL;
            sql = sql.Replace("~mainTable", main_table);
            sql = sql.Replace("~detailTable", detail_table);
            model.Grids["gridItems"].QueryFromSQL = sql;
            object records = await model.GetRecordFromQuerySQL(Request, cmd, data);
            return records;
        }


        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            string sParams = Request.Form["params"];
            sParams = System.Web.HttpUtility.UrlDecode(sParams);
            dynamic queryParams = JsonConvert.DeserializeObject(sParams);
            string sheetType = queryParams.sheetType;

            var main_table = "sheet_sale_main";
            var detail_table = "sheet_sale_detail";
            if (sheetType == "xd")
            {
                main_table = "sheet_sale_order_main";
                detail_table = "sheet_sale_order_detail";
            }

            SalesSummaryByClientModel model = new SalesSummaryByClientModel(cmd);
            var sql = model.Grids["gridItems"].QueryFromSQL;
            sql = sql.Replace("~mainTable", main_table);
            sql = sql.Replace("~detailTable", detail_table);
            model.Grids["gridItems"].QueryFromSQL = sql;
            return await model.ExportExcel(Request, cmd, queryParams);
            
        }
    }
}
