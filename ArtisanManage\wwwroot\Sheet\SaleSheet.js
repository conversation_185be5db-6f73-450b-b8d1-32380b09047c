﻿function onPageReady(sheetRows) {
     console.log(window.g_queriedItems)
     mmSheetInit();

     window.addEventListener('message', function (rs) {
         if (rs.data.msgHead === "SaleFeeApportionSheet") {//费用分摊
             if (rs.data.action === "apportion") {
                 console.log('sale sheet apportion fee OK');
                 if (rs.data.saleFeeAppoSheet.sheetRowSaleFeeAppoList.length > 0 && toMoney(rs.data.saleFeeAppoSheet.total_fee_amount) > 0) {
                     $('#saleFeeSheetInfo').val(JSON.stringify(rs.data.saleFeeAppoSheet));
                 } else {
                     $('#saleFeeSheetInfo').val('');
                 }
                 $('#fee_btn').text(`销售费用：${rs.data.saleFeeAppoSheet.total_fee_amount}`);
                 updateCostAmount()
             } else if (rs.data.action === "cancel") {
                 console.log('sale sheet apportion fee cancel');
             }
             $('#popFee').jqxWindow('hide');
         }
         else if (rs.data.msgHead === "AppendixPhotoEdit") {
             // 附件
             if (rs.data.action === "close") {
                 appendixPhotoList = rs.data.appendix
                 window.appendixSrcList = rs.data.src
                 console.log("appendixSaved:" + appendixPhotoList)
                 $("#popAppendix").css('display', 'none')
                 $('#popAppendix').jqxWindow('closeWindow');
             }
         }

     });
    function renderCopySheet() {
        if (requestString('copy')) {
            var params = paramsFromSrcWindow
            if ((params.sheet_type).indexOf('BUY') != -1) params.supcust_id = "";
 
            GetRowsByCopyFromSheets(params)
        }
    }
       
    document.all.ckPrintSmallBarcode.checked = true
    if (window.g_companySetting) {
        if (window.g_companySetting.sheetShowBarcodeStyle != '2') {
            document.all.ckPrintSmallBarcode.checked = false
        }
    }

    var theme = "";
    var datafields = [];
    var data = new Array; var rowscount = 10;

    var approve_time = $('#approve_time').text();
    /*
    var canSave = window.getRightValue('sale.sheetSale.save')
    var canApprove = window.getRightValue('sale.sheetSale.approve')
    var canReview = window.getRightValue('sale.sheetSale.review')
    var canRed = window.getRightValue('sale.sheetSale.red')

    if (canMake != "true") $('#btnSave').css('display', 'none');
    if (canApprove != "true") $('#btnApprove').css('display', 'none');
    if (canReview != "true") $('#btnReview').css('display', 'none');
    if (canRed != "true") $('#btnRed').css('display', 'none');
    if (canRed != "true") $('#btnRedAndChange').css('display', 'none');
    */
    
    if (approve_time) {
        var red_flag = $('#red_flag').val()
        if (red_flag) {
            $("#btnRedAndChange").attr('disabled', true);
        }
    } else {
        $("#btnRedAndChange").attr('disabled', true);
    }

    var source =
    {
        sort: funcSortByColumn,
        localdata: data,
        unboundmode: true,
        totalrecords: 10,
        datafields: datafields,
        updaterow: function (rowid, rowdata) {
        }
    }
    window.g_gridSource=source
    var dataAdapter = new $.jqx.dataAdapter(source);

    var fixColCss = 'jqx-widget-header';
    if (theme !== '') fixColCss += ' jqx-widget-header-' + theme;


    var canSeeBuyPrice = false;
    var canSeeProfit = true;
    var canSeeSalePrice = true;

    var saleRememberBranch = true;
    var saleAndOrderRemeberSeller = true;
     if (window.g_operRights.delicacy) {
         if (   window.g_operRights.delicacy.seeInPrice
             && window.g_operRights.delicacy.seeInPrice.value) {
             canSeeBuyPrice = true;
         }
         if (   window.g_operRights.delicacy.seeProfit
             && window.g_operRights.delicacy.seeProfit.value) {
             canSeeProfit = true;
         }
         if (window.g_operRights.delicacy.seeSalePrice && !window.g_operRights.delicacy.seeSalePrice.value) {
             canSeeSalePrice = false;
         }
         if (window.g_operRights.delicacy.saleRememberBranch && !window.g_operRights.delicacy.saleRememberBranch.value) {
             saleRememberBranch = false;
         }
         if (window.g_operRights.delicacy.saleAndOrderRemeberSeller && !window.g_operRights.delicacy.saleAndOrderRemeberSeller.value) {
             saleAndOrderRemeberSeller = false;
         }
    }

    var sheetShowPriceList = true
    if (window.g_companySetting && window.g_companySetting.sheetShowPriceList && window.g_companySetting.sheetShowPriceList.toLowerCase() == 'false')
        sheetShowPriceList = false;

    window.GridData = {
        source: dataAdapter,
        showaggregates: true,
        showstatusbar: true,
        columnsheight:36,
        rowsheight:36,
        statusbarheight: 30,
        pageable: false,
        // autoheight: true,
        // sortable: true,sortmode:'many',
        editable: true,
        columnsresize: true,
        ready: function() {
            $("#jqxgrid").jqxGrid('focus');
        },
        renderstatusbar: function(statusbar) {
        },
        editmode: 'click', // 'selectedcell',
        selectionmode: 'multiplecellsadvanced', //'singlecell',// 'multiplecellsadvanced',
        hoverrow: true,
        theme: theme,
        sortable: true,
        cellhover: cellhover,
        handlekeyboardnavigation: handlekeyboardnavigation,
        columns: [
            {
                text: '',
                sortable: false,
                filterable: false,
                editable: false,
                pinned: true,
                groupable: false,
                draggable: false,
                resizable: false,
                datafield: '',
                columntype: 'number',
                width: 45,
                cellclassname: fixColCss,
                cellsrenderer: pinCellsRenderer,
                renderer: leftTopCellRenderer
            },

            {
                text: '商品名称',
                sortable: true,
                datafield: 'item_id',
                displayfield: 'item_name',
                width: '200', alwaysShow: true, align: 'center',
                columntype: 'template',
                createeditor: createeditor_item_name,
                initeditor: initeditor_item_name,
                geteditorvalue: function(row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                },
                cellsrenderer: function(row, column, value, p4, p5, rowData) {
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    var trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
                    var order_item_sheets_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_item_sheets_id');
                    var order_item_sheets_no = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_item_sheets_no');
                    if (order_item_sheets_id && order_item_sheets_no) {
                        order_item_sheets_id = order_item_sheets_id.replace(',,', ',')
                        order_item_sheets_no = order_item_sheets_no.replace(',,', ',')
                        var arr = order_item_sheets_no.split(',')
                        order_item_sheets_no=''
                        arr.forEach(sheet_no => {
                            if (sheet_no) {
                                if (order_item_sheets_no) order_item_sheets_no += ','
                                order_item_sheets_no += sheet_no
                            } 
                        })
                    }
                    var attrOptNames=''
                    if (rowData.attr_qty) {
                        if (!Array.isArray(rowData.attr_qty)) 
                           rowData.attr_qty = JSON.parse(rowData.attr_qty)
                       
                        rowData.attr_qty.forEach(opt => {
                           
                            var optName =''
                            for (var propName in opt) {
                                if (propName.indexOf('optName_') == 0) {
                                    var optName = opt[propName]
                                    break
                                }
                            }
                            if (rowData.attr_qty.length > 1) {
                                optName+='(' + opt.qty + ')'
                            }
                            attrOptNames += optName

                        })
                    }
                    var lblAttrOpt = ''
                    if (attrOptNames) {
                        lblAttrOpt = 
                            `<label style ="margin-left:10px;background:white;color:#333388;border-radius:5px;font-size:14px;text-align:center;line-height:20px" >${attrOptNames}</label>`;
                    
                    }
                    

                    var label = '';
                    if (order_sub_id) {
                        label =
                            `<label style ="margin-left:4px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:17px" >定 </label>`;
                        label += `<label style="color:#88f;cursor:pointer;">${order_item_sheets_no} </label>`
                    }
                   
                    if (disp_flow_id)
                        label =
                            '<label style ="margin-left:4px;background:#e6214a;color:white;border-radius:5px;width:30px;font-size:10px;text-align:center;line-height:17px" >陈列</label>';
                    if (trade_type === 'J')
                        label =
                            '<label style ="margin-left:4px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:17px" >借</label>';
                    if (trade_type === 'H')
                        label =
                            '<label style ="margin-left:4px;background:#e6214a;color:white;border-radius:5px;width:16px;font-size:10px;text-align:center;line-height:17px" >还</label>';
                    if (order_sub_id || disp_flow_id || trade_type || attrOptNames) {
                        var div =
                            `<div style = "height:100%;display:flex;align-items:center;"><label style="text-align:left;margin-left:4px">${
                            value}</label>${lblAttrOpt}${label}</div>`
                        return div;

                    }
                },
            },

            //{
            //    text: '客户编号',
            //    datafield: 'supcust_id',
            //    width: '70', align: 'center', cellsalign: 'center', hidden: false,
            //    cellbeginedit: function (row, datafield, columntype, value) {
            //        return false;
            //    }
            //},

            {
                text: '商品编号', datafield: 'item_no', width: '70', align: 'center', cellsalign: 'center', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },

            {
                text: '仓库',
                hidden: true,
                datafield: 'branch_id',
                displayfield: 'branch_name',
                width: 150, align: 'center',
                columntype: 'template',
                createeditor: createeditor_branch_name,
                initeditor: initeditor_branch_name,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if(!item_id) return false
                },
            },
            {
                text: '库位',
                sortable: true,
                hidden: true,
                datafield: 'branch_position',
                displayfield: 'branch_position_name',
                width: 150, align: 'center',
                columntype: 'template',
                createeditor: createeditor_branch_position_name,
                initeditor: initeditor_branch_position_name,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var row_branch_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'branch_id');
                    var branch_id = $('#branch_id').jqxInput('val').value
                    if ((!row_branch_id && !branch_id) || !item_id) return false;
                },
            },
            {
                text: '规格',
                datafield: 'item_spec',
                width: '60',
                align: 'center',
                cellsalign: 'center',
                hidden: true
            },
            {
                text: '别名',
                datafield: 'item_alias',
                width: '60',
                align: 'center',
                cellsalign: 'center',
                hidden: true
            },
            {
                text: '商品备注',
                datafield: 'item_remark',
                width: '60',
                align: 'center',
                cellsalign: 'center',
                hidden: true
            },
            {
                text: '商品类别编号',
                datafield: 'other_class',
                width: '60',
                align: 'center',
                cellsalign: 'center',
                hidden: true
            },
            {
                text: '品牌编号',
                datafield: 'brand_id',
                width: '60',
                align: 'center',
                cellsalign: 'center',
                hidden: true
            },
            {
                text: '品牌',
                datafield: 'brand_name',
                width: '60',
                align: 'center',
                cellsalign: 'center',
                hidden: true
            },
            {
                text: '保质期',
                datafield: 'valid_days',
                width: '80',
                align: 'center',
                cellsalign: 'right',
                hidden: true
            },
            {
                text: '单位',
                datafield: 'unit_no',
                width: '80',
                align: 'center',
                cellsalign: 'center',
                columntype: 'template',
                alwaysShow: false,
                cellbeginedit: function(row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    if ((datafield === "unit_no" && !item_id) || disp_flow_id) return false;
                },
                createeditor: createeditor_unit_no,
                initeditor: initeditor_unit_no,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                }

            },
            {
                text: '包装率', datafield: 'unit_factor', width: '80', align: 'center', cellsalign: 'center', alwaysShow: false,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '单位关系', datafield: 'unit_relation1', width: '100', align: 'center', cellsalign: 'center', alwaysShow: false, hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '条码',
                datafield: 'barcode',
                width: '150',
                align: 'center',
                cellsalign: 'right',
                cellbeginedit: function(row, datafield, columntype, value) {
                    return true;
                },
                cellsrenderer: function (row, column, value, p4, p5, rowData) {
                    let attrOptNames = '';
                    rowData.barcode = rowData.barcode || ""
                    if (rowData.barcode.indexOf("主商品(") != -1) {
                        rowData.barcode = rowData.barcode.substring(rowData.barcode.indexOf("主商品(")+4, rowData.barcode.indexOf(")"))
                    }
                    if (isNaN(rowData.barcode)) {
                        rowData.barcode = ""
                    }
                    if (rowData.attr_qty && !rowData.son_mum_item) {
                        if (rowData.barcode != ""&&rowData.attr_qty.length!=0 ) {
                            rowData.barcode = `主商品(${rowData.barcode})`
                        }
                        if (typeof rowData.attr_qty === 'string') {
                            rowData.attr_qty = JSON.parse(rowData.attr_qty);
                        }
                        rowData.attr_qty.forEach(opt => {
                            for (const propName in opt) {
                                if (propName.startsWith('optName_') && Number(opt.qty) !== 0) {
                                    switch (rowData.unit_no) {
                                        case rowData.b_unit_no:
                                            code = opt.bBarcode;
                                            break;
                                        case rowData.m_unit_no:
                                            code = opt.mBarcode;
                                            break;
                                        case rowData.s_unit_no:
                                            code = opt.sBarcode;
                                            break;
                                    }
                                    if(code)
                                        attrOptNames += `${opt[propName]}(${code})`;
                                    break;
                                }
                            }
                        });
                           rowData.barcode += attrOptNames;
                    }
                   
                    const lblAttrOpt = attrOptNames ?
                        `<label style="margin-left:10px;background:white;color:#333388;border-radius:5px;font-size:14px;text-align:center;line-height:20px">${attrOptNames}</label>` :
                        '';
                    const div = `
    <div style="height:100%;display:flex;align-items:center;">
        <label style="text-align:left;margin-left:4px">${rowData.barcode}</label>
        
    </div>
`;
                    return div;
                }
            },
            {
                text: '条码(小)', datafield: 's_barcode', width: '150', align: 'center', cellsalign: 'center', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return true;
                },
                cellsrenderer: function(row, column, value, p4, p5, rowData) {
                    let attrOptNames = '';
                    rowData.s_barcode = rowData.s_barcode || ""
                    if (rowData.s_barcode.indexOf("主商品(") != -1) {
                        rowData.s_barcode = rowData.s_barcode.substring(rowData.s_barcode.indexOf("主商品(") + 4, rowData.s_barcode.indexOf(")"))
                    }
                    if (isNaN(rowData.s_barcode)) {
                        rowData.s_barcode=""
                    }
                    if (rowData.attr_qty&&!rowData.son_mum_item) {
                        if (rowData.s_barcode != "" && rowData.attr_qty.length != 0) {
                            rowData.s_barcode = `主商品(${rowData.s_barcode})`
                        }
                        if (typeof rowData.attr_qty === 'string') {
                            rowData.attr_qty = JSON.parse(rowData.attr_qty);
                        }
                        rowData.attr_qty.forEach(opt => {
                            for (const propName in opt) {
                                if (propName.startsWith('optName_') && Number(opt.qty) !== 0) {
                                    if (opt.sBarcode)
                                        attrOptNames += `${opt[propName]}(${opt.sBarcode})`;
                                    break;
                                }
                            }
                        });
                            rowData.s_barcode += attrOptNames;
                    }
                    const lblAttrOpt = attrOptNames ?
                        `<label style="margin-left:10px;background:white;color:#333388;border-radius:5px;font-size:14px;text-align:center;line-height:20px">${attrOptNames}</label>` :
                        '';
                    const div = `
    <div style="height:100%;display:flex;align-items:center;">
        <label style="text-align:left;margin-left:4px">${rowData.s_barcode}</label>
    </div>
`;
                    return div;
                }
            },
            {
                text: '虚拟产期',
                sortable: false,
                datafield: 'virtual_produce_date',
                width: '100px',
                align: 'center',
                hidden: true,
                cellsalign: 'center',
                cellbeginedit: function (row, datafield, columntype, value, c, d, e) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (datafield === "virtual_produce_date" && !item_id) return false;
                }
            },
            {
                text: '生产日期', datafield: 'batch_id', displayfield: "produce_date", width: '150', align: 'center', cellsalign: 'center', columntype: 'template', hidden: true,
                createeditor: createeditor_produce_date,
                initeditor: initeditor_produce_date,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val()
                    return v;
                },
                cellsrenderer: function (row, columnfield, value) {
                    var rowsData = $("#jqxgrid").jqxGrid('getrows')
                    var rowData = rowsData[row]
                    if (rowData.batch_level == "" || rowData.batch_level === "0") value = "无产期"
                    return '<div style="line-height:37px;text-align:center;">' + value + '</div>';
                },
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var rowsData = $("#jqxgrid").jqxGrid('getrows')
                    var rowData = rowsData[row]
                    if ((datafield === "batch_id" && !item_id) || rowData.batch_level == "" || rowData.batch_level === "0") return false;
                },
            },
            {
                text: '批次', datafield: 'batch_id_f', displayfield: 'batch_no', width: '60', align: 'center', cellsalign: 'center', columntype: 'template', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var rowsData = $("#jqxgrid").jqxGrid('getrows')
                    var rowData = rowsData[row]
                    if ((datafield === "batch_id_f" && !item_id) || rowData.batch_level !== "2") return false;
                },
                createeditor: createeditor_batch_no,
                initeditor: initeditor_batch_no,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val(); return v;
                }

            },
            {
                text: '交易类型', datafield: 'trade_type', displayfield: 'trade_type_name', width: '100', align: 'center', columntype: 'template', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id'); 
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id'); 
                    if ((datafield === "trade_type" && !item_id) || disp_flow_id || order_sub_id) return false;
                },
                createeditor: createeditor_trade_type,
                initeditor: initeditor_trade_type,
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    return v;
                }
            },

            {
                text: '数量',
                datafield: 'quantity',
                width: '80',
                alwaysShow: false,
                cellsrenderer: cellsrenderer_quantity,
                align: 'center',
                cellsalign: 'right',
                aggregates: aggregates_quantity,
                aggregatesrenderer: aggregatesrenderer_quantity,
                geteditorvalue: geteditorvalue_quantity,
                cellbeginedit: function (row, datafield, columntype, value) {
                    
                    var gridRow = $('#jqxgrid').jqxGrid('getrows')[row]
                    var item_id = gridRow.item_id 
                    if (!item_id) return false
                    if (gridRow.mum_attributes) {
                        if (typeof gridRow.mum_attributes === "string") {
                            gridRow.mum_attributes = JSON.parse(gridRow.mum_attributes)
                        }
                        if (gridRow.mum_attributes.find(attr => attr.distinctStock))
                            return false
                        if (gridRow.mum_attributes.find(attr => !attr.distinctStock)) {
                            if (gridRow.attr_qty && gridRow.attr_qty.length > 0) {
                                return false
                            }
                        } 
                    }  
                    
                }
            },
            {
                text: '辅助数量',
                datafield: 'quantity_unit_conv',
                width: '100',
                hidden:true,
                cellsrenderer: cellsrenderer_multi_qty,
                columntype: 'template',
                align: 'center',
                cellsalign: 'right',
                //aggregates: aggregates_quantity,
                //aggregatesrenderer: aggregatesrenderer_quantity,
                createeditor: createeditor_multi_qty,
                initeditor: initeditor_multi_qty,
                geteditorvalue: geteditorvalue_multi_qty,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var gridRow = $('#jqxgrid').jqxGrid('getrows')[row]
                    var item_id = gridRow.item_id
                    if (!item_id) return false
                    if (gridRow.mum_attributes) {
                        if (gridRow.mum_attributes.find(attr => attr.distinctStock))
                            return false
                        if (gridRow.mum_attributes.find(attr => !attr.distinctStock)) {
                            if (gridRow.attr_qty && gridRow.attr_qty.length > 0) {
                                return false
                            }
                        }
                    }

                }
            },

            {
                text: '原价',
                datafield: 'orig_price',
                width: '70',
                align: 'center',
                hidden: !canSeeSalePrice,
                hideOnLoad: !canSeeSalePrice,
                cellsalign: 'right',
                cellbeginedit: function(row, datafield, columntype, value) {
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false;
                    //else if (order_sub_id) return false;
                }
            },
            {
                text: '原价金额', datafield: 'orig_amount', width: '100', align: 'center', cellsalign: 'right',  hidden: true,
                hideOnLoad: !canSeeSalePrice,
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount
            },

            {
                text: '系统售价',
                datafield: 'sys_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function(row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '上次售价',
                datafield: 'last_time_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '折扣(%)',
                datafield: 'discount',
                width: '100',
                align: 'cnter',
                cellsalign: 'center',
                cellbeginedit: function(row, datafield, columntype, value) {
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var discount = $('#jqxgrid').jqxGrid('getcellvalue', row, 'discount');
                    if (!item_id) return false;
                    if (order_sub_id) return false;
                    if (discount == "NAN" || discount == "Infinity") discount = null;
                }
            },
            {
                text: '价格',
                datafield: 'real_price',
                width: '90',
                align: 'center',
                cellsalign: 'right',
                columntype: sheetShowPriceList ? 'template' : undefined,
                createeditor: sheetShowPriceList ? createeditor_real_price : undefined,
                initeditor: sheetShowPriceList ? initeditor_real_price : undefined,
                geteditorvalue: sheetShowPriceList ? function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    if (v.label && !v.value) {
                        v.value = toMoney(v.label.toString(),4,2)
                    }

                    return v;
                } : undefined,
                alwaysShow: canSeeSalePrice,
                hidden: !canSeeSalePrice,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function(row, datafield, columntype, value) {
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
                    if (!item_id) return false;
                    //if (order_sub_id || disp_flow_id || (trade_type) return false;
                    if (order_sub_id || disp_flow_id || (trade_type === 'J' || trade_type === 'H')) return false;


                },
               
                cellsrenderer: function(row, column, value, p4, p5, rowData) {
                    var sys_price = rowData.sys_price;
                    var real_price = rowData.real_price;
                    if (rowData.item_id) {
                        if (toMoney(sys_price || 0) != toMoney(real_price || 0)) {
                            var div =
                                `<div onmouseenter='onMouseEnterRealPrice(event,${row},${sys_price
                                })' onmousedown='onMouseLeaveRealPrice()' onmouseleave='onMouseLeaveRealPrice()'  style = "height:100%;margin-right:4px;color:#f00;display:flex;align-items:center;justify-content:flex-end;">${toMoneyText(value, 4, 2)}</div>`
                            return div;
                        }
                         
                    }
                    return undefined

                },

            },
            {
                text: '小单位价',
                datafield: 's_real_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    //var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    //if (!item_id) return false;
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
                    if (!item_id) return false;
                    //if (order_sub_id || disp_flow_id || (trade_type) return false;
                    if (order_sub_id || disp_flow_id || (trade_type === 'J' || trade_type === 'H')) return false;
                
                },
                cellsrenderer:price_renderer
         
            },
            {
                text: '中单位价',
                datafield: 'm_real_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var rows = $('#jqxgrid').jqxGrid('getrows');
                    var curRow = rows[row]
                    if (!curRow.item_id) return false
                    if (!curRow.m_unit_no) return false
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
                    if (!item_id) return false;
                    //if (order_sub_id || disp_flow_id || (trade_type) return false;
                    if (order_sub_id || disp_flow_id || (trade_type === 'J' || trade_type === 'H')) return false;

                },
                cellsrenderer: money_renderer

            },
            {
                text: '大单位价',
                datafield: 'b_real_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    var rows = $('#jqxgrid').jqxGrid('getrows');
                    var curRow=rows[row] 
                    if (!curRow.item_id) return false 
                    if (!curRow.b_unit_no) return false
                    var order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    var disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    var trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
                    if (!item_id) return false;
                    //if (order_sub_id || disp_flow_id || (trade_type) return false;
                    if (order_sub_id || disp_flow_id || (trade_type === 'J' || trade_type === 'H')) return false;
                },
                cellsrenderer: money_renderer


            },
            {
                text: '分摊金额', 
                datafield: 'allocate_amount', 
                width: '90', 
                align: 'center', 
                cellsalign: 'right', 
                hidden: true,
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount,
                cellbeginedit: function (row, datafield, columntype, value, d, c) {
                    let item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (item_id) return true;
                    else return false;
                },
                cellsrenderer: money_renderer

            },
            {
                text: '金额',
                datafield: 'sub_amount',
                width: '90',
                align: 'center',
                cellsalign: 'right',
                alwaysShow: canSeeSalePrice,
                hidden: !canSeeSalePrice,
                hideOnLoad: !canSeeSalePrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    
                    let order_sub_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'order_sub_id');
                    let disp_flow_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'disp_flow_id');
                    let trade_type = $('#jqxgrid').jqxGrid('getcellvalue', row, 'trade_type');
                    let item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false;
                    if (order_sub_id || disp_flow_id || (trade_type === 'J' || trade_type === 'H')) return false;
                },
                cellsrenderer: money_renderer,

                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sale_sub_amount
            },
            {
                text: '供应商', datafield: 'supplier_id', displayfield: 'supplier_name', width: '80', hidden: true, align: 'center', cellsalign: 'center',
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },

            {
                text: '生产商', columntype: 'template', datafield: 'manufactor_id', displayfield: 'manufactor_name', width: '70', hidden: true, sortable: false, align: 'center',
                createeditor: function (row, cellvalue, editor, cellText, width, height) {
                    var element = $('<div></div >');
                    editor.append(element);
                   // var supplier_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'supplier_id');
                    var inputElement = editor.find('div')[0];
                    var datafields = new Array({ datafield: 'manufactor_name', displayfield: 'manufactor_id', text: '名称', width: width });
                    //$(inputElement).jqxInput({
                    //     height: height, width: width,
                    //    borderShape: "none",
                    //    buttonUsage: 'list',
                    //    checkboxes: false,
                    //    showHeader: true,
                    //    dropDownHeight: 260,
                    //    displayMember: "manufactor_name",
                    //    valueMember: "manufactor_id",
                    //    dataFields: datafields,
                    //    searchFields: ["manufactor_id", "manufactor_name"],
                    //    searchMode: 'none',
                    //    maxRecords: 9,
                    //    url: `/api/SaleSheet/GetSheetManufactor?sheetType=X&supplier_name=${supplier_name}&operKey=` + window.g_operKey,
                    //})
                    $(inputElement).jqxInput({
                        height: height, width: width,
                        borderShape: 'none',
                        buttonUsage: 'list',
                        dropDownHeight: 160,
                        dropDownWidth: width,
                        keepNoValueLabel: true,
                        displayMember: 'manufactor_name',
                        valueMember: 'manufactor_id',
                        datafields: datafields,
                        searchFields: ['manufactor_name'],
                        maxRecords: 9,
                        searchMode: 'none',
                        source: function (query, response) {
                            
                            var supplier_id = $('#jqxgrid').jqxGrid('getcellvalue', row, "supplier_id");
                            if (supplier_id) {
                                $.ajax({
                                    url: '/api/SaleSheet/GetSheetManufactor',
                                    type: 'GET',
                                    contentType: 'application/json',
                                    data: {
                                        operKey: g_operKey,
                                        supplier_id: supplier_id
                                    },
                                    success: function (data) {
                                        // if (data.result == "OK") {
                                        data = JSON.parse(data)
                                        response(data)
                                        // }
                                    }
                                })

                            }
                         
                                
                        }
                        
                        //url: `/api/SaleSheet/GetSheetManufactor?sheetType=X&supplier_id=${supplier_id}&operKey=` + window.g_operKey,
                        // source: []
                    })
                },
                align: 'center',
                initeditor: function (row, cellvalue, editor, celltext, pressedkey) {
                    var inputField = editor.find('input');
                    if (pressedkey) {
                        inputField.jqxInput('val', pressedkey);
                        inputField.jqxInput('selectLast');
                    } else {
                        // 直接设置 valueMember 的值（manufactor_id）
                        inputField.jqxInput('val', celltext);
                        inputField.jqxInput('selectAll');
                    }
                },
                
                geteditorvalue: function (row, cellvalue, editor) {
                    var v = editor.find('input').val();
                    //v.value = v.manufactor_id
                    //$('#manufactor_id').val(v.manufactor_id)
                    $('#jqxgrid').jqxGrid('setcellvalue', row, "manufactor_id", v.manufactor_id);
                    return v;
                },
            },

            {
                // to-do: show a feather svg like multi-flavour item's quantity
                text: '序列号', datafield: 'sn_code', width: '70', sortable: false, align: 'center', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return true; // 先禁止编辑
                },
                //geteditorvalue: function (row, cellvalue, editor) {
                //    var e = editor.find('input')
                //    if (e.length > 0) {
                //        var code = e[0].value
                //        // if the code is like "http(s)://.../...?code=xxxxx"
                //        if (code.match(/code=(\w+)/)) {
                //            code = code.match(/code=(\w+)/)[1];
                //        }
                //        // else if the code is like "http(s)://.../...?cd=xxxxx"
                //        else if (code.match(/cd=(\w+)/)) {
                //            code = code.match(/cd=(\w+)/)[1];
                //        }
                //        return code;
                //    }
                //    return '';
                //}
            },
            {
                text: '备注', columntype: 'template', datafield: 'remark', width: '70', sortable: false, align: 'center',
                createeditor: function (row, cellvalue, editor, cellText, width, height) {
                        var element = $('<div></div >');
                        editor.append(element);
                        var inputElement = editor.find('div')[0];
                        var datafields = new Array({ datafield: 'remark', text: '', width: width });
                        $(inputElement).jqxInput({
                            height: height, width: width,
                            borderShape: 'none',
                            buttonUsage: 'list',
                            dropDownHeight: 160,
                            dropDownWidth:width,
                            keepNoValueLabel:true,
                            displayMember: 'remark',
                            valueMember: 'remark',
                            datafields: datafields,
                            searchFields: ['remark'],
                            maxRecords: 9,
                            url: '/api/SaleSheet/GetSheetRemarks?sheetType=X&operKey=' + window.g_operKey,
                           // source: []
                        });
                }, align: 'center',
                cellbeginedit: function (row, datafield, columntype, value) {
                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false;
                },
                initeditor: function (row, cellvalue, editor, celltext, pressedkey) {
                        var inputField = editor.find('input');
                        if (pressedkey) {
                            inputField.val(pressedkey);
                            inputField.jqxInput('selectLast');
                        }
                        else {
                            inputField.val({ value: cellvalue, label: celltext });
                            if (cellvalue == '') inputField.val('');
                            inputField.jqxInput('selectAll');
                        }
                    },
                    geteditorvalue: function (row, cellvalue, editor) {
                        var e = editor.find('input')
                        if (e.length > 0) {
                            var v = e[0]
                            return v.value
                        }                    
                        return '';
                    }
            },
            {
                text: '批发价',
                datafield: 'wholesale_price',
                hidden:true,
                width: '100',
                align: 'center',
                cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                cellsrenderer: price_renderer

            },
            {
                text: '零售价',
                datafield: 'retail_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                cellsrenderer: money_renderer

                
            },
            {
                text: '最近零售价',
                datafield: 'recent_retail_price',
                width: '10%',
                align: 'center',
                cellsalign: 'right',
                columntype: 'template',
                createeditor: create_recent_retail_price_editor,
                initeditor: init_editor,
                geteditorvalue: function (row, cellvalue, editor) {

                    var v = editor.find('input').val();
                    if (v.label && !v.value) {
                        v.value = v.label.toString()
                    }
                    //if (v.label) v = v.label;
                    return v;
                },
                cellbeginedit: function (row, datafield, columntype, value) {

                    var item_id = $('#jqxgrid').jqxGrid('getcellvalue', row, 'item_id');
                    if (!item_id) return false;
                    // return false;
                },
                cellsrenderer: money_renderer

            },
            {
                text: '小单位零售价',
                datafield: 's_retail_price',
                width: '120',
                align: 'center',
                cellsalign: 'right',
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                cellsrenderer: money_renderer

            },
            {
                text: '成本价',
                datafield: 'cost_price',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function(row, datafield, columntype, value) {
                    return false;
                },
                cellsrenderer: cost_price_renderer

            },
            {
                text: '预设进价',
                datafield: 'cost_price_buy',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties, rowData) {
                    var unit_factor = rowData.unit_factor
                    var cost_price_buy = rowData.cost_price_buy
                    if (cost_price_buy && unit_factor) {
                        var html = '<div class="jqx-grid-cell-right-align" style="margin-top: 10px;">' + toMoney (cost_price_buy * unit_factor,4,2) + '</div>';
                        return html;
                    }
                } 
            },
            {
                text: '加权平均价',
                datafield: 'cost_price_avg',
                width: '120',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                cellsrenderer:function (row, columnfield, value, defaulthtml, columnproperties, rowData) {
                        var unit_factor = rowData.unit_factor
                        var cost_price_avg = rowData.cost_price_avg
                        if (cost_price_avg && unit_factor) {
                            var html = '<div class="jqx-grid-cell-right-align" style="margin-top: 10px;">' + toMoney (cost_price_avg * unit_factor,4,2) + '</div>';
                            return html;
                        }
                    } 
            },
            {
                text: '最近平均进价',
                datafield: 'cost_price_recent',
                width: '120',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                },
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties, rowData) {
                    var unit_factor = rowData.unit_factor
                    var cost_price_recent = rowData.cost_price_recent
                    if (cost_price_recent && unit_factor) {
                        var html = '<div class="jqx-grid-cell-right-align" style="margin-top: 10px;">' + toMoney (cost_price_recent * unit_factor,4,2) + '</div>';
                        return html;
                    }
                } 
            },
            {
                text: '预设成本',
                datafield: 'cost_price_prop',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
                ,
                cellsrenderer: function (row, columnfield, value, defaulthtml, columnproperties, rowData) {
                    var unit_factor = rowData.unit_factor
                    var cost_price_prop = rowData.cost_price_prop
                    if (cost_price_prop && unit_factor) {
                        var html = '<div class="jqx-grid-cell-right-align" style="margin-top: 10px;">' + toMoney(cost_price_prop * unit_factor,4,2) + '</div>';
                        return html;
                    }
                } 
            },
            {
                text: '成本金额',
                datafield: 'cost_amount',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice,
                cellbeginedit: function(row, datafield, columntype, value) {
                    return false;
                },
                cellsrender: money_renderer,
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount
            },
            {
                text: '利润',
                datafield: 'profit',
                width: '60',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice || !canSeeProfit,
                cellbeginedit: function(row, datafield, columntype, value) {
                    return false;
                },
                cellsrender: money_renderer,
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount
            },
            {
                text: '利润率(%)',
                datafield: 'profit_rate',
                width: '100',
                align: 'center',
                cellsalign: 'right',
                hidden: true,
                hideOnLoad: !canSeeBuyPrice || !canSeeProfit,
                cellbeginedit:  function(row, datafield, columntype, value) {
                    return false;
                },
                cellsrender: money_renderer,
                aggregatesrenderer: aggregatesrenderer_sub_amount_profit_rate
            },
            {
                text: '实际库存',
                datafield: 'stock_qty_unit',
                width: '100',
                align: 'center',
                cellsalign: 'center',
                cellbeginedit:  function(row, datafield, columntype, value) {

                    return false;
                }
            },
            {
                text: '占用库存',
                datafield: 'sell_pend_qty_unit',
                width: '100',
                align: 'center',
                cellsalign: 'center', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {

                    return false;
                }
            },
            {
                text: '可用库存',
                datafield: 'usable_stock_qty_unit',
                width: '100',
                align: 'center',
                cellsalign: 'center',
                cellbeginedit: function (row, datafield, columntype, value) { 
                    return false;
                }
            },
            {
                text: '可还数量',
                datafield: 'specific_qty_unit',
                width: '100',
                align: 'center',
                cellsalign: 'center',
                hidden: true,
                hideOnLoad: true,
                cellbeginedit: function(row, datafield, columntype, value) {
                    if (datafield === "specific_qty_unit") return false;

                }
            },
            {
                text: '可用还货数',
                datafield: 'order_qty',
                width: '100',
                align: 'center',
                cellsalign: 'center',
                hidden: true,
                hideOnLoad: true,
                cellbeginedit: function(row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '差额',
                datafield: 'price_diff_p',
                width: '100',
                align: 'center',
                cellsalign: 'center',
                
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '定货款账户',
                datafield: 'order_sub_name',
                align: 'center',
                cellsalign: 'center',
                hidden: true,
                hideOnLoad: true,
                cellbeginedit: function(row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: 'order_item_sheets_id',
                datafield: 'order_item_sheets_id',
                align: 'center',
                cellsalign: 'center',
                hidden: true,
                hideOnLoad: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: 'order_item_sheets_no',
                datafield: 'order_item_sheets_no',
                align: 'center',
                cellsalign: 'center',
                hidden: true,
                hideOnLoad: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            { text: '重量(小)', datafield: 's_weight', hidden: true, hideOnLoad: true },
            { text: '重量(中)', datafield: 'm_weight', hidden: true, hideOnLoad: true },
            { text: '重量(大)', datafield: 'b_weight', hidden: true, hideOnLoad: true },
            {
                text: '单位重量', datafield: 'unit_weight', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '重量',
                datafield: 'weight',
                hidden: true,
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            { text: '体积(小)', datafield: 's_volume', hidden: true, hideOnLoad: true },
            { text: '体积(中)', datafield: 'm_volume', hidden: true, hideOnLoad: true },
            { text: '体积(大)', datafield: 'b_volume', hidden: true, hideOnLoad: true },
            {
                text: '单位体积', datafield: 'unit_volume', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '体积',
                datafield: 'volume',
                hidden: true,
                aggregates: aggregates_sub_amount,
                aggregatesrenderer: aggregatesrenderer_sub_amount,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            { text: '陈列flow_id', datafield: 'disp_flow_id', hidden: true, hideOnLoad: true },
            { text: '定货款账户', datafield: 'order_sub_id', hidden: true, hideOnLoad: true },
            { text: '定货flow_id', datafield: 'order_flow_id', hidden: true, hideOnLoad: true },
            { text: '定货价(大)', datafield: 'b_order_price', hidden: true, hideOnLoad: true },
            { text: '陈列sheet_id', datafield: 'disp_sheet_id', hidden: true, hideOnLoad: true },
            { text: '陈列月', datafield: 'disp_month_id', hidden: true, hideOnLoad: true },
            { text: '陈列月份', datafield: 'disp_month', hidden: true, hideOnLoad: true },
            { text: '陈列剩余', datafield: 'disp_left_qty', hidden: true, hideOnLoad: true },
            // 会员积分用
            {
                text: '是否为会员积分兑换商品', datafield: 'vip_is_redeem', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '消耗会员积分', datafield: 'vip_used_point', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '积分兑换单位', datafield: 'vip_redeem_unit', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '积分兑换价格', datafield: 'vip_redeem_price', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '积分兑换数量', datafield: 'vip_redeem_amount', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '积分兑换详情', datafield: 'vip_redeem_description', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
            {
                text: '连锁删除ID', datafield: 'deleteLinkId', hidden: true,
                cellbeginedit: function (row, datafield, columntype, value) {
                    return false;
                }
            },
        ]
     }
    debugger
    adjustColumnsBySetting()
    
     $("#jqxgrid").jqxGrid(
         GridData
     );
     window.destroyWindow = function () {
        
         $('#jqxgrid').jqxGrid('clear');
         $('#jqxgrid').jqxGrid('destroy');
         window.GridData = null
     }
    renderCopySheet()
     
    window.loadSheetData = function (sheetRows) {
        //setFormDataItems(sheet)
        var supcustID = $('#supcust_id').val().value;
        var approve_time = $('#approve_time').text();
        if (supcustID) getClientAccountInfo(supcustID)
        initProduceDate(sheetRows)
        //使用定货会商品时，更新支付方式
        sheetRows.forEach((row) => {
            if (row.order_sub_id) {
                var payway1_id = $('#payway1_id').jqxInput('val')
                var payway1_name = payway1_id.label
                $('#payway1_id').jqxInput({ disabled: true })
                $('#payway1_amount').jqxInput({ disabled: true })
                $('#payway1_id .row-oper').remove()
                $('#payway1_amount .row-oper').remove()
                if (row.order_sub_name = payway1_name) $('#jqxgrid').jqxGrid('selectcell', row.index, 'order_sub_name');
            }

        })

        loadSheetRows(sheetRows)
        updateTotalAmount()
        $('#fee_btn').text(`销售费用：${$('#saleFeeSheetInfo').val() ? JSON.parse($('#saleFeeSheetInfo').val()).total_fee_amount : 0}`);
         
       mmRefreshStockQty()

      // ReturnItemsRender()
    }
     
    loadSheetData(sheetRows)
     
    enableHideCostColumn()
     

    $("#jqxgrid").on('cellendedit', cellendedit);

     function checkAllocateAmount() {
         let msg = "";

         let sheetType = $('#sheetType').val();
         let rows = $("#jqxgrid").jqxGrid("getrows");
         let totalAllocateAmount = 0;
         for (let i = 0; i < rows.length; i++) {
             let row = rows[i];
             if (row.item_id && isNumber(row.allocate_amount)) {
                 if (sheetType == 'T' && parseFloat(row.allocate_amount) != 0) {
                     return `退货单不支持分摊，第${i+1}行请清除分摊金额`;
                 }
                 totalAllocateAmount += parseFloat(row.allocate_amount); // 使用 parseFloat 进行精确处理
             }
         }


         let apportionFee = $('#saleFeeSheetInfo').val() ? JSON.parse($('#saleFeeSheetInfo').val()).total_fee_amount : 0;
         if (Math.abs(totalAllocateAmount - apportionFee) >= 0.01) {
             msg = `商品分摊金额合计(${toMoney(totalAllocateAmount)})≠分摊费用金额(${toMoney(apportionFee)})，请重新点击分摊，或手动修改分摊金额`;
         }
         return msg;
     }

    $("#btnSave").on('click', function () {
        if (!document.getElementById('confirm-toast-styles')) {
            const style = document.createElement('style');
            style.id = 'confirm-toast-styles';
            style.textContent = `
            .custom-confirm-toast {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 15px rgba(0,0,0,0.2);
                z-index: 10000;
                width: 300px;
                max-width: 90%;
                animation: fadeIn 0.3s;
            }
            .custom-confirm-toast .message {
                margin-bottom: 15px;
                color: #333;
                line-height: 1.5;
            }
            .custom-confirm-toast .buttons {
                display: flex;
                gap: 10px;
                justify-content: center;
            }
            .custom-confirm-toast button {
                padding: 8px 16px;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                min-width: 100px;
                font-size: 14px;
            }
            .custom-confirm-toast .confirm-btn {
                background: #4CAF50;
                color: white;
            }
            .custom-confirm-toast .cancel-btn {
                background: #f44336;
                color: white;
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translate(-50%, -60%); }
                to { opacity: 1; transform: translate(-50%, -50%); }
            }
        `;
            document.head.appendChild(style);
        }
        // 检查分配金额
        let msg = checkAllocateAmount();
        if (msg) {
            bw.toast(msg, 5000);
            return;
        }

        // 获取表单数据
        var res = GetSheetData();

        // 错误处理
        if (res.result === "Error") {
            bw.toast(res.msg, 5000);
            return;
        }

        // 利用闭包共享res.sheet
        const executeSave = function () {
            $.ajax({
                url: '/api/SaleSheet/Save',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(res.sheet),
                success: function (data) {
                    if (data.result === 'OK') {
                        $('#sheet_id').val(data.sheet_id);
                        $('#sheet_no').text(data.sheet_no);
                        if ($('#maker_name').text() == '') $('#maker_name').text(window.g_operName);
                        $('#make_time').text(data.make_time);

                        updateSheetState();
                        removeSheetFromCach();
                        bw.toast('保存成功', 1000);

                        var canDelete = window.getRightValue('sale.sheetSale.delete');
                        if (canDelete && canDelete.toLowerCase() == "false") {
                            $('#btnDelete').css('display', 'none');
                        }

                        if (window.g_companySetting && window.g_companySetting.newSheetAfterSave == 'True') {
                            btnCopySheetHead_click();
                        }
                    } else {
                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            });
        };

        // 警告处理
        if (res.result === "Warning") {
            const toast = document.createElement('div');
            toast.className = 'custom-confirm-toast';
            toast.innerHTML = `
            <div class="message">${res.msg}</div>
            <div class="buttons">
                <button class="confirm-btn">确认保存</button>
                <button class="cancel-btn">取消</button>
            </div>
        `;

            document.body.appendChild(toast);

            $(toast).find('.confirm-btn').on('click', function () {
                $(toast).remove();
                executeSave(); // 调用闭包函数
            });

            $(toast).find('.cancel-btn').on('click', function () {
                $(toast).remove();
               //bw.toast('操作已取消', 3000);
            });

            return;
        }

        executeSave(); 
    });
     
     $("#btnAdd").on('click', function () {
  
        var sheetType = $('#sheet_type').val();
        if (sheetType == 'SHEET_SALE') window.parent.newTabPage('销售单', `Sheets/SaleSheet?forReturn=false`);
        if (sheetType == 'SHEET_SALE_RETURN') window.parent.newTabPage('退货单', `Sheets/SaleSheet?forReturn=true`);
          
         if (!window.firstCachTime) {
             setTimeout(() => {
                 window.parent.closeTab(window);
             },50)
             
         }
     })
     function initProduceDate(rows) {
         rows.forEach((row) => {
             if (row.item_id && row.batch_level && row.batch_level !== "0" && !row.produce_date) {
                 row.produce_date = "无产期"
                 row.batch_id = "0"
             }
         })
     }
    function approveSheet(bReview) { 
        //updateTheCostAmount();
        var res = GetSheetData();
        console.log(res)
         if (res.result === "Error") {
             bw.toast(res.msg, 5000); return;
         }
        var sheet = res.sheet;

        var checked = $("#noArrearsBill").jqxCheckBox('checked');

        sheet.noArrearsBillChecked = checked === true ? "true" : "false"
        //JSON.parse(sheet.prepay_sheet_info)
         var alertMsg = ''
         var alertCondi = "审核"
         if (bReview) alertCondi = "复核"
         function approve() {
             jConfirm('确定要'+alertCondi+'吗?<br>' + alertMsg, function () {

                 if (res.result === "Error") {
                     bw.toast(res.msg, 5000); return;
                 }
                 
                 $("#btnApprove").attr('disabled', true)
                 var previousPage = null
                 if (window.srcWindow) previousPage = window.srcWindow.app

                 var sheets = ""
                 var checkAccount_sellerID = ""
                 if (previousPage) {
                     sheets = previousPage.sheets;

                     if (sheets) {
                         checkAccount_sellerID = sheets.sellerID;
                         sheets = getPreviousSheetsList(sheets);
                     }
                 }

                 var tempi = null
                 if (window.g_temp_index) tempi = window.g_temp_index
                 /* zhangwei temp
                 if (window.g_companySetting && window.g_companySetting.happenTimeOnSave == 'True' && sheet.make_time) {
                     sheet.TempHappenTime = 'false'
                    // sheet.happen_time = sheet.make_time
                     $('#TempHappenTime').val('false')
                     $('#happen_time').val(sheet.make_time)
                 }*/
                 if (bReview) sheet.bReview = true
                 $.ajax({
                     url: '/api/SaleSheet/SaveAndApprove',
                     type: 'POST',
                     contentType: 'application/json',
                     data: JSON.stringify(sheet),
                     success: function (data) {
                         if (data.result === 'OK') {
                             $('#sheet_id').val(data.sheet_id);
                             $('#sheet_no').text(data.sheet_no);
                             $('#approve_time').text(data.approve_time);
                             $('#approve_time').val(data.approve_time);
                            // $('#isRedAndChange').val(data.isRedAndChange);
                             $('#happen_time').jqxDateTimeInput('val', data.happen_time);
                             if (data.sale_fee_apportion_sheet_id) {
                                 let saleFeeSheetInfo = $('#saleFeeSheetInfo').val() ? JSON.parse($('#saleFeeSheetInfo').val()) : '';
                                 if (saleFeeSheetInfo && Object.keys(saleFeeSheetInfo).length > 0) {
                                     saleFeeSheetInfo.sale_fee_apportion_sheet_id = data.sale_fee_apportion_sheet_id;
                                     $('#saleFeeSheetInfo').val(JSON.stringify(saleFeeSheetInfo));
                                 }
                                 window.g_initFormData.approve_time = data.approve_time;
                             }
                             // 附件窗口设置
                             window.appendixApproved = true
                             updateSheetState()
                             removeSheetFromCach()
                             $("#noArrearsBill").jqxCheckBox({ disabled: true }); 
                             if (bReview) bw.toast('复核成功', 3000);
                             else bw.toast('审核成功', 3000); 
                             if (srcWindow && srcWindow.updateGridRow) {
                                 if (bReview) {
                                     $('#review_time').text(data.approve_time);
                                     $("#btnReview").attr('disabled', true);
                                     srcWindow.updateGridRow(data.sheet_id, { sheet_status: '已复核', approve_time: data.approve_time })
                                 }
                                 else {
                                     srcWindow.updateGridRow(data.sheet_id, { sheet_status: '已审', approve_time: data.approve_time })
                                 }
                                
                             }

                             //srcWindow.refreshPrintCount(data.sheet_id, { sheet_status: '已审', approve_time: data.approve_time })
                             // window.parent.refreshSheetPrintCount1(window.srcWindow)

                             var sellerID = $('#seller_id').val()
                             var order_sheet_id = $('#order_sheet_id').val()
                             var senders_id = $('#senders_id').val()
                             var flag = false
                             var senderID = senders_id
                             if (senders_id.length > 1) senderID = senders_id[0]

                             var newRes = GetSheetData();
                             if (newRes.result === "Error") {
                                 bw.toast(res.msg, 5000); return;
                             }
                             var newSheetInfo = newRes.sheet
                             if (order_sheet_id) {
                                 if (senderID.value == checkAccount_sellerID) {
                                     flag = true
                                 }
                             } else {
                                 if (sellerID.value == checkAccount_sellerID) {
                                     flag = true
                                 }
                             }

                             if (flag) {
                                 newSheetInfo.isChecked = true
                                 newSheetInfo.isFromWeb = false
                                 newSheetInfo.isSum = false
                                 newSheetInfo.is_imported = false
                                 newSheetInfo.fixinG_ARREARS = false
                                 newSheetInfo.sheetRows = newSheetInfo.SheetRows
                                 if (tempi) {
                                     sheets.splice(tempi, 0, newSheetInfo)
                                 } else {
                                     sheets.unshift(newSheetInfo)
                                 }
                                 //sheets.splice(tempi, 0, newSheetInfo)
                                 //sheets.push(newSheetInfo)
                                 assignSheets(sheets)
                             }
                             if (window.g_companySetting) {
                                 if (window.g_companySetting.newSheetAfterApprove == 'True') {
                                     btnCopySheetHead_click()
                                 }
                             }


                         }
                         else {
                             $("#btnApprove").attr('disabled', false)

                             bw.toast(data.msg, 3000);
                         }
                     },
                     error: function (xhr) {
                         console.log("返回响应信息：" + xhr.responseText);
                     }
                 });
             }, "");
         }
         if (sheet.sheet_id != '') {

             checkHasInventory(sheet, 'SaleSheet').then((data) => {
                 let itemNameList = data.inventoryItemNames
                 itemNameList.forEach((itemName) => {
                     if (alertMsg == '') {
                         alertMsg = itemName
                     } else {
                         alertMsg += ',' + itemName
                     }
                 })
                 if (alertMsg != '') {
                     alertMsg = alertMsg + '<br>以上商品单据保存期间发生过盘点，审核可能会造成库存不准'
                 }
                 approve()
             })
         } else {
             approve()
         }
     }

     $("#btnApprove").on('click',  function () {
      
         approveSheet(false)
         
     });

     $("#btnReview").on('click', function () {
         var sheet_id = $('#sheet_id').val()
         var approve_time = $('#approve_time').val()
         if (approve_time) {
             $.ajax({
                 // url: '/api/SaleOrderSheet/Review?operKey=' + g_operKey,
                 url: '/api/SaleSheet/Review',
                 type: 'POST',
                 contentType: 'application/json',
                 // data: JSON.stringify({sheet_id:'1',supcust_id:'2'}),
                 data: JSON.stringify({ sheet_id: sheet_id, operKey: g_operKey, approve_time: approve_time }),
                 success: function (data) {
                     if (data.result === 'OK') {
                         $('#sheet_id').val(data.sheet_id);
                         $('#sheet_no').text(data.sheet_no);
                         $('#review_time').text(data.review_time)
                         updateSheetState();
                         removeSheetFromCach()
                         bw.toast('复核成功', 3000);
                     }
                     else {
                         bw.toast(data.msg, 3000);
                     }
                 },
                 error: function (xhr) {
                     console.log("返回响应信息：" + xhr.responseText);
                 }
             })
         }
         else {
                 approveSheet(true)
         }

     });

    $("#btnDelete").on('click', function () {
        /*var res = GetSheetData();
       
        if (res.result === "Error") {
            bw.toast(res.msg, 5000); return;
        }
        var sheet = res.sheet;
        */

        var sheet_id = $('#sheet_id').val()
      
        jConfirm('确定要删除本单据吗?', function () {
            $.ajax({
                url: '/api/SaleSheet/Delete',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ operKey: g_operKey, sheet_id: sheet_id }),
                success: function (data) {
                    if (data.result === 'OK') {
                        //$("#btnApprove").attr('disabled', true);
                        $("#btnSave").attr('disabled', true)
                        $("#btnApprove").attr('disabled', true)
                        $("#btnDelete").attr('disabled', true)
                        $("#btnPrint").attr('disabled', true)
                        $("#btnSyncAccessTicketSys").attr('disabled', true);
                        $("#btnCopy").attr('disabled', true)
                        $("#btnAdd").attr('disabled', true)
                        removeSheetFromCach()
                        bw.toast('删除成功,即将关闭窗口', 3000);
                        setTimeout(function () {
                            window.parent.closeTab(window);
                        }, 2000);

                    }
                    else {
                        bw.toast(data.msg, 3000);
                    }
                },
                error: function (xhr) {
                    console.log("返回响应信息：" + xhr.responseText);
                }
            });
        }, "");

    });
 
    $("#btnPrint").on('click', function () {


        //获取表格的类型
        var sheetType = $('#sheet_type').val();
        var sheet_id = $('#sheet_id').val();
        //获取客户的id
        var clientID = $('#supcust_id').jqxInput('val');
        if (clientID) clientID = clientID.value;

       // var sheet = getSheetToPrint()
       // if (!sheet) return
        $.ajax({
            url: '/api/PrintTemplate/GetTemplateToUse',
            type: 'GET',
            contentType: 'application/json',
            data: {
                operKey: g_operKey,
                sheetType: sheetType,
                clientID: clientID
            },
            success: function (printInfo) {
                if (printInfo.result === 'OK') {
                    if (printInfo.templateList.length == 0) {
                        bw.toast("没有可用打印模板", 5000)
                        return
                    }

                    var tmp = printInfo.templateList[0]
                    var sTmp = tmp.template_content
                    tmp = JSON.parse(tmp.template_content)


                    var printTemplate = []


                    if (sTmp.indexOf('"prepay_balance"') >= 0) printTemplate.push({ name: "prepay_balance" })
                    if (sTmp.indexOf('"arrears_balance"') >= 0) printTemplate.push({ name: "arrears_balance" })
                    if (sTmp.indexOf('"order_item_balance"') >= 0) printTemplate.push({ name: "order_item_balance" })
                    if (sTmp.indexOf('"print_count"') >= 0) printTemplate.push({ name: "print_count" })
                    if (sTmp.indexOf('"name":"give_qty_unit"') >= 0) printTemplate.push({ name: "give_qty_unit" })

                    var smallUnitBarcode = document.all.ckPrintSmallBarcode.checked

                    $.ajax({
                        url: '/api/SaleSheet/GetSheetToPrint',
                        type: 'GET',
                        contentType: 'application/json',
                        data: {
                            operKey: g_operKey,
                            sheet_id: sheet_id,
                            smallUnitBarcode: smallUnitBarcode,
                            printTemplate: JSON.stringify(printTemplate)

                        },
                        success: function (data) {
                            if (data.result === 'OK') {
                                var sheet = data.sheet


                                var container = window.parent.CEFPrinter
                                if (!container)
                                    container = window.parent.CefGlue

                                if (!container)
                                    container = window.parent

                                if (!container.printSheetByTemplate) {
                                     
                                    if (printInfo.cloudPrinters.length == 0) {
                                        bw.toast("请到客户端打印，或是添加一个云打印机", 5000)
                                        return
                                    }
                                    var ptr = printInfo.cloudPrinters[0]
                                    var device_id = ptr.device_id
                                    var check_code = ptr.check_code
                                    var printer_brand = ptr.printer_brand
                                    var url = '/AppApi/CloudPrint/PrintSheetWithTemplate';
                                    if (window.CoolieServerUri) {
                                        url = window.CoolieServerUri + url;
                                    }

                                    $.ajax({
                                        url: url,
                                        type: 'POST',
                                        contentType: 'application/json',
                                        data: JSON.stringify({
                                            operKey: g_operKey,
                                            device_id: device_id,
                                            check_code: check_code,
                                            printer_brand: printer_brand,
                                            sheet: sheet,
                                            tmp: tmp,
                                            cus_orderid: sheet.sheet_no,
                                            copies: "1"
                                        }),
                                        success: function (res) { 
                                            console.log(res)
                                            var msg = res.msg == "" ? "打印请求已发送,请等待" : res.msg
                                            bw.toast(msg)
                                            console.log("Print post sent to " + device_id)
                                        },
                                        error: function (xhr) {
                                            bw.toast('发送打印请求失败')
                                            console.log("返回响应信息：" + xhr.responseText)
                                        }
                                    });                                    
                                    return
                                }
                                 
                                window.parent.g_SheetsWindowForPrint = window.srcWindow
                                container.printSheetByTemplate(sheet, tmp, true, printInfo.cloudPrinters, printInfo.variables) 
                              
                            }
                            else {
                                bw.toast(data.msg, 3000)
                            }
                        },
                        error: function (xhr) {
                            bw.toast('获取单据信息失败')
                            console.log("返回响应信息：" + xhr.responseText)
                        }
                    }) 
                }
                else {
                    bw.toast(data.msg, 3000)
                }
            },
            error: function (xhr) {
                bw.toast('网络连接失败')
                console.log("返回响应信息：" + xhr.responseText)
            }
        })
    })


     $(document).ready(function () {
         // 此处定义 windowHeight 和 windowWidth，或从实际大小获取
         let windowHeight = $(window).height();
         let windowWidth = $(window).width();

         // 初始化 jqxWindow
         $("#popAppendix").jqxWindow({
             closeButtonAction: 'keepFrame',
             isModal: true,
             modalOpacity: 0.3,
             height: 600,
             width: 900,
             maxHeight: windowHeight,
             maxWidth: windowWidth,
             theme: 'summer',
             autoOpen: false,
             showCloseButton: true,
             closeButtonSize: 32,
             showAnimationDuration: 500,
             closeAnimationDuration: 500,
             animationType: 'fade'
         });
     });

     // 附件列表
     let appendixPhotoList = null

     window.appendixSrcList = []

     if ($("#appendix_photos").val()) {
         let photoList = JSON.parse($("#appendix_photos").val())

         for (p of photoList) {//用for of的话，photoList为键值格式时会报错
             window.appendixSrcList.push(Href + '/uploads' + p)
         }
     }


     window.appendixWindowCreated = false
     window.appendixApproved = approve_time ? true: false
     let iframeElement = null
     $("#btnAppendix").on('click', function () {
         // 附件按钮触发
         $('#popAppendix').jqxWindow('open')

         // var clientView = 'ClientsView'
         if (!window.appendixWindowCreated) {
             debugger
             let approve_time = $('#approve_time').text();
             let src = `/BaseInfo/AppendixPhotoEdit?operKey=${g_operKey}`
             if (approve_time) { src = `/BaseInfo/AppendixPhotoEdit?operKey=${g_operKey}&editable=false` }
             $("#popAppendix").jqxWindow('setContent', `<iframe id="iframeAppendix" src="${src}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
             window.appendixWindowCreated = true
             $('#iframeAppendix').on('load', function () {
                 // 获取 iframe 元素
                 iframeElement = document.getElementById('iframeAppendix');
                 // 构造要发送的数据
                 let photo = $("#appendix_photos").val()
                 debugger
                 if (photo === "" || photo === undefined || photo === null) {
                     return
                 } else {
                     // let href = Href + "/"
                     let href = Href + "/uploads"
                     // 向 iframe 发送数据
                     iframeElement.contentWindow.postMessage({ msg: "loadPhoto", photo: photo, href: href }, '*');
                 }
             });

         }
         else {
             if (window.appendixApproved) {

                 // 获取 iframe 元素
                 // let iframeElement = document.getElementById('iframeAppendix');
                 // 构造要发送的数据

                 iframeElement.contentWindow.postMessage({ msg: "setDisable" }, '*');


             }
             if (!window.appendixApproved && window.appendixWindowCreated) {
                 iframeElement.contentWindow.postMessage({ msg: "setAble" }, '*');
             }
         }

         // $("#popClient").jqxWindow('setContent', `<iframe src="/BaseInfo/ClientsView?forSelect=1&forAll=true&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);


     })

    $('#choosetemplate').on('click', function () {

        var maskBg = document.getElementById('topCoverDiv');
        var dia = document.getElementById('dia');
        maskBg.style.display = (maskBg.style.display == 'none') ? 'block' : 'none';
        dia.style.display = (dia.style.display == 'none') ? 'block' : 'none';


        //获取表格的类型
        var sheetType = $('#sheet_type').val();
        //获取客户的id
        var clientID = $('#supcust_id').jqxInput('val');
        if (clientID) clientID = clientID.value;
        //获取表单的数据
       // var sheet = getSheetToPrint()
        //if (!sheet) return
        $.ajax({
            url: '/api/PrintTemplate/GetTemplateToUse',
            type: 'GET',
            contentType: 'application/json',
            data: {
                operKey: g_operKey,
                sheetType: sheetType,
                clientID: clientID
            },
            success: function (printInfo) {
                if (printInfo.result === 'OK') {
                    if (printInfo.templateList.length == 0) {
                        bw.toast("没有可用打印模板", 5000)
                        return
                    }

                    
                    var templateList = printInfo.templateList;
                    var templateShowInHTML = $('#template-list');
                    templateShowInHTML.empty();
                    for (let i = 0; i < templateList.length; i++) {
                        templateHTML = `<button id="templatePrint" value="${i}" style="margin: 10px 0px 0px 10px;width:auto;">${templateList[i].template_name}</button>`
                        templateShowInHTML.append(templateHTML);
                    }
                 

                    $("[id='templatePrint']").on('click', function () {
                        var index = parseInt(this.value);
                        var tmp = printInfo.templateList[index];
                        var sTmp = tmp.template_content
                        tmp = JSON.parse(tmp.template_content);

                        var printTemplate = []
                       

                        if (sTmp.indexOf('"prepay_balance"') >= 0) printTemplate.push({ name: "prepay_balance" })
                        if (sTmp.indexOf('"arrears_balance"') >= 0) printTemplate.push({ name: "arrears_balance" })
                        if (sTmp.indexOf('"order_item_balance"') >= 0) printTemplate.push({ name: "order_item_balance" })
                        if (sTmp.indexOf('"print_count"') >= 0) printTemplate.push({ name: "print_count" })
                        if (sTmp.indexOf('"name":"give_qty_unit"') >= 0) printTemplate.push({ name: "give_qty_unit" })
                     
                        var smallUnitBarcode = document.all.ckPrintSmallBarcode.checked
                        var sheet_id = $('#sheet_id').val();

                        $.ajax({
                            url: '/api/SaleSheet/GetSheetToPrint',
                            type: 'GET',
                            contentType: 'application/json',
                            data: {
                                operKey: g_operKey,
                                sheet_id: sheet_id,
                                smallUnitBarcode: smallUnitBarcode,
                                printTemplate: JSON.stringify(printTemplate)

                            },
                            success: function (data) {
                                if (data.result === 'OK') {
                                    var sheet = data.sheet

                                    var container = window.parent.CEFPrinter
                                    if (!container)
                                        container = window.parent.CefGlue

                                    if (!container)
                                        container = window.parent

                                    if (!container.printSheetByTemplate) {

                                        if (printInfo.cloudPrinters.length == 0) {
                                            bw.toast("请到客户端打印，或是添加一个云打印机", 5000)
                                            return
                                        }
                                        templateShowInHTML.empty();
                                        templateShowInHTML.append("请选择打印机：<br><br>");
                                        for (let i = 0; i < printInfo.cloudPrinters.length; i++) {
                                            templateHTML = `<button id="cloudPrinter" value="${i}" style="margin: 10px 0px 0px 10px;width:auto;">${printInfo.cloudPrinters[i].printer_name}</button>`
                                            templateShowInHTML.append(templateHTML);
                                        }
                                        $("[id='cloudPrinter']").on('click', function () {
                                            var index = parseInt(this.value);
                                                
                                            var ptr = printInfo.cloudPrinters[index]
                                            var device_id = ptr.device_id
                                            var check_code = ptr.check_code
                                            var printer_brand = ptr.printer_brand

                                                    $.ajax({
                                                        url: '/AppApi/CloudPrint/PrintSheetWithTemplate',
                                                        type: 'POST',
                                                        contentType: 'application/json',
                                                        data: JSON.stringify({
                                                            operKey: g_operKey,
                                                            device_id: device_id,
                                                            check_code: check_code,
                                                            printer_brand: printer_brand,
                                                            sheet: sheet,
                                                            tmp: tmp,
                                                            variables:printInfo.variables,
                                                            cus_orderid: sheet.sheet_no,
                                                            copies: "1"
                                                        }),
                                                        success: function (res) {
                                                            console.log(res)
                                                            var msg = res.msg == "" ? "打印请求已发送,请等待" : res.msg
                                                            bw.toast(msg)
                                                            console.log("Print post sent to " + device_id)
                                                        },
                                                        error: function (xhr) {
                                                            bw.toast('发送打印请求失败')
                                                            console.log("返回响应信息：" + xhr.responseText)
                                                        }
                                                    });
                                                })

                                         
                                        return
                                    }
                                   
                                    window.parent.g_SheetsWindowForPrint = window.srcWindow
                                    container.printSheetByTemplate(sheet, tmp, true, printInfo.cloudPrinters,printInfo.variables)

                                    var clientVersion = 0
                                    if (window.parent && window.parent.CefGlue) {
                                        clientVersion = window.parent.CefGlue.getClientVersion()
                                    }

                                    if (parseFloat(clientVersion) < 3.32) {
                                        $.ajax({
                                            url: '/api/Printer/PrintMark',
                                            type: 'POST',
                                            contentType: 'application/json',
                                            data: JSON.stringify({
                                                operKey: g_operKey,
                                                sheetType: 'X',
                                                sheetIDs: sheet.sheet_id,
                                                printEach: true,
                                                printSum: false
                                            }),
                                            success: function (data) {
                                                if (data.result === 'OK') {
                                                }
                                                else {

                                                }
                                            },
                                            error: function (xhr) {
                                                // console.log("返回响应信息：" + xhr.responseText)
                                            }
                                        })
                                    }
                                }
                                else {
                                    bw.toast(data.msg, 3000)
                                }
                            },
                            error: function (xhr) {
                                console.log("返回响应信息：" + xhr.responseText)
                            }
                        }) 
                         
                    });
                  

                    return;

                }
                else {
                    bw.toast(data.msg, 3000);
                }
            },
            error: function (xhr) {
                console.log("返回响应信息：" + xhr.responseText);
            }
        });
    });
     let supcustWhenFocus
     $('#supcust_id>input').on('focus', function () {
         supcustWhenFocus =$(this).val()
     })
     $('#supcust_id').on('optionSelected', function (a, b) {
         
         let oldValue = supcustWhenFocus
         let value = $('#supcust_id').val()
         let sheetType = $('#sheet_type').val();
         if (sheetType == 'SHEET_SALE' && oldValue == '') {
             SetBranchAndSellerOnSupcustUpdate(value.v, saleRememberBranch, saleAndOrderRemeberSeller)
         }
         if ((sheetType == 'SHEET_SALE' || sheetType == 'SHEET_SALE_RETURN') && $('#jqxgrid').jqxGrid('getrows').filter(r=>r.item_id!=''&&r.item_id!=undefined).length>0 && JSON.stringify(oldValue) != JSON.stringify(value)) {
             $('#cancle').click(function () {
                 $('#changeClientAlertBox').hide();
                 $('#supcust_id').jqxInput('val', { value: oldValue.value, label: oldValue.label })
             })
             $('#confirm').click(function () {
                 SetBranchAndSellerOnSupcustUpdate(value.v, saleRememberBranch, saleAndOrderRemeberSeller)
                 let refreshFlag = $("input[name='changeClient']:checked").val()
                 let id = ''
                 if (value) id = value.value
                 getClientAccountInfo(id, true)
                 if (refreshFlag == 'true') {

                     const branch_id = $('#branch_id').jqxInput('val').value
                     $('#changeClientAlertBox').hide();
                     refreshRowsBySupChange(id, branch_id)
                 } else {
                     $('#changeClientAlertBox').hide();
                 
                 }
                 $('#confirm').off('click')
             })
             $('#changeClientAlertBox').show()

         }
         else
         {
             let id = ''
             if (value) id = value.value
             getClientAccountInfo(id,true)
             const branch_id = $('#branch_id').jqxInput('val').value
             refreshRowsBySupChange(id, branch_id)
         }
     })
    //let sheetType = $("#sheetType").val()
    //if ((sheetType == "T" || sheetType == "TD") && window.g_companySetting && window.g_companySetting.backBranchPositionType) {
    //    $('#branch_id').on('change', function (a, b) {
    //        var rowsData = $("#jqxgrid").jqxGrid('getrows')
    //        let rowindexs = []
    //        for (let i = 0; i < rowsData.length; i++) {
    //            let row = rowsData[i]
    //            if (!row.item_id || row.branch_id) continue
    //            rowindexs.push(i)
    //        }
    //        var branch_id = $('#branch_id').val().value
    //        branch_id = branch_id ? branch_id : "-1"
    //        var backBranchPositionType = window.g_companySetting.backBranchPositionType
    //        console.log(backBranchPositionType)
    //        if (rowindexs.length) getBranchPositionForReturn(rowindexs, branch_id, backBranchPositionType)
    //    })
     //}
     $('#left_amount').on('change', function () {
         // 好像只有在left_amount被清除才会调用
         var left_amount = $('#left_amount').jqxInput('val');
         setArrearsCheckBox(left_amount)
         
     });
    window.onresize()
    window.onCopySheet = function () {
        // order_source不清除，自动同步的单据复制后不能再生成同步单据
       // $('#order_source').val('') 写入sheethead
        let leftAmount = $("left_amount").val()
        let sheetType = $("sheetType").val()
        if (toMoney(leftAmount) !== 0
            && window.g_companySetting
            && window.g_companySetting.saleNeedMarkIOU
            && window.g_companySetting.saleNeedMarkIOU.toLowerCase() == 'true'
            && sheetType === 'X') {
            // 有欠款且勾选，显示checkbox
            $("#div_noArrearsBill").css('display', '')
        }
        $("#iou_get_time").val("")
        $("#noArrearsBill").jqxCheckBox({ checked: false });
        $("#noArrearsBill").jqxCheckBox({ disabled: false }); 
        window.appendixWindowCreated = false
        window.appendixApproved = false
    }
    $('#getOrder').on('click', function () {
       /* let order_sheet_no = $('#order_sheet_no').val()*/
        let sheet_id = $('#order_sheet_id').val()
        let url = `/Sheets/SaleOrderSheet?sheet_id=${sheet_id}`
        window.parent.newTabPage('销售订单', url, window);
    })

   
     //费用分摊 jqxWindows初始化
     $("#popFee").jqxWindow({ isModal: true, modalOpacity: 0.3, height: '82%', width: '80%', theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });

}

function setArrearsCheckBox(leftAmount) {
    var setting = window.g_companySetting
    if (setting && setting.saleNeedMarkIOU && setting.saleNeedMarkIOU.toLowerCase() == 'true') {
        let sheetType = $('#sheetType').val()
        if (toMoney(leftAmount) && toMoney(leftAmount) !== 0 && $("#noArrearsBill").length > 0 ) {
            $("#div_noArrearsBill").css('display', '')
            //$("#noArrearsBill").show();

        } else if ((!leftAmount || toMoney(leftAmount) === 0) && $("#noArrearsBill").length > 0) {
            $("#div_noArrearsBill").css('display', 'none')
            $("#noArrearsBill").jqxCheckBox({ checked: false });
        }
    }
}
function ShowFeeBox() {
    //let approveTime = $('#approve_time').val();
    let saleSheetId = $('#sheet_id').val();
    let saleSheetIdPara = saleSheetId ? `&sale_sheet_id=${saleSheetId}` : '';
    let saleFeeApportionInfo = $('#saleFeeSheetInfo').val() ? JSON.parse($('#saleFeeSheetInfo').val()) : {};
    window.g_initFormData.saleFeeSheetInfo = JSON.stringify(saleFeeApportionInfo);
    if (!saleSheetId && window.g_initFormData.approve_time) {
        window.g_initFormData.approve_time = '';//复制过来的时候没清掉，跳转分摊单会看这个属性
    }
    let saleFeeAppoSheetId = saleFeeApportionInfo.sale_fee_apportion_sheet_id ? `&sheet_id=${saleFeeApportionInfo.sale_fee_apportion_sheet_id}` : '';


    $('#popFee').jqxWindow('open');
    $("#popFee").jqxWindow('setContent', `<iframe src="/Sheets/SaleFeeApportionSheet?inSaleSheet=true${saleFeeAppoSheetId}${saleSheetIdPara}&operKey=${g_operKey}" width="100%" height="100%" scrolling="no" frameborder="no"></iframe>`);
}

function getOrderById(sheet_id, operKey) {
    var win = this;
    $.get('/appapi/AppSheetSaleOrder/Load?operKey=' + operKey + '&sheetId=' + sheet_id).then(result => {
        if (result.result == 'OK') {
            var sheet = result.sheet
            console.log(sheet)
            loadSheetRows(sheet.sheetRows)
            win.$('#supcust_id').jqxInput('val', { value: sheet.supcust_id, label: sheet.sup_name })
            win.$('#seller_id').jqxInput('val', { value: sheet.seller_id, label: sheet.seller_name })
            win.$('#branch_id').jqxInput('val', { value: sheet.branch_id, label: sheet.branch_name })
            win.$('#inputhappen_time').val(sheet.happen_time)
            win.$('#order_sheet_no').text(sheet.sheet_no)
            win.$('#order_sheet_id').text(sheet.sheet_id)
            win.updateTotalAmount()
            win.updatePayAmount()
            return true
        }
    })
}

//if (document.addEventListener) {
  //  document.addEventListener("DOMMouseScroll", onMouseLeaveRealPrice, false);
//}
//window.onmousewheel = document.onmousewheel = onMouseLeaveRealPrice;

window.onmousewheel = function (e) { 
    onMouseLeaveRealPrice;
}

window.onPayAmountUpdated = function (leftAmount) {
     
    setArrearsCheckBox(leftAmount)
}
window.onSheetPageUpdated = function () {
    var approve_time = $('#approve_time').text();

    if ($("#noArrearsBill").length > 0 && window.g_companySetting && window.g_companySetting.saleNeedMarkIOU && window.g_companySetting.saleNeedMarkIOU.toLowerCase() == 'true') {
        if (approve_time) {
            $("#noArrearsBill").jqxCheckBox({ disabled: true });
        }
        else {
            $("#noArrearsBill").jqxCheckBox({ disabled: false });
        }
    }

}
function btnItemInfoSyncTest_click() {
    console.log("cync click")
    var sendData = { clientID: "113", planID: "40", operKey: g_operKey }
    $.ajax({
        url: '../api/ImportInfo/ItemInfoSync',
        type: 'POST',
        contentType: 'application/json',
        // data
        data: JSON.stringify(sendData),
        success: function (res) {

        },
        error: function (response, ajaxOptions, thrownError) {

        },

    })
}
function SetBranchAndSellerOnSupcustUpdate(supcust_id, rememberBranch, rememberSeller) {
    var params = { operKey: g_operKey, supcust_id: supcust_id }
    $.ajax({
        url: '/api/SaleSheet/SetBranchAndSellerOnSupcustUpdate',
        type: 'GET',
        contentType: 'application/json',
        processData: true,
        data: params,
        success: function (data) {
            if (data.result === 'OK') {
                if (rememberBranch && data.data[0] && data.data[0].branch_id) {
                    $('#branch_id').jqxInput('val', { value: data.data[0].branch_id, label: data.data[0].branch_name })
                }
                else {
                    $('#branch_id').jqxInput('val', { value: "", label: "" })
                }
                if (rememberSeller && data.data[0] && data.data[0].seller_id && data.data[0].status!=0 ) {
                    $('#seller_id').jqxInput('val', { value: data.data[0].seller_id, label: data.data[0].seller_name })
                }
                else {
                    $('#seller_id').jqxInput('val', { value: "", label: "" })
                }

            }
        }
    })
}
function btnExportExcel() {
    // 只能导出表体
    
    let url = '/api/SaleSheet/JqxExportExcel?operKey=' + g_operKey
    let data = $("#jqxgrid").jqxGrid('exportdata', 'json');
    $("#jqxgrid").jqxGrid('exportdata', 'xls', '销售单', true, null, false, url)

    //$("#jqxgrid").jqxGrid('exportdata', 'xls', '收款单')
}


//-------------------------<分批使用预收款>----------------------------------
var prepay_sheet_infos = {} // 存储输入的信息
var headerInfo = []
var payamounts = []


function getPrepayDetail(sub_id, callback) {
    var that = this
    var supcustID = $('#supcust_id').val().value;
    var data = {
        operKey: g_operKey,
        supcust_id: supcustID,
        sub_id: sub_id,
        companyId: ''
    }
    var prepay_sheet_info = $('#prepay_sheet_info').val()
    if (prepay_sheet_info) this.prepay_sheet_infos = JSON.parse(prepay_sheet_info)
    $.ajax({
        url: `/api/SaleSheet/GetPrepayDetail`,
        type: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function (res) {
            that.headerInfo = []
            if (res !== undefined) {
                res.forEach(item => {
                    // 保留已有的 amount 数据
                    if (that.prepay_sheet_infos[data.sub_id]) {
                        var existingItem
                        that.prepay_sheet_infos[data.sub_id].forEach(items => {
                            if ((Number(items.sub_id) === Number(item.sub_id)) && (Number(items.sheet_id) === Number(item.sheet_id))) existingItem = items
                        })
                        
                        
                        if (existingItem) {
                            item.amount = existingItem.amount;
                        } else {
                            item.amount = 0;
                        }
                    } else {
                        item.amount = 0;
                    }
                    if (item.balance < 0) item.amount = item.balance
                    that.headerInfo.push(item)
                })
                if (Object.keys(that.prepay_sheet_infos).length === 0) {
                    var prepay_sheet_temp = {}
                    prepay_sheet_temp[data.sub_id] = that.headerInfo
                    $('#prepay_sheet_info').val(JSON.stringify(prepay_sheet_temp))
                } else if (!that.prepay_sheet_infos[data.sub_id]){
                    that.prepay_sheet_infos[data.sub_id] = that.headerInfo
                    $('#prepay_sheet_info').val(JSON.stringify(that.prepay_sheet_infos))
                }   
            }
            callback(that.headerInfo);  
        },
        error: function (response, ajaxOptions, thrownError) {

        },

    })
}


//点击选单按钮
function openDialog(event, sub_id, paywayId) {
    // 设置一个具体的高度和宽度，而不是自动调整
    let windowHeight = document.body.offsetHeight - 50
    let windowWidth = document.body.offsetWidth - 80
    $("#prepayDialog").jqxWindow({ isModal: true, modalOpacity: 0.3, height: 300, width: 770, maxHeight: windowHeight, maxWidth: windowWidth, theme: 'summer', autoOpen: false, showCloseButton: true, closeButtonSize: 32, showAnimationDuration: 500, closeAnimationDuration: 500, animationType: 'fade' });
    $("#prepayDialog").jqxWindow('open')
    console.log(paywayId.id)
    this.payamounts.push(paywayId.id)
    getPrepayDetail(sub_id, function (headerInfo) {
        this.headerInfo = headerInfo
        var source = {
            localdata: this.headerInfo,
            datafields: [
                { name: 'sheet_no', type: 'string' },
                { name: 'init_balance', type: 'int' },
                { name: 'balance', type: 'int' },
                { name: 'amount', type: 'int' },
                { name: 'sheet_id', type: 'int' },
                { name: 'remark', type: 'string' },
                { name: 'sub_id', type: 'int' },
                { name: 'sheet_type', type: 'string' }
            ],
            datatype: "array"
        }
        var dataAdapter = new $.jqx.dataAdapter(source);
        $("#prepayDetail").jqxGrid({
            editable: true,
            scrollbarsize: 10,
            source: dataAdapter,
            enablebrowserselection: false,
            selectionmode: 'multiplecellsadvanced',
            enablehover: true,
            copytoclipboardhiddencolumns: true,
            autoheight: true,
            autowidth: true,
            columns: [
                {
                    text: '预收款单', datafield: 'sheet_no', width: 160, cellsalign: 'center', Linkable: true, editable: false,
                    cellsrenderer: function (row, columnfield, value, defaultHtml, columnproperties) {
                        return '<div class="sheet-no" style="width:160px;height:28px;text-align:center;line-height:28px; color: #609afb;">' + value + '</div>';
                    }
                },
                {
                    text: '单据类型', datafield: 'sheet_type', width: 100, cellsalign: 'center', editable: false,
                    cellsrenderer: function (row, columnfield, value, defaultHtml, columnproperties) {
                        var sheetType = ''
                        if (value === 'YS') {
                            sheetType = '预收款'
                        }
                        if (value === 'T') {
                            sheetType = '退货单'
                        }
                        return '<div class="sheet-type" style="width:100px;height:28px;text-align:center;line-height:28px;">' + sheetType + '</div>';
                    }
                },
                { text: '初始金额', datafield: 'init_balance', width: 100, cellsalign: 'center', editable: false },
                {
                    text: '余额', datafield: 'balance', width: 100, cellsalign: 'center', editable: false,
                    cellsrenderer: function (row, columnfield, value, defaultHtml, columnproperties) {
                        if (Number(value) < 0) {
                            return '<div class="balance" style="width:100px;height:28px;text-align:center;line-height:28px;color:red;">' + value + '</div>';
                        }
                        return '<div class="balance" style="width:100px;height:28px;text-align:center;line-height:28px;">' + value + '</div>';
                    }
                },
                {
                    text: '备注', datafield: 'remark', width: 100, cellsalign: 'center', editable: false,
                    cellsrenderer: function (row, columnfield, value, defaultHtml, columnproperties) {
                        return '<div class="prepay-remark"><div class="prepay-remark-cell" style="width:100px;height:28px;line-height:28px;" title="' + value + '">' + value + '</div><span class="prepay-remark-text">' + value + '</span></div>';
                    }
                },
                { text: '金额', datafield: 'amount', width: 100, cellsalign: 'center' }
            ],


        })
    })
    
}

//点击确认按钮
function btnReturnVanConfirm_click() {
    var headerInfo = $('#prepayDetail').jqxGrid('getrows')
    var pay_amount = 0
    var subId = ""
    var preSheets = []
    headerInfo.forEach(item => {
        if (item.amount != '') {
            pay_amount += parseInt(item.amount)
            if (!subId) subId = item.sub_id
        }
        preSheets.push(item)
    })
 
    if (!this.prepay_sheet_infos[subId]) {
        this.prepay_sheet_infos[subId] = [];
    }
    this.prepay_sheet_infos[subId] = preSheets;

    var prepay_sheet_info = $('#prepay_sheet_info').val()
    if (prepay_sheet_info) {
        prepay_sheet_info = JSON.parse(prepay_sheet_info)

    } else {
        prepay_sheet_info = {}
    }
    prepay_sheet_info[subId] = preSheets
    $('#prepay_sheet_info').val(JSON.stringify(prepay_sheet_info))

    if (this.payamounts.length > 0) {
        if (this.payamounts[this.payamounts.length - 1] === 'payway1_id') {
            var payway1_amount = $('#payway1_amount')
            payway1_amount.val(pay_amount)
            //payway1_amount.trigger('input');
            input_amount("#payway1_amount",true)
            
        }
        if (this.payamounts[this.payamounts.length - 1] === 'payway2_id') {
            var payway2_amount = $('#payway2_amount')
            payway2_amount.val(pay_amount, true)
            //payway2_amount.trigger('input');
            input_amount("#payway2_amount", true)
        }
        if (this.payamounts[this.payamounts.length - 1] === 'payway3_id') {
            var payway3_amount = $('#payway3_amount')
            payway3_amount.val(pay_amount, true)
            //payway3_amount.trigger('input');
            input_amount("#payway3_amount", true)
        }
    }
    $("#prepayDialog").jqxWindow('close')
}
//自动分配金额
function input_amount(paywayid,ignorePrepayAssign) {
    var paywayId = '';
    if (paywayid === "#payway1_amount") {
        paywayId = '#payway1_id'
    } else if (paywayid === "#payway2_amount") {
        paywayId = '#payway2_id'
    } else {
        paywayId = '#payway3_id'
    }
    if (!ignorePrepayAssign && window.prepayAccounts) {
        //判断是否是预收款账户
        const isPrepay = window.prepayAccounts.some(item => item.sub_id === $(paywayId).val().value);
        if (isPrepay) {
            console.log(window.prepayAccounts + "")

            let presheetinfo = $('#prepay_sheet_info').val()
            if (presheetinfo) presheetinfo = JSON.parse(presheetinfo)
            else return
            var inputamount = parseInt($(paywayid).val())
            var payamount = 0;
            presheetinfo[$(paywayId).val().value].forEach(item => {
                if (item.amount != '') {
                    payamount += parseInt(item.amount)
                }
            })
            presheetinfo[$(paywayId).val().value].forEach(item => {
                if (inputamount === 0) item.amount = 0
                if (inputamount > 0) {
                    if (inputamount > Number(item.balance)) {
                        inputamount -= item.balance
                        item.amount = item.balance
                    } else {
                        item.amount = inputamount
                        inputamount = 0
                    }
                }

            })
            $('#prepay_sheet_info').val(JSON.stringify(presheetinfo))
        }
    }
  
}

//创建小羽毛
function createFeather(paywayamountId, subId, paywayId) {
    // 获取payway_amount的div元素
    var containerDiv = document.getElementById(paywayamountId);
    console.log(555)
    console.log(paywayId)

    var existingSvg = containerDiv.querySelector('.svgfeather');
    var existingRow_oper = containerDiv.querySelector('.row-oper');
    if (existingSvg) {
        containerDiv.removeChild(existingSvg);
    }
    if (existingRow_oper) {
        containerDiv.removeChild(existingRow_oper);
    }
    // 创建SVG元素
    var svgElement = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    svgElement.setAttribute("class", "svgfeather");
    svgElement.setAttribute("onmousedown", "openDialog(event," + JSON.stringify(subId) + "," + paywayId +")");
    svgElement.setAttribute("height", "15");
    svgElement.setAttribute("width", "15");
    svgElement.style.cursor = "pointer";

    // 创建use元素
    var useElement = document.createElementNS("http://www.w3.org/2000/svg", "use");
    useElement.setAttributeNS("http://www.w3.org/1999/xlink", "xlink:href", "/images/images.svg#feather");

    // 将use元素添加到SVG元素中
    svgElement.appendChild(useElement);

    // 将SVG元素添加到ID为'payid'的div中
    containerDiv.appendChild(svgElement);
}

//关闭弹窗
function closeDialog() {
    $("#prepayDialog").jqxWindow('close')
}

 function updateCostAmount(sheetRows, rowIndex) {//分摊
     var SheetRows = []
     var rows = $('#jqxgrid').jqxGrid('getrows');
     for (var i = 0; i < rows.length; i++) {
         if (!rows[i].item_id) continue
         if (rows[i].quantity <= 0) continue;
         SheetRows.push(rows[i])
     }
     
     let totalAmount = 0
     let totalQuantityS = 0
     let totalQuantityB = 0
     let totalWeight = 0
     if (SheetRows && SheetRows.length) {
         SheetRows.forEach(row => {
             if (isNumber(row.real_price) && isNumber(row.quantity)) {
                 totalAmount += row.real_price * row.quantity
             }
             if (isNumber(row.quantity) && isNumber(row.unit_factor) && row.unit_factor) {
                 totalQuantityS += row.quantity * row.unit_factor
                 if (row.unit_no == row.b_unit_no) {
                     totalQuantityB += row.quantity
                 } else {
                     totalQuantityB += row.quantity / row.unit_factor;
                 }

             }
             if (isNumber(row.weight) && isNumber(row.unit_factor) && isNumber(row.quantity)) {
                 totalWeight += row.weight * row.unit_factor * row.quantity
             }
         })
     }

     //费用分摊
     let saleFeeSheetInfo = $('#saleFeeSheetInfo').val() ? JSON.parse($('#saleFeeSheetInfo').val()) : '';
     if (saleFeeSheetInfo) {
         let totalFee = saleFeeSheetInfo.total_fee_amount;
         let totalFeeApportion = 0;
         if (totalFee) {
             if (totalAmount) {
                 SheetRows.forEach((e, index) => {
                     let allocate_amount = toMoney(e.real_price * e.quantity / totalAmount * totalFee);
                     totalFeeApportion += allocate_amount;
                     $("#jqxgrid").jqxGrid('setcellvalue', e.boundindex ? e.boundindex : index, 'allocate_amount', allocate_amount);
                 });
             } else {
                 SheetRows.forEach((e, index) => {
                     $("#jqxgrid").jqxGrid('setcellvalue', e.boundindex ? e.boundindex : index, 'allocate_amount', 0);
                 });
             }
             
             if (Math.abs(toMoney(totalFee - totalFeeApportion)) >= 0.01) {
                 let lastRowAmt = $("#jqxgrid").jqxGrid('getcellvalue', SheetRows.length - 1, 'allocate_amount')
                 $("#jqxgrid").jqxGrid('setcellvalue', SheetRows.length - 1, 'allocate_amount', toMoney(lastRowAmt + toMoney(totalFee - totalFeeApportion)));
             }
         }
     }
     return SheetRows
 }
function getGridMenuHtml() {
    var divMenu = `<div id='gridMenu'>
                    <ul>
                        <li id='copyRow'>复制</li>
                        <li id='addOtherUnitRow'>加其他单位(+)</li>
                        <li id='hideCostPrice'>隐藏成本(F8)</li>
                        <li id='changeToBorrowItem'>借货商品</li>
                        <li id='exchangeItem'>换货</li>
                        <li id='itemHistory'>历史记录</li>
                        <li id='customerHistory'>历史单据</li>
                    </ul>
                   </div>`;
    return divMenu
}