﻿.bw-toast{
    box-sizing: border-box;
    position: absolute;
    right: calc(50% - 150px);
    /*height:50px;*/
    width: 300px;
    border-radius: 3px;
    color: #fff;
    font-size: 18px;
    font-weight:bold;
    padding: 10px 10px;
    background-color: #444444;
    transition: all .1s;
    z-index:3000;
}
 
html,body{
    height:100%; 
    padding:0px;
    margin:0px;
}
.mycontainer{
    display:flex;
    flex-direction:column;
    height:100%; 
    background-color:white;
        width:100%;
        padding:0px;
        margin:0px;
}
.mypage{
    flex-grow:1;
    width:100%; 
    overflow-y:auto; 
   
} 
.myfooter{
    height:40px;background-color:#30b9ed;
    width:100%;
    display:flex;
}
a:link {text-decoration: none}
 
.inputItem{width:100%;float:none;height:40px;}
.inputItem>div{height:40px;}
.inputItem>div>label{margin-right:10px;}
.inputItem>div:first-child{
    float:left;width:80px;text-align:right; 
}
.inputItem>div:nth-child(2){
    float:left;
    width:calc(100% - 82px);
}
.inputItem input{width:96%; border-top:none;border-left:none;border-right:none;border-radius:0px;border-color:#ccc;font-size:medium;}
 
.labelStyle{
    background-color:#f8f8f8;
}
.item-input-wrap{text-align:right;}
.item-input-wrap input{padding-right:15px; width:180px;text-align:right;}
.item-input-wrap input:focus{text-align:left;}
.card-header{font-weight:bold;text-align:center;}


.inputErr:after {
    content: '请输入此项';
    position: absolute;
    left: 0px;
    top: 22px;
    width: 100px;
    text-align: left;
    color: red;
}

.jqx-widget-content{
    font-family:微软雅黑;
    font-size:14px;
}
body {
    font-family: 微软雅黑;
    font-size:14px;
}


/*.jqx-notification-close-button-info, .jqx-notification-close-button-success, .jqx-notification-close-button-mail, .jqx-notification-close-button-time, .jqx-notification-close-button-error {
    background-image: url('../jqwidgets/jqwidgets/styles/images/close_black.png');
}*/

.jqx-notification-info, .jqx-info {
    background-color: #f88 !important;
}
.jqx-notification{
    border-style:none;
}

.num-text {
    mso-number-format: "\@@";
}

/*.dropDownButton .jqx-widget-content*/
.jqx-dropdownbutton-popup .chkbox {
    /* 对片区下拉树进行样式设置 */
    position: static !important; /* 如果勾选框不设置position，继承的absolute会无法和滚动条一起滑动 */
    display: inline-block; /* 和文字对齐 */
    vertical-align: middle;
    margin-top: 0px !important;
    margin-left: 4px !important;
    line-height: 16px !important; /* 设置行高让文字和勾选框水平对齐 */
    /*  padding: 3px 0px;   /* 配合文字有一个padding */ */
}

.jqx-dropdownbutton-popup .jqx-tree-item {
    /* 对片区下拉树进行样式设置 */
    vertical-align: middle; /* 和勾选框对齐 */
    margin-left: 8px !important; /* 与勾选框的间距 */
    line-height: 16px !important; /* 设置行高让文字和勾选框水平对齐 */
}