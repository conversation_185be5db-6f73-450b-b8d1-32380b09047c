using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using NPOI.POIFS.Storage;
using System.Dynamic;
using System.Text;
using System.Text.Encodings.Web;
using System.Web;
using ArtisanManage.WeChat.Bean.TemplateMessage;
using ArtisanManage.WeChat.Conf;
using ArtisanManage.WeChat.Servlet;
using ArtisanManage.WeChat.Utils;
using Newtonsoft.Json.Linq;
using System.Net.Http;
using ArtisanManage.MyCW;
using NPOI.SS.Formula.Functions;
using ArtisanManage.CwPages;
using System.ComponentModel.Design;
using ArtisanManage.Pages.WeChat.SheetPages;
using NPOI.SS.Formula.Eval;
using NPOI.SS.UserModel;
using ArtisanManage.Pages.CwPages;
using ArtisanManage.YingJiangMallMini.Dao;
using ArtisanManage.YingJiangMallMini.Model;
using Newtonsoft.Json;
using System.Diagnostics;
using NPOI.OpenXmlFormats.Spreadsheet;
using Microsoft.CodeAnalysis.Elfie.Diagnostics;
using System.Collections;
using NPOI.SS.Formula.PTG;
using Swashbuckle.AspNetCore.SwaggerGen;
using static ArtisanManage.Pages.Report.AttendanceReportAllViewController;
using Jint.Parser.Ast;
using CSRedis;

namespace ArtisanManage.MyJXC
{

    public enum SHEET_TYPE
    {
        EMPTY,
        /// <summary>
        /// 销售单
        /// </summary>
        [DBValue("X")] SHEET_SALE,
        /// <summary>
        /// 退货单
        /// </summary>
        [DBValue("T")] SHEET_SALE_RETURN,
        /// <summary>
        /// 销售订单
        /// </summary>
        [DBValue("XD")] SHEET_SALE_DD,
        /// <summary>
        /// 借货单
        /// </summary>
        [DBValue("JH")] SHEET_BORROW_ITEM,
        /// <summary>
        /// 还货单
        /// </summary>
        [DBValue("HH")] SHEET_RETURN_ITEM,
        /// <summary>
        /// /// 销售订单
        /// </summary>
        [DBValue("ZWD")] SHEET_PLACEHOLDER_DD,
        /// 退货订单
        /// </summary>
        [DBValue("TD")] SHEET_SALE_DD_RETURN,
        /// <summary>
        /// 采购单
        /// </summary>
        [DBValue("CG")] SHEET_BUY,
        /// <summary>
        /// 采购退货单
        /// </summary>
        [DBValue("CT")] SHEET_BUY_RETURN,
        /// <summary>
        /// 采购订单
        /// </summary>
        [DBValue("CD")] SHEET_BUY_DD,
        /// <summary>
        /// 采购退货订单
        /// </summary>
        [DBValue("CTD")] SHEET_BUY_DD_RETURN,
        /// <summary>
        /// 采购费用分摊单
        /// </summary>
        [DBValue("FYFT")] SHEET_FEE_APPORTION,

        /// <summary>
        /// 调拨单
        /// </summary>
        [DBValue("DB")] SHEET_MOVE_STORE,

        /// <summary>
        /// 对账单
        /// </summary>
        [DBValue("DZ")] SHEET_GET_MONEY_ORDER,
        /// <summary>
        /// 收款单
        /// </summary>
        [DBValue("SK")] SHEET_GET_MONEY,
        /// <summary>
        /// 批量收款单
        /// </summary>
        [DBValue("PLSK")] SHEET_GET_BULK_MONEY,
        /// <summary>
        /// 付款单
        /// </summary>
        [DBValue("FK")] SHEET_PAY_MONEY,
        /// <summary>
        /// 预收款单
        /// </summary>
        [DBValue("YS")] SHEET_PRE_GET_MONEY,
        /// <summary>
        /// 预付款单
        /// </summary>
        [DBValue("YF")] SHEET_PRE_PAY_MONEY,
        /// <summary>
        /// 组装单
        /// </summary>
        [DBValue("ZZ")] SHEET_COMBINE_ITEMS,
        /// <summary>
        /// 拆分单
        /// </summary>
        [DBValue("CF")] SHEET_SPLIT_ITEMS,
        /// <summary>
        /// 组装模型
        /// </summary>
        [DBValue("CM")] SHEET_COMBINE_MODEL,
        /// <summary>
        /// 盘点单
        /// </summary>
        [DBValue("PD")] SHEET_INVENT_INPUT,
        /// <summary>
        /// 盘点盈亏单
        /// </summary>
        [DBValue("YK")] SHEET_INVENT_ADD,
        /// <summary>
        /// 报损单
        /// </summary>
        [DBValue("BS")] SHEET_INVENT_REDUCE,
        /// <summary>
        /// 费用支出单
        /// </summary>
        [DBValue("ZC")] SHEET_FEE_OUT,
        /// <summary>
        /// 
        /// </summary>
        [DBValue("SR")] SHEET_FEE_IN,

        /// <summary>
        /// 订货会
        /// </summary>
        [DBValue("DH")] SHEET_ORDER_ITEM,
        /// <summary>
        /// 订货会调整单
        /// </summary>
        [DBValue("DHTZ")] SHEET_ORDER_ITEM_ADJUST,
        /// <summary>
        /// 陈列协议
        /// </summary>
        [DBValue("CX")] SHEET_DISPLAY_AGREEMENT,
        /// <summary>
        /// 陈列协议调整单
        /// </summary>
        [DBValue("CXTZ")] SHEET_DISPLAYAGREEMENT_ADJUST,
        /// <summary>
        /// 调价单
        /// </summary>
        [DBValue("TJ")] SHEET_PRICE_ADJUST,
        /// <summary>
        /// 采购调价单
        /// </summary>
        [DBValue("CGTJ")] BUY_SHEET_PRICE_ADJUST,
        /// <summary>
        /// 成本调价单
        /// </summary>
        [DBValue("CBTJ")] SHEET_COST_PRICE_ADJUST,
        /// <summary>
        /// 特价审批单
        /// </summary>
        [DBValue("TJSP")] SHEET_SPECIAL_PRICE,
        /// <summary>
        /// 装车单
        /// </summary>
        [DBValue("ZHC")] SHEET_ASSIGN_VAN,
        /// <summary>
        /// 回库单
        /// </summary>
        [DBValue("HK")] SHEET_BACK_BRANCH_VAN,
        /// <summary>
        /// 换车单
        /// </summary>
        [DBValue("HC")] SHEET_CHANGE_VAN,
        /// <summary>
        /// 回退单
        /// </summary>
        [DBValue("HT")] SHEET_RETREAT_ASSIGN_VAN,
        /// <summary>
        /// 发货单
        /// </summary>
        [DBValue("FH")] SHEET_DELIVER_VAN,
        /// <summary>
        /// 财务凭证
        /// </summary>
        [DBValue("FV")] FINANCIAL_VOUCHER,
        /// <summary>
        /// 转账单
        /// </summary>
        [DBValue("TR")] SHEET_CASH_BANK_TRANSFER,
        /// <summary>
        /// 贷款单
        /// </summary>
        [DBValue("DK")] SHEET_LOAN_MONEY,
        /// <summary>
        /// 还贷款单
        /// </summary>
        [DBValue("HDK")] SHEET_REPAY_MONEY,
        /// <summary>
        ///其他入库单
        /// </summary>
        [DBValue("RK")] SHEET_STOCK_IN,
        /// <summary>
        /// 其他出库单
        /// </summary>
        [DBValue("CK")] SHEET_STOCK_OUT,
        /// <summary>
        ///期初库存单
        /// </summary>
        [DBValue("QCKC")] SHEET_OPENING_STOCK_IN,


        /// <summary>
        ///门店库存上报单
        /// </summary>
        [DBValue("SS")] SHEET_STORE_STOCK,
        /// <summary>
        /// 交账单
        /// </summary>
        [DBValue("JZ")] SHEET_CHECK_ACCOUNT,
        /// <summary>
        /// 打印-交账单-来源
        /// </summary>
        [DBValue("JZ_SUMMARY")] SHEET_CHECK_ACCOUNT_SUMMARY,
        /// <summary>
        /// 打印-交账单-支付方式
        /// </summary>
        [DBValue("JZ_PAYWAY")] SHEET_CHECK_ACCOUNT_PAYWAY,
        /// <summary>
        /// 打印-交账单-单据列表
        /// </summary>
        [DBValue("JZ_SHEETLIST")] SHEET_CHECK_ACCOUNT_SHEETLIST,
        /// <summary>
        /// 打印-交账单-商品列表
        /// </summary>
        [DBValue("JZ_ITEMLIST")] SHEET_CHECK_ACCOUNT_ITEMLIST,

        /// <summary>
        /// 标签打印单
        /// </summary>
        [DBValue("BQ")] SHEET_LABEL_PRINT,
        /// <summary>
        /// 标签打印单
        /// </summary>
        [DBValue("QTF")] SHEET_ARREARS_GRANT,
        /// <summary>
        /// 标签打印单
        /// </summary>
        [DBValue("QTS")] SHEET_ARREARS_REVOKE,
        /// <summary>
        /// 补差单
        /// </summary>
        [DBValue("BC")] SHEET_PRICE_REBATE,
        /// <summary>
        /// 销售费用分摊单
        /// </summary>
        [DBValue("XSFY")] SHEET_SALE_FEE_APPORTION,
        /// <summary>
        /// 补贴单
        /// </summary>
        [DBValue("BT")] SHEET_PRICE_ALLOWANCE,
    }
    public class SheetRowBase : ICloneable
    {
        public SheetRowBase()
        {

        }

        [SaveToDB][FromFld] public virtual int inout_flag { get; set; } = 0;
        [SaveToDB] public virtual int row_index { get; set; } = 1;

        [SaveToDB][FromFld] public virtual string remark { get; set; }

        [FromFld] public virtual string sheet_id { get; set; }

       
		//[FromFld("m.sheet_no")] public string sheet_no { get; set; }

		public object Clone()
        {
            return this.MemberwiseClone(); //浅复制
        }
    }
    public class CInfoForApproveBase
    {//基础审批信息的容器，存储与审批相关的不同类型的信息，比如错误消息、微信信息、账户余额等
        //CInfoForApproveBase 类提供了一个基础框架，用于在审批流程中处理和存储关键信息。它主要聚焦于财务数据和相关的业务数据，适用于处理和记录审批过程中遇到的各种情况，包括错误处理、账户管理和业务周期记录
        public string ErrMsg = "";
        public List<ExpandoObject> WeChatInfo = new List<ExpandoObject>();//WeChatInfo 是一个动态类型列表，用于存储与微信相关的信息，ExpandoObject 允许在运行时动态添加和删除成员，使得这个列表非常灵活，可以根据需要存储各种微信相关的数据
        public string ArrearsBalance = "";
        public string FirstArrearsTime = "",FirstArrearsSheetID="", FirstArrearsSheetNo="", FirstArrearsSheetType = "";
        
        public decimal AccountHistoryArrearsChange = 0;
        public Dictionary<int, decimal> AccountHistoryPrepayChange = null;

        public decimal SupArrearsPendAmount = 0;//供应商应收款中的待处理（悬挂）金额
        public decimal OrderLeftAmount = 0;//订单剩余金额
        public bool SellerHasMaxArrears = false;//销售人员是否有最大应收款限额
        public string ClientIdForAcct = "";//存储与会计相关的客户标识
        public string BizStartPeriod = "";//记录业务开始的时间周期或阶段
        public string OrderSupCustID = "";//记录销售订单欠款的客户，方便在转销售单更改客户后，正确地更新pend_amount
    }
    public enum LOAD_PURPOSE
    {
        SHOW_OR_APPROVE,
        SHOW,
        APPROVE

    }

    public partial class SheetBase<TROW> where TROW : SheetRowBase, new()
    {
        internal IHttpClientFactory _httpClientFactory = null;
        protected LOAD_PURPOSE LoadPurpose;
       
        [JsonIgnore] public string MainTable { get; set; } = "";
        [JsonIgnore] public string DetailTable { get; set; } = "";
        [JsonIgnore] public bool FIXING_ARREARS = false;
        [JsonIgnore] public bool SYNCHRONIZE_SHEETS = false;
        //public int ItemInoutFlag = 0;
        protected CInfoForApproveBase InfoForApprove = null;

        [SaveToDB][IDField][FromFld] public virtual string sheet_id { get; set; } = "";
        [SaveToDB][FromFld] public virtual string sheet_no { get; set; } = "";
        [SaveToDB][FromFld] public string company_id { get; set; } = "";
        private string _company_name = "";
        public string company_name
        {
            get
            {
                return _company_name;
            }
            set
            {
                _company_name = value;
            }
        }
        public string SheetIdFld
        {
            get
            {
                string idFld = "sheet_id";
                if (this.sheet_type == SHEET_TYPE.SHEET_ASSIGN_VAN || this.sheet_type == SHEET_TYPE.SHEET_BACK_BRANCH_VAN ||this.sheet_type==SHEET_TYPE.SHEET_CHANGE_VAN||this.sheet_type==SHEET_TYPE.SHEET_RETREAT_ASSIGN_VAN)
                {
                    idFld = "op_id";
                }
                return idFld;
            }
        }
		public string server_uri { get; set; }
		public string company_tel = "", company_address = "";
        private string _sheet_name = "";
        public string sheet_name
        {
            get
            {
                if (_sheet_name != "") return _sheet_name;
                return this.sheet_type switch
                {
                    SHEET_TYPE.SHEET_SALE => "销售单",
                    SHEET_TYPE.SHEET_SALE_RETURN => "退货单",
                    SHEET_TYPE.SHEET_SALE_DD => "销售订单",
                    SHEET_TYPE.SHEET_PLACEHOLDER_DD => "占位单",
                    SHEET_TYPE.SHEET_SALE_DD_RETURN => "退货订单",
                    SHEET_TYPE.SHEET_SALE_FEE_APPORTION => "销售费用分摊单",
                    SHEET_TYPE.SHEET_BUY => "采购单",
                    SHEET_TYPE.SHEET_BUY_RETURN => "采购退货单",
                    SHEET_TYPE.SHEET_BUY_DD => "采购订单",
                    SHEET_TYPE.SHEET_BUY_DD_RETURN => "采购退货订单",
                    SHEET_TYPE.SHEET_FEE_APPORTION => "采购费用分摊单",

                    SHEET_TYPE.SHEET_MOVE_STORE => "调拨单",
                    SHEET_TYPE.SHEET_GET_MONEY => "收款单",
                    SHEET_TYPE.SHEET_GET_BULK_MONEY => "批量收款单",
                    SHEET_TYPE.SHEET_PAY_MONEY => "付款单",
                    SHEET_TYPE.SHEET_INVENT_ADD => "盘点盈亏单",
                    SHEET_TYPE.SHEET_INVENT_REDUCE => "报损单",
                    SHEET_TYPE.SHEET_INVENT_INPUT => "盘点单",

                    SHEET_TYPE.SHEET_ORDER_ITEM => "定货单",
                    SHEET_TYPE.SHEET_ORDER_ITEM_ADJUST => "定货调整单",

                    SHEET_TYPE.SHEET_FEE_OUT => "费用支出单",

                    SHEET_TYPE.SHEET_FEE_IN => "其他收入单",
                    SHEET_TYPE.SHEET_DISPLAY_AGREEMENT => "陈列协议",
                    SHEET_TYPE.SHEET_DISPLAYAGREEMENT_ADJUST => "陈列协议调整单",
                    SHEET_TYPE.SHEET_COST_PRICE_ADJUST => "成本调价单",
                    SHEET_TYPE.SHEET_PRICE_ADJUST => "调价单",
                    SHEET_TYPE.SHEET_ASSIGN_VAN => "装车单",
                    SHEET_TYPE.SHEET_BACK_BRANCH_VAN => "回库单",
                    SHEET_TYPE.SHEET_SPECIAL_PRICE => "特价审批单",
                    SHEET_TYPE.SHEET_CHANGE_VAN => "换车单",
                    SHEET_TYPE.SHEET_RETREAT_ASSIGN_VAN => "回撤单",
                    SHEET_TYPE.SHEET_DELIVER_VAN => "发货单",
                    SHEET_TYPE.SHEET_COMBINE_MODEL => "组装拆分模型",
                    SHEET_TYPE.SHEET_COMBINE_ITEMS => "组装单",
                    SHEET_TYPE.SHEET_SPLIT_ITEMS => "拆分单",
                    SHEET_TYPE.FINANCIAL_VOUCHER => "财务凭证",
                    SHEET_TYPE.SHEET_CASH_BANK_TRANSFER => "转账单",
                    SHEET_TYPE.SHEET_CHECK_ACCOUNT => "交账单",
                    SHEET_TYPE.SHEET_CHECK_ACCOUNT_SUMMARY => "交账单收款来源",
                    SHEET_TYPE.SHEET_CHECK_ACCOUNT_PAYWAY => "交账单支付账户",
                    SHEET_TYPE.SHEET_CHECK_ACCOUNT_SHEETLIST => "交账单单据",
                    SHEET_TYPE.SHEET_CHECK_ACCOUNT_ITEMLIST => "交账单商品",
                    SHEET_TYPE.SHEET_STOCK_IN => "其他入库单",
                    SHEET_TYPE.SHEET_STOCK_OUT => "其他出库单",
                    SHEET_TYPE.SHEET_OPENING_STOCK_IN => "期初库存单",
                    SHEET_TYPE.SHEET_STORE_STOCK => "门店库存上报单",
                    SHEET_TYPE.SHEET_ARREARS_GRANT => "欠条发放单",
                    SHEET_TYPE.SHEET_ARREARS_REVOKE => "欠条回收单",
                    SHEET_TYPE.SHEET_GET_MONEY_ORDER => "对账单",
                    SHEET_TYPE.SHEET_LABEL_PRINT => "标签打印单",
                    SHEET_TYPE.SHEET_PRICE_REBATE => "补差单",
                    _ => "未知单据"
                };
            }
            set
            {
                _sheet_name = value;
            }
        }

        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public dynamic printTemplate = null;
        public string OperKey { get; set; } = "";
        public string OperID { get; set; } = "";

        public bool isRedAndChange { get; set; } = false;
        public string old_sheet_id { get; set; } = "";
        public string review_time { get; set; } = "";
        public string reviewer_id { get; set; } = "";

        //  [SaveToDB] public string oper_id { get; set; } = ""; 
        public virtual SHEET_TYPE sheet_type { get; set; }
        public string SheetType
        {
            get
            {
                return StrFromSheetType(sheet_type);
            }
            set
            {
                sheet_type = SheetTypeFromStr(value);
            }
        }
        public virtual async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            if (bLoadCompanySetting)
                await LoadCompanySetting(cmd);
        }


        // 获取微信关注等信息
        public void SetQQForWeChatInfo(SQLQueue QQ, string supcust_id)
        {
            string sql = @$"
select wu.open_id,wuss.subscribe_content,wu.subscribe_flag subscribewechatflag,wuss.subscribe_flag subscribemsgflag
from info_cust_contact icc
left join wx_user wu on icc.wx_user_id = wu.wx_user_id
left join wx_user_subscribe_setting wuss on wu.wx_user_id = wuss.wx_user_id
            where company_id = '{company_id}' and supcust_id = '{supcust_id}' and wu.open_id is not null and wu.open_id <> ''";
            QQ.Enqueue("weChatInfo", sql);
        }
        public void ReadQQDataForWeChatInfo(string sqlName, CMySbDataReader dr, CInfoForApproveBase info)
        {
            if (sqlName == "weChatInfo")
            {
                info.WeChatInfo = CDbDealer.GetRecordsFromDr(dr, false);
            }
        }
        /// <summary>
        /// 发送模板消息——头部信息，如 单据名称，供应商
        /// </summary>
        /// <returns></returns>
        public virtual string GetWeChatMsgHead()
        {
            return null;
        }
        /// <summary>
        /// 发送模板消息——尾部信息，如 单号、业务员、送货员、备注 共（200个字符）
        /// </summary>
        /// <returns></returns>
        public virtual string GetWeChatMsgTail()
        {
            return null;
        }
        public async Task<JsonResult> SendSheetSimple(CMySbCommand cmd, IHttpClientFactory httpClientFactory, dynamic wechatInfo, string sheetType, string nowDiscAmount, string leftAmount, string payway1Name, string payway1Amount, string payway2Name, string payway2Amount, string payway3Name, string payway3Amount, string supcust_id)
        {
            //string touser,string qRCodeParams = "-yMgV2_PW6sZ5VMuLJH8piNgBj5OR3xCSOLI2Xq8ROM"
            //touser = "oHD04643d9zgHcdMVgQ2wQEQ7wWA";
            if (httpClientFactory == null) return null;

            string templateId = WeChatConfig.TemplateMessageSheetId;

            SheetSimpleTemplateData sheetSimpleTemplate = new SheetSimpleTemplateData();

            sheetSimpleTemplate.template_id = templateId; // 模板ID

            string openPageUrl = "";
            if (this.SheetType == "X" || this.SheetType == "T" || this.SheetType == "DH")
            {
                openPageUrl = WeChatConfig.TemplateMessageSheetPagesSaleSheet;
            }
            else if (this.SheetType == "YS")
            {
                openPageUrl = WeChatConfig.TemplateMessageSheetPagesPreSheet;
            }
            else if (this.SheetType == "SK")
            {
                openPageUrl = WeChatConfig.TemplateMessageSheetPagesGetArrearsSheet;
            }

            MallMiniSettingDao mallMiniSettingDao = new MallMiniSettingDao(cmd);
            //dynamic miniAppSetting = await mallMiniSettingDao.GetMallSettingInfoDao(this.company_id);
            string miniAppId = WeChatConfig.MINIAPPID;
            // if (miniAppSetting != null)
            // {
            //     miniAppId = miniAppSetting.mini_app_id;
            // }

            sheetSimpleTemplate.url = @$"{openPageUrl}?sheetID={this.sheet_id}&sheetType={this.SheetType}&companyID={this.company_id}&companyName={this.company_name}"; // 模板跳转链接
            sheetSimpleTemplate.miniprogram = new MiniProgram(miniAppId, @$"pages/login/login?mini_action={MallMiniAction.OFFICIALACCOUNTPUSHSHEET}&sheet_id={this.sheet_id}&sheet_type={this.SheetType}&company_id={this.company_id}&oper_id={this.OperID}&supcust_id={supcust_id}");  //跳小程序所需数据
            //sheetSimpleTemplate.miniprogram = new MiniProgram("","");  //跳小程序所需数据
            // string sheetTypeName =  this.sheetTypeName; // 单据类型 
            // string supplier = this.s;   // 供货商 
            // string sheetStatus = this.sheetStatus; // 单据状态

            string first = $@"您有新的【单据】来自【供应商】，请注意查收";
            first = GetWeChatMsgHead();

            string sheetTime = this.approve_time;            // 下单时间

            string sheetPayAmount = this.total_amount.ToString() + "元";       // 合计

            string sheetDiscAmount = nowDiscAmount + "元"; // 优惠


            string sheetLeftAmount = leftAmount + "元";     // 欠款

            string sheetPayWay1Info = ""; // 支付方式1
            if (payway1Amount != "0") sheetPayWay1Info = @$"【{payway1Name}:{payway1Amount}】";

            string sheetPayWay2Info = "";// 支付方式2
            if (payway2Amount != "0") sheetPayWay2Info = @$"【{payway2Name}:{payway2Amount}】";
            string sheetPayWay3Info = "";// 支付方式3
            if (payway3Amount != "0") sheetPayWay3Info = @$"【{payway3Name}:{payway3Amount}】";

            string remark = "";                                 // 其他若干信息
            remark = GetWeChatMsgTail();


            sheetSimpleTemplate.data = new TemplateDataSheetSimpleInfo(
                new TemplateDataItem(first, ""),
                new TemplateDataItem(sheetTime, ""),
                new TemplateDataItem(sheetPayAmount, ""),
                new TemplateDataItem(sheetDiscAmount, nowDiscAmount == "0" ? "" : "#ff6600"),
                new TemplateDataItem(sheetLeftAmount, leftAmount == "0" ? "" : "#ff6600"),
                new TemplateDataItem($"{sheetPayWay1Info}{sheetPayWay2Info}{sheetPayWay3Info}", ""),
                new TemplateDataItem(remark, "")
            );
            List<Object> tousers = new List<object>();
            foreach (dynamic wechat in wechatInfo)
            {
                string subscribewechatflag = wechat.subscribewechatflag;
                string subscribemsgflag = wechat.subscribemsgflag;
                dynamic subscribe_content = JsonConvert.DeserializeObject(wechat.subscribe_content);
                if (subscribemsgflag == "true")
                {
                    switch (sheetType)
                    {
                        case "X":
                        case "T":
                            string subscribe_content_sheet = subscribe_content.sheet;
                            if (subscribe_content_sheet == "true")
                            {
                                tousers.Add(wechat);
                            }
                            break;
                        case "XD":
                        case "TD":
                            string subscribe_content_order = subscribe_content.order;
                            if (subscribe_content_order == "true")
                            {
                                tousers.Add(wechat);
                            }
                            break;
                        case "YS":
                        case "DH":
                        case "CL":
                            string subscribe_content_preorder = subscribe_content.preorder;
                            if (subscribe_content_preorder == "true")
                            {
                                tousers.Add(wechat);
                            }

                            break;
                        case "SK":
                            string subscribe_content_receipt = subscribe_content.receipt;
                            if (subscribe_content_receipt == "true")
                            {
                                tousers.Add(wechat);
                            }
                            break;
                    }
                }
            }
            JArray jos = new JArray();
            foreach (dynamic touserItem in tousers)
            {
                sheetSimpleTemplate.touser = touserItem.open_id; // 接收者openid
                string data = JsonConvert.SerializeObject(sheetSimpleTemplate);
                dynamic jsonStr = await HttpUtils.HttpPost(httpClientFactory, await WeChatApiURL.MessageTemplateSend(_httpClientFactory), data);
                JObject jo = JsonConvert.DeserializeObject(jsonStr);
                jos.Add(jo);
            }
            return new JsonResult(new { jos });
        }

        [SaveToDB][FromFld] public string red_flag { get; set; } = "";
        [SaveToDB][FromFld] public virtual string red_sheet_id { get; set; } = "";
        //[SaveToDB] [FromFld] public string red_sheet_date { get; set; } = "";

        public virtual decimal total_amount { get; set; }

        public virtual int money_inout_flag { get; set; }
        [SaveToDB][FromFld] public virtual string maker_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string maker_name { get; set; } = "";
        [SaveToDB][FromFld("to_char(t.make_time,'yyyy-MM-dd hh24:mi:ss') as make_time")] public virtual string make_time { get; set; } = "";
        [SaveToDB][FromFld("to_char(t.happen_time,'yyyy-MM-dd hh24:mi:ss') as happen_time")] public string happen_time { get; set; } = "";
        public string happen_date
        {
            get
            {
                return CPubVars.GetDateTextNoTime(happen_time);
            }
        }
        public string make_date
        {
            get
            {
                return CPubVars.GetDateTextNoTime(make_time);
            }
        }
        public string happen_date_cn
        {
            get
            {
                if (happen_date != "")
                {
                    DateTime dt = Convert.ToDateTime(happen_date);
                    return dt.Year.ToString() + "年" + dt.Month.ToString().PadLeft(2, '0') + "月" + (dt.Day).ToString().PadLeft(2, '0') + "日";
                }
                return "";
            }
        }
        internal bool IgnoreRepeatedSubmitCheck = false;
        public bool TempHappenTime { get; set; } = true;
        [SaveToDB][FromFld] public virtual string approver_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string approver_name { get; set; } = "";
        [SaveToDB][FromFld("to_char(t.approve_time,'yyyy-MM-dd hh24:mi:ss') as approve_time")] public virtual string approve_time { get; set; } = "";
        public bool unapproved
        {
            get
            {
               return !approve_time.IsValid(); 
            }
        }
        [SaveToDB][FromFld] public virtual string make_brief { get; set; } = "";
        [SaveToDB][FromFld] public virtual string approve_brief { get; set; } = "";
        [SaveToDB] public virtual string submit_time { get; set; } = "";

        public virtual string print_count { get; set; } = "";

        public string sum_quantity_unit_conv = "";

        public string sum_real_qty_unit = "";
		public string sum_quantity_unit_conv_real = "";

		public bool IsSum = false;
        public enum SumType
        {
            NotSum, AllSum, BigUnitSum
        }
        public SumType sumType;
        public bool IsFromWeb { get; set; } = false;
        public List<TROW> SheetRows = new List<TROW>();
        public List<TROW> MergedSheetRows = new List<TROW>();
        public List<TROW> MergedSheetRowByBatchAndItem = new List<TROW>();
        public dynamic company_setting = null;
        public SheetBase(string mainTable, string detailTable, LOAD_PURPOSE loadPurpose)
        {
            MainTable = mainTable;
            DetailTable = detailTable;
            LoadPurpose = loadPurpose;
        }
        private string GetFieldsFromClassType(Type tp)
        {
            string flds = "";
            PropertyInfo[] props = tp.GetProperties();
            foreach (PropertyInfo p in props)
            {
                dynamic dAttr = p.GetCustomAttribute(typeof(FromFld));
                string fromFld = null;
                bool load = true;
                if (dAttr != null) load = Convert.ToBoolean(dAttr.Load);
                if (!load) continue;
                if (dAttr != null) fromFld = dAttr.Field.ToString();

                if (fromFld != null)
                {
                    LOAD_PURPOSE Purpose = (LOAD_PURPOSE)dAttr.Purpose;

                    if (Purpose == LOAD_PURPOSE.SHOW && LoadPurpose != LOAD_PURPOSE.SHOW) continue;
                    if (Purpose == LOAD_PURPOSE.APPROVE && LoadPurpose != LOAD_PURPOSE.APPROVE) continue;
                    string prefix = "";
                    if (Purpose == LOAD_PURPOSE.SHOW_OR_APPROVE)
                    {
                        if (!fromFld.Contains("."))
                            prefix = $"t.";
                    }
                    if (flds != "") flds += ",";
                    if (fromFld == "")
                        flds += prefix + p.Name;
                    else if (fromFld.StartsWith("tochar"))
                        flds += fromFld;
                    else
                    {
                        flds += prefix + fromFld;
                        // if(!fromFld.Contains(" "))
                        if (!fromFld.EndsWith(" " + p.Name))
                        {
                            flds += " " + p.Name;
                        }
                    }

                }
            }
            return flds;
        }


        [JsonIgnore] public string MainLeftJoin { get; set; } = "";
        [JsonIgnore] public string DetailLeftJoin { get; set; } = "";
        private string GetSheetIDField()
        {
            string fromFld = "";
            PropertyInfo[] props = this.GetType().GetProperties();
            foreach (PropertyInfo p in props)
            {
                if (p.Name == "sheet_id")
                {
                    dynamic dAttr = p.GetCustomAttribute(typeof(FromFld));
                    if (dAttr != null) fromFld = dAttr.Field.ToString();
                    break;
                }
            }
            if (fromFld == "") fromFld = "sheet_id";
            return fromFld;
        }
        public Dictionary<string, string> Variables = null;
        public virtual async Task BeforeLoad(CMySbCommand cmd, string companyID, string sheetID)
        {

        }
		public virtual async Task AfterLoad(CMySbCommand cmd, string companyID, string sheetID)
		{

		}
		public virtual async Task<CInfoForApproveBase> Load(CMySbCommand cmd, string companyID, string sheetID, bool bForRed = false)
        {
            this.company_id = companyID;

            if (sheetID == null) sheetID = "";
            this.sheet_id = sheetID;

            if (sheet_id != "")
            {
                await BeforeLoad(cmd, companyID, sheetID);
                string sql;
                SQLQueue QQ = new SQLQueue(cmd);
                string flds = GetFieldsFromClassType(this.GetType());
                string sheetIDField = this.GetSheetIDField();
                sql = $"select {flds} from {MainTable} t {MainLeftJoin} where t.company_id={companyID} and t.{sheetIDField}={sheet_id};";
                sql = sql.Replace("~sheet_id", sheet_id).Replace("~SHEET_ID", sheet_id, StringComparison.OrdinalIgnoreCase);
                sql = sql.Replace("~company_id", companyID, StringComparison.OrdinalIgnoreCase);
                if (Variables != null)
                {
                    foreach (var kp in Variables)
                    {
                        sql = sql.Replace("VAR_" + kp.Key, kp.Value, StringComparison.OrdinalIgnoreCase);
                    }
                }
                QQ.Enqueue("main", sql);
                if (DetailTable != "")
                {
                    flds = GetFieldsFromClassType(typeof(TROW));
                    sql = $"select {flds} from {DetailTable} t {DetailLeftJoin} where t.company_id={companyID} and t.{sheetIDField}={sheet_id} order by row_index;";
                    sql = sql.Replace("~sheet_id", sheet_id).Replace("~SHEET_ID", sheet_id, StringComparison.OrdinalIgnoreCase);
                    sql = sql.Replace("~company_id", companyID, StringComparison.OrdinalIgnoreCase);
                    if (Variables != null)
                    {
                        foreach (var kp in Variables)
                        {
                            sql = sql.Replace("VAR_" + kp.Key, kp.Value, StringComparison.OrdinalIgnoreCase);
                        }
                    }
                    QQ.Enqueue("detail", sql);
                }
	 
				sql = @$"SELECT server_uri from g_company gc left join g_server gs on gc.server_id=gs.server_id where gc.company_id ={companyID};";
				QQ.Enqueue("companyInfo", sql);
				CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    string sqlName = QQ.Dequeue();
                    if (sqlName == "main")
                    {
                        //Type T = this.GetType();

                        if (dr.Read())
                        {
                            InvokeStaticMethod(typeof(CDbDealer), "SetObjectByDr", new Type[] { this.GetType() }, new object[] { dr, this, false });
                        }

                    }
                    else if (sqlName == "detail")
                    {
                        List<TROW> rows = CDbDealer.GetRecordsFromDr<TROW>(dr, false);
                        SheetRows.Clear();
                        foreach (TROW r in rows) SheetRows.Add(r);
                    }
					else if (sqlName == "companyInfo")
					{
						dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
						this.server_uri = rec.server_uri;
					}
				}
                QQ.Clear();
                if (bForRed)
                {
                    red_flag = "2";
                    money_inout_flag *= -1;
                    foreach (var sheetRow in SheetRows)
                    {
                        sheetRow.inout_flag *= -1;
                    }
                }

                //if (LoadPurpose == LOAD_PURPOSE.APPROVE || red_flag == "2")
                if (LoadPurpose == LOAD_PURPOSE.APPROVE || bForRed)
                {
                    QQ = new SQLQueue(cmd);
                    GetInfoForApprove_SetQQ(QQ);
                    if (QQ.Count > 0)
                    {
                        dr = await QQ.ExecuteReaderAsync();
                        while (QQ.Count > 0)
                        {
                            string sqlName = QQ.Dequeue();
                            GetInfoForApprove_ReadData(dr, sqlName, bForRed);
                        }
                        QQ.Clear();
                    }

                }
				await AfterLoad(cmd, companyID, sheetID);

			}
            else
            {
				string sql = @$"SELECT server_uri from g_company gc left join g_server gs on gc.server_id=gs.server_id where gc.company_id ={companyID};";
                cmd.CommandText = sql;
                object ov=await cmd.ExecuteScalarAsync();
                if (ov != null && ov != DBNull.Value)
                {
                    this.server_uri = ov.ToString();
                }
			}
            return (CInfoForApproveBase)InfoForApprove;
        }

        public async Task<object> LoadMultiSheets1<TSHEET>(CMySbCommand cmd, string companyID, string sheetIDs, string sortColumn, string sortDirection, LOAD_PURPOSE loadPurpose = LOAD_PURPOSE.SHOW) where TSHEET : SheetBase<TROW>
        {
            this.company_id = companyID;
            if (string.IsNullOrEmpty(sheetIDs)) throw new Exception("sheetIDs should be specified");
            List<object> lstSheets = new List<object>();

            Dictionary<string, TSHEET> dicSheets = new Dictionary<string, TSHEET>();

            if (sheetIDs != "")
            {
                string sql;
                SQLQueue QQ = new SQLQueue(cmd);
                string flds = GetFieldsFromClassType(this.GetType());
                if (!string.IsNullOrEmpty(sortColumn))
                {
                    if (flds.Contains(sortColumn))
                    {
                        if (sortDirection == "desc") sortColumn += " desc";
                        sortColumn += ",";
                    }
                    else sortColumn = "";
                }

                sql = $"select {flds} from {MainTable} t {MainLeftJoin} where t.company_id={companyID} and t.sheet_id in ({sheetIDs}) order by {sortColumn}sheet_id;";
                // sql = sql.Replace("~sheet_id", sheet_id);
                QQ.Enqueue("main", sql);
                if (DetailTable != "")
                {
                    flds = GetFieldsFromClassType(typeof(TROW));
                    sql = $"select {flds} from {DetailTable} t {DetailLeftJoin} where t.company_id={companyID} and t.sheet_id in ({sheetIDs}) order by row_index;";
                    sql = sql.Replace("~company_id", companyID, StringComparison.OrdinalIgnoreCase);
                    QQ.Enqueue("detail", sql);
                }

                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    string sqlName = QQ.Dequeue();
                    if (sqlName == "main")
                    {
                        //SheetBase<TROW> sheet = new SheetBase<TROW>(MainTable,DetailTable,LOAD_PURPOSE.SHOW);

                        while (dr.Read())
                        {
                            TSHEET sheet = (TSHEET)Activator.CreateInstance(typeof(TSHEET), new object[] { loadPurpose });
                            //  TSHEET sheet = new TSHEET(MainTable, DetailTable, LOAD_PURPOSE.SHOW);

                            InvokeStaticMethod(typeof(CDbDealer), "SetObjectByDr", new Type[] { this.GetType() }, new object[] { dr, sheet, false });

                            dicSheets.Add(sheet.sheet_id, sheet);
                            lstSheets.Add(sheet);
                        }
                    }
                    else if (sqlName == "detail")
                    {
                        List<TROW> rows = CDbDealer.GetRecordsFromDr<TROW>(dr, false);
                        foreach (var row in rows)
                        {
                            if (dicSheets.ContainsKey(row.sheet_id))
                            {
                                var sheet = dicSheets[row.sheet_id];
                                sheet.SheetRows.Add((TROW)row);
                            }
                        }
                    }
                }
                QQ.Clear();
            }
            return lstSheets;
        }
        public async Task<List<TSHEET>> LoadMultiSheets<TSHEET>(CMySbCommand cmd, string companyID, string sheetIDs, string sortColumn, string sortDirection, LOAD_PURPOSE loadPurpose = LOAD_PURPOSE.SHOW) where TSHEET : SheetBase<TROW>
        {
            this.company_id = companyID;
            if (string.IsNullOrEmpty(sheetIDs)) throw new Exception("sheetIDs should be specified");
            List<TSHEET> lstSheets = new List<TSHEET>();
            TSHEET sheetTmp = (TSHEET)Activator.CreateInstance(typeof(TSHEET), new object[] { loadPurpose });
            await sheetTmp.BeforeLoad(cmd, companyID, sheetIDs);
            Dictionary<string, TSHEET> dicSheets = new Dictionary<string, TSHEET>();

            if (sheetIDs != "")
            {
                string sql;
                SQLQueue QQ = new SQLQueue(cmd);
                string flds = GetFieldsFromClassType(this.GetType());
                if (!string.IsNullOrEmpty(sortColumn))
                {
                    

					if (flds.Contains(sortColumn))
                    {
						//sortColumn += " collate \"zh_CN\" ";
						if (sortDirection == "desc") sortColumn += " desc";
                        sortColumn += ",";
                    }
                    else if (sortColumn == "sortByIds")
                    {
                        sortColumn = $"  position(t.sheet_id::text in '{sheetIDs}') ,";
                    }
                    else sortColumn = "";
                }

                sql = $"select {flds} from {MainTable} t {MainLeftJoin} where t.company_id={companyID} and t.sheet_id in ({sheetIDs}) order by {sortColumn} t.happen_time desc, t.sheet_id desc;";
                // sql = sql.Replace("~sheet_id", sheet_id);
                sql = sql.Replace("~company_id", companyID, StringComparison.OrdinalIgnoreCase);
                if (sheetTmp.Variables != null)
                {
                    foreach (var kp in sheetTmp.Variables)
                    {
                        sql = sql.Replace("VAR_" + kp.Key, kp.Value, StringComparison.OrdinalIgnoreCase);
                    }
                }

                QQ.Enqueue("main", sql);
                if (DetailTable != "")
                {
                    flds = GetFieldsFromClassType(typeof(TROW));
                    sql = $"select {flds} from {DetailTable} t {DetailLeftJoin} where t.company_id={companyID} and t.sheet_id in ({sheetIDs}) order by row_index;";

                    sql = sql.Replace("~company_id", companyID, StringComparison.OrdinalIgnoreCase);
                    if (sheetTmp.Variables != null)
                    {
                        foreach (var kp in sheetTmp.Variables)
                        {
                            sql = sql.Replace("VAR_" + kp.Key, kp.Value, StringComparison.OrdinalIgnoreCase);
                        }
                    }

                    QQ.Enqueue("detail", sql);
                }

                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    string sqlName = QQ.Dequeue();
                    if (sqlName == "main")
                    {
                        //SheetBase<TROW> sheet = new SheetBase<TROW>(MainTable,DetailTable,LOAD_PURPOSE.SHOW);

                        while (dr.Read())
                        {
                            TSHEET sheet = (TSHEET)Activator.CreateInstance(typeof(TSHEET), new object[] { loadPurpose });
                            //  TSHEET sheet = new TSHEET(MainTable, DetailTable, LOAD_PURPOSE.SHOW);
                            InvokeStaticMethod(typeof(CDbDealer), "SetObjectByDr", new Type[] { this.GetType() }, new object[] { dr, sheet, false });

                            dicSheets.Add(sheet.sheet_id, sheet);
                            lstSheets.Add(sheet);
                            sheet.Init();
                        }
                    }
                    else if (sqlName == "detail")
                    {
                        List<TROW> rows = CDbDealer.GetRecordsFromDr<TROW>(dr, false);
                        foreach (var row in rows)
                        {
                            if (dicSheets.ContainsKey(row.sheet_id))
                            {
                                var sheet = dicSheets[row.sheet_id];
                                sheet.SheetRows.Add((TROW)row);
                            }
                        }
                    }
                }
                QQ.Clear();
            }
            return lstSheets;
        }

        public virtual string Init()
        {
            if (!string.IsNullOrEmpty(OperKey))
            {
                Security.GetInfoFromOperKey(OperKey, out string companyID, out string operID);
                this.OperID = operID;
                this.company_id = companyID;
            }



            if (SheetType != "")
            {
                sheet_type = SheetTypeFromStr(SheetType);
            }
            int inoutFlag = 0;
            if (sheet_type == SHEET_TYPE.EMPTY)
            {
                throw (new Exception("单据未指定类型"));
            }
            switch (sheet_type)
            {
                case SHEET_TYPE.SHEET_SALE:
                case SHEET_TYPE.SHEET_SALE_DD:
                case SHEET_TYPE.SHEET_PLACEHOLDER_DD:
                case SHEET_TYPE.SHEET_BUY_RETURN:
                    money_inout_flag = 1;
                    inoutFlag = -1; break;
                case SHEET_TYPE.SHEET_BUY:
                case SHEET_TYPE.SHEET_BUY_DD:
                case SHEET_TYPE.SHEET_SALE_RETURN:
                case SHEET_TYPE.SHEET_SALE_DD_RETURN:
                    inoutFlag = 1;
                    money_inout_flag = -1;
                    break;
                case SHEET_TYPE.SHEET_PRE_GET_MONEY:
                    money_inout_flag = 1;
                    break;
                case SHEET_TYPE.SHEET_PRE_PAY_MONEY:
                    money_inout_flag = -1;
                    break;
                case SHEET_TYPE.SHEET_GET_MONEY:
                    money_inout_flag = 1;
                    break;
                case SHEET_TYPE.SHEET_PAY_MONEY:
                    money_inout_flag = -1;
                    break;
                case SHEET_TYPE.SHEET_FEE_OUT:
                    money_inout_flag = -1;
                    break;
                case SHEET_TYPE.SHEET_FEE_IN:
                    money_inout_flag = 1;
                    break;
                case SHEET_TYPE.SHEET_INVENT_INPUT:
                    inoutFlag = 1;
                    money_inout_flag = -1;
                    break;
                case SHEET_TYPE.SHEET_INVENT_ADD:
                    inoutFlag = 1;
                    break;
                case SHEET_TYPE.SHEET_INVENT_REDUCE:
                    inoutFlag = -1;
                    break;
                case SHEET_TYPE.SHEET_STOCK_IN:
                    inoutFlag = 1;
                    break;
                case SHEET_TYPE.SHEET_STOCK_OUT:
                    inoutFlag = -1;
                    break;
                case SHEET_TYPE.SHEET_OPENING_STOCK_IN:
                    inoutFlag = 1;
                    break;
                case SHEET_TYPE.SHEET_ORDER_ITEM:
                    money_inout_flag = 1;
                    break;
                case SHEET_TYPE.SHEET_DISPLAY_AGREEMENT:
                case SHEET_TYPE.SHEET_FEE_APPORTION:
                    money_inout_flag = 0;
                    inoutFlag = 0;
                    break;
                case SHEET_TYPE.SHEET_CASH_BANK_TRANSFER:
                    money_inout_flag = 1;
                    break;
            }

            if (sheet_type != SHEET_TYPE.SHEET_SPLIT_ITEMS && sheet_type != SHEET_TYPE.SHEET_COMBINE_ITEMS && sheet_type != SHEET_TYPE.SHEET_COMBINE_MODEL)
            {
                foreach (SheetRowBase row in SheetRows)
                {
                    if (row != null) row.inout_flag = inoutFlag;
                }
            }
            return "";
        }
        protected virtual void InitForSave()
        {
            if (maker_id == "" || maker_id == "-1") maker_id = OperID;
            if (string.IsNullOrEmpty(make_time))
            {
                make_time = CPubVars.GetDateText(DateTime.Now);// bMakeNow = true;
            }

            if (string.IsNullOrEmpty(happen_time))
            {
                happen_time = CPubVars.GetDateText(DateTime.Now);
                HappenNow = true;
            }

        }
        public bool HappenNow = false;
        public virtual string GetOtherSaveSQL()
        {
            return "";
        }

        public virtual string GetCancelSQL()
        {
            return "";
        }

        public virtual string GetSaveSQL(bool bForApproveOrRed, out string err)
        {

            //bool bMakeNow = false;
            err = "";

            if (happen_time != "" && !IsImported)
            {
                if(this.red_flag != "2")
                {
                    var tm = Convert.ToDateTime(happen_time); 
                    if (tm < DateTime.Now.AddMonths(-18))//lin
                    {
                        if(company_id!="724" && company_id!= "1274" && company_id!= "423" && company_id != "462")  
                        {
							//err = "不能开18个月前的单子";
							//return err;
						}
                    }
                }
                
            }
            this.make_brief = this.make_brief.Replace("'", "");
            this.approve_brief = this.approve_brief.Replace("'", "");
            //if (approver_id.IsInvalid() || make_time.IsInvalid())

            if (string.IsNullOrEmpty(happen_time))
            {
                throw new Exception("happen_time未指定");
            }

            for (int i = 0; i <= SheetRows.Count - 1; i++)
            {
                if (SheetRows[i] == null)
                {
                    return "第{i+1}行记录为空";
                }
            }

            if (bForApproveOrRed) TempHappenTime = true;//for not save tempHappenTime for less storage
            this.review_time = "";
            this.reviewer_id = "";
            string sqlMain = InvokeStaticMethod(typeof(CDbDealer), "GetSaveSqlFromObject", new Type[] { this.GetType() }, new object[] {company_id, this, MainTable, true, null }).ToString() + ";";
        
           
            if (sqlMain == "")
            {
                err = "未能生成保存单据";
                return "";
            }
            //string json = JsonConvert.SerializeObject(this, Formatting.Indented);
            //Console.WriteLine(json);
            string sqlDetail = "";

            List<CField> lstOtherFlds = new List<CField>();
            string sheetIDField = this.GetSheetIDField();
            lstOtherFlds.Add(new CField("company_id", company_id));
            lstOtherFlds.Add(new CField("happen_time", happen_time));
            lstOtherFlds.Add(new CField(sheetIDField, $"@{sheetIDField}"));

            if (sheet_id != "" && DetailTable != "")
            {

                sqlDetail += $"delete from {DetailTable} where company_id={company_id} and {sheetIDField}={sheet_id};";
            }

            int rowIndex = 1;
            foreach (TROW row in SheetRows)
            {
                row.row_index = rowIndex;
                string sqlRow = CDbDealer.GetSaveSqlFromObject<TROW>(company_id,row, DetailTable, false, lstOtherFlds) + ";";
                sqlDetail += sqlRow;
                rowIndex++;
            }
            string sql;
            string sqlDetailOrOther = sqlDetail;
            string otherSaveSQL = GetOtherSaveSQL();
            if (otherSaveSQL != "")
            {
                sqlDetailOrOther += otherSaveSQL;
            }
            if (sheet_id == "")
            {
                sql = $@"SELECT yj_exeSqlByInsertedRowID('{sqlMain.Replace("'", "''")}','{sqlDetailOrOther.Replace("'", "''")}','@{sheetIDField}');";
            }
            else
            {
                sqlDetailOrOther = sqlDetailOrOther.Replace($"@{sheetIDField}", sheet_id);
                sql = sqlMain + sqlDetailOrOther;
            }
            if (!IsImported && sheet_id == "")
            {
                if (maker_id == "" || maker_id == "-1") maker_id = OperID;
                switch (sheet_type)
                {
                    case SHEET_TYPE.SHEET_SALE:
                        sql += $@"INSERT INTO oper_activity(company_id, oper_id, happen_date, sale_times) VALUES ({company_id}, {maker_id}, '{make_date}', 1) 
                        ON CONFLICT(company_id, oper_id, happen_date) DO UPDATE SET sale_times = oper_activity.sale_times + 1;";
                        break;
                    case SHEET_TYPE.SHEET_SALE_DD:
                        sql += $@"INSERT INTO oper_activity(company_id, oper_id, happen_date, sale_order_times) VALUES ({company_id}, {maker_id}, '{make_date}', 1) 
                        ON CONFLICT(company_id, oper_id, happen_date) DO UPDATE SET sale_order_times = oper_activity.sale_order_times + 1;";
                        break;
                    case SHEET_TYPE.SHEET_BUY:
                        sql += $@"INSERT INTO oper_activity(company_id, oper_id, happen_date, buy_times) VALUES ({company_id}, {maker_id}, '{make_date}', 1) 
                        ON CONFLICT(company_id, oper_id, happen_date) DO UPDATE SET buy_times = oper_activity.buy_times + 1;";
                        break;
                }
            }
            return sql;
        }
         
        private async Task<CallResult> GetNewSheetNo(CMySbCommand cmd)
        {
			OnGettingSheetNo(out string sheetNo, company_setting);
			if (sheetNo.IsValid())
			{
				return new CallResult("OK", "", sheetNo);
			}

			string sheetType = StrFromSheetType(sheet_type);
            string sql = $"select o_tmp,o_company_month_num,o_company_num,o_oper_num,o_oper_no from yj_get_new_sheet_no({company_id},'{sheetType}',{maker_id})";
            cmd.CommandText = sql;
            using (CMySbDataReader dr = await cmd.ExecuteReaderAsync())
            {
                dr.Read();
                CallResult res = GetNewSheetNoByDr(dr);
                dr.Close();
                return res;
            }
        }
        private CallResult GetNewSheetNoByDr(CMySbDataReader dr)
        {
            string sheetType = StrFromSheetType(sheet_type);
            string tmp = "", company_num = "", company_month_num = "", oper_num = "", oper_no = "";

            tmp = CPubVars.GetTextFromDr(dr, "o_tmp");
            company_num = CPubVars.GetTextFromDr(dr, "o_company_num");
            company_month_num = CPubVars.GetTextFromDr(dr, "o_company_month_num");
            oper_num = CPubVars.GetTextFromDr(dr, "o_oper_num");
            oper_no = CPubVars.GetTextFromDr(dr, "o_oper_no");


            if (tmp == "")
            {
                tmp = @"[
{""var"":""sheetType""},
{""var"":""date""}, 
{""var"":""operNo""},
{ ""var"":""pn"",""len"":""2""},
{ ""var"":""cn"",""len"":""3""} 
]";


            }
            JArray arr = JsonConvert.DeserializeObject<JArray>(tmp);
            string sheetNo = "";
            string padContent(string content, int len)
            {
                if (len > 0) content = content.PadLeft(len, '0');
                return content;
            }
            bool hasDate = false, hasCompanyNum = false;
            foreach (JObject o in arr)
            {
                string varName = "", text = "";
                int len = 0;
                dynamic v = o["var"]; if (v != null) varName = v.ToString();
                v = o["text"]; if (v != null) text = v.ToString();
                v = o["len"]; if (v != null) len = Convert.ToInt32(v.ToString());
                if (varName == "sheetType")
                {
                    JObject types = (JObject)o["types"];
                    if (types != null)
                    {
                        v = types[sheetType]; if (v != null) sheetType = (string)v;
                    }
                    sheetNo += sheetType;
                }
                else if (varName == "date")
                {
                    hasDate = true;
                    var dt = DateTime.Now;
                    sheetNo += dt.Year.ToString().Substring(2, 2) + dt.Month.ToString().PadLeft(2, '0') + dt.Day.ToString().PadLeft(2, '0');
                }
                else if (varName == "longDate")
                {
                    hasDate = true;
                    var dt = DateTime.Now;
                    sheetNo += dt.Year.ToString() + "-" + dt.Month.ToString().PadLeft(2, '0') + "-" + dt.Day.ToString().PadLeft(2, '0');
                }
                else if (varName == "operNo")
                    sheetNo += padContent(oper_no, len);
                else if (varName == "pn")
                    sheetNo += padContent(oper_num, len);
                else if (varName == "cn")
                {
                    hasCompanyNum = true;
                    sheetNo += padContent(company_num, len);
                }
                else if (varName == "cmn")
                {
                    hasCompanyNum = true;
                    sheetNo += padContent(company_month_num, len);
                }
                else if (text != "")
                    sheetNo += text;
            }
            string result = "OK", msg = "";
            if (!hasCompanyNum)
            {
                result = "Error"; msg = "单据号中没有全局编号";
            }
            if (!hasDate)
            {
                if (msg != "") msg += ",";
                result = "Error"; msg += "单据号中没有日期部分";
            }
            return new CallResult(result, msg, sheetNo);
        }

      
        protected virtual void NeedUpdateClientHistory(out string supcustID, out bool updateArrears, out string updatePrepaySubIDs)
        {
            supcustID = "";
            updateArrears = false;
            updatePrepaySubIDs = "";

        }
        protected virtual void GetInfoForSave_SetQQ(SQLQueue QQ)
        {
            string sql = @$"select setting,setting->>'companyName' as company_name,setting->>'contactTel' as company_tel,setting->>'companyAddress' as company_address from company_setting where company_id={this.company_id}";
            QQ.Enqueue("company_setting", sql);


            if (sheet_id != "")
            {
                string sheetIDField = this.GetSheetIDField();
                string is_del_fld = "false is_del";
                if (this.SheetType?.ToLower() == "xd" || this.SheetType?.ToLower() == "td")
                {
                    is_del_fld = "is_del";
                }
              
                sql = $"select approve_time,{is_del_fld} from {MainTable} where {sheetIDField}={sheet_id} and company_id = {company_id}";
                QQ.Enqueue("saved_sheet", sql);
                 
            }

            /*if (sheet_no == "")
            {
                string sheetType = StrFromSheetType(sheet_type);
                //var sql = $"select yj_getnewsheetno({company_id},'{sheetType}',{maker_id})";
                sql = $"select o_tmp,o_company_month_num,o_company_num,o_oper_num,o_oper_no from yj_get_new_sheet_no({company_id},'{sheetType}',{maker_id})";
                QQ.Enqueue("sheet_no", sql);

                
            }*/
        }

        protected virtual void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
			if (InfoForApprove == null) InfoForApprove = new CInfoForApproveBase();
            if (sqlName == "company_setting")
            {
                if (dr.Read())
                {
                   // string company_name = CPubVars.GetTextFromDr(dr, "company_name");
                    //string company_tel = CPubVars.GetTextFromDr(dr, "company_tel");
                    //string company_address = CPubVars.GetTextFromDr(dr, "company_address");
                    string setting = CPubVars.GetTextFromDr(dr, "setting");  
                    if (setting != "") company_setting = JsonConvert.DeserializeObject(setting);
                    
                }
            }
            else if (sqlName == "saved_sheet")
            {
                if (!bForRed && !FIXING_ARREARS)
                {
                    string err = "";
                    dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                    if (rec == null)
                    {
                        err = "该单已删除, 不能保存";
                    }
                    else if (((string)rec.is_del).ToLower() == "true")
                    {
                        err = "该单已删除了, 不能保存";
                    }
                    else if (rec.approve_time != "")
                    {
                        err = "该单已审核,不能保存";
                    }
                    InfoForApprove.ErrMsg = err;
                }
                
            }
            /*else if (sqlName == "sheet_no")
            {
                if (dr.Read())
                {
                    //  object ov = dr[0];
                    //  if (ov != DBNull.Value) sheet_no = ov.ToString();
                    CallResult rec = GetNewSheetNoByDr(dr);
                    if (rec.result != "OK") InfoForApprove.ErrMsg = rec.msg;
                    sheet_no = rec.data;
                    OnSheetNoGot(sheet_no, company_setting);

                }
            }*/
        }

        protected virtual void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            if (maker_id == "") maker_id = OperID;
            approver_id = OperID;
            //    if (red_flag != "2" && sheet_id == "") sheet_no = "";
            string sql = "";

            GetInfoForSave_SetQQ(QQ);

            /*if (sheet_no == "")
            {
                string sheetType = StrFromSheetType(sheet_type);
                //var sql = $"select yj_getnewsheetno({company_id},'{sheetType}',{maker_id})";
                sql = $"select o_tmp,o_company_month_num,o_company_num,o_oper_num,o_oper_no from yj_get_new_sheet_no({company_id},'{sheetType}',{maker_id})";
                QQ.Enqueue("sheet_no", sql);

                // logger.Info("in GetInfoForApprove_SetQQ:sql=" + sql);
            }*/
            sql = $"select business_start_period, business_period from g_company where company_id ={this.company_id};";
            QQ.Enqueue("business_period", sql);

           

            if (!HappenNow)
            {
                NeedUpdateClientHistory(out string supcustID, out bool updateArrears, out string updatePrepaySubIDs);
                if (supcustID.IsValid())
                {
                    if (updateArrears)
                    {
                        sql = $"select sum(change_amount) arrears_change_amount from client_account_history where company_id={company_id} and happen_time>'{this.happen_time}' and supcust_id={supcustID} and sub_type='QK';";

                        QQ.Enqueue("client_history_arrears_sum", sql);

                    }

                    if (updatePrepaySubIDs.IsValid())
                    {
                        sql = $"select sub_id, change_amount from client_account_history where company_id={company_id} and happen_time>'{this.happen_time}' and supcust_id={supcustID} and sub_type in ('YS','YF') ;";

                        QQ.Enqueue("client_history_prepay", sql);
                    }


                }
            }
        }
        protected virtual void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed)
        {
            GetInfoForSave_ReadData(dr, sqlName, bForRed);
            if (InfoForApprove != null && InfoForApprove.ErrMsg != "")
            {
                return;
            }
            
            if (sqlName == "business_period")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                string sheetType = StrFromSheetType(sheet_type);
                if (rec.business_start_period == "")
                {
                    if (sheetType == "QCKC")//只有期初库存单是在没启用业务期间的时候不能使用的
                    {
                        InfoForApprove.ErrMsg = $"尚未录入系统启用年月，请联系客服";
                    }

                }
                else//启用了业务期间
                {
                    InfoForApprove.BizStartPeriod = rec.business_start_period;
                    if (string.IsNullOrEmpty(happen_time))
                    {
                        throw new Exception("happen_time没有设置");
                    }

                    DateTime ht = Convert.ToDateTime(happen_time);
                    DateTime bizPeriod = Convert.ToDateTime(rec.business_period);
                    if (sheetType == "QCKC")
                    {
                        if (rec.business_start_period != rec.business_period)
                        {
                            InfoForApprove.ErrMsg = $"业务已结账至【{rec.business_period}】，只能在第一个月录入期初库存，请查阅菜单【设置-业务结账】";
                        }
                    }
                    else
                    {
                        if (ht < bizPeriod && !IsImported)
                        {
                            if (IsRealTimeImported && rec.business_start_period != rec.business_period)
                            {
                                InfoForApprove.ErrMsg = $"业务已结账至【{rec.business_period}】，只能在第一个月导入早于系统启用日的单据，请查阅菜单【设置-业务结账】";
                            }
                            else if (!IsRealTimeImported)
                            {
                                if(sheetType!="XD" && sheetType != "TD" && sheetType!= "TJSP"&& sheetType != "DB"&&sheetType!="ZHC")
                                    InfoForApprove.ErrMsg = $"业务已结账至【{rec.business_period}】，交易日期不能早于该时间，请查阅菜单【设置-业务结账】";
                            }
                        }
                        else if (ht < bizPeriod && IsImported && red_flag!="")
                        {
                            if (ht < Convert.ToDateTime(rec.business_start_period))//正常的导入单据
                            {
                                if (rec.business_start_period != rec.business_period)
                                {
                                    InfoForApprove.ErrMsg = $"业务已结账至【{rec.business_period}】，只能在第一个月红冲早于系统启用日的单据，请在【设置-业务结账】中反结账";
                                }
                                //第一个业务期间，可以红冲启用日之前的业务导入单据
                            }
                            else//导入单据脏数据（是导入单据，时间却晚于启用日，逻辑视作正常开单）
                            {
                                if (sheetType != "XD" && sheetType != "TD" && sheetType != "TJSP" && sheetType != "DB" && sheetType != "ZHC")
                                    InfoForApprove.ErrMsg = $"业务已结账至【{rec.business_period}】，不能红冲交易日期早于该时间的单据，请在【设置-业务结账】中反结账";
                            }
                        }
                        //saveandapprove: isImported=true会全部放过去，在importinfo里分别限制
                        //red: isImported=true且早于当前期间的要限制
                        //IsRealTimeImported=true时，isImported必为false，这种情况允许在第一个月往前开，且受正常开单结账逻辑限制
                    }

                }

            }
            else if (sqlName == "client_history_arrears_sum")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                if (rec != null && rec.arrears_change_amount != "")
                {
                    InfoForApprove.AccountHistoryArrearsChange = CPubVars.ToDecimal(rec.arrears_change_amount);
                }

            }
            else if (sqlName == "client_history_prepay")
            {
                var records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    //InfoForApprove.AccountHistoryPrepayChange
                    if (InfoForApprove.AccountHistoryPrepayChange == null)
                    {
                        InfoForApprove.AccountHistoryPrepayChange = new Dictionary<int, decimal>();
                    }
                    if (InfoForApprove.AccountHistoryPrepayChange.ContainsKey(Convert.ToInt32(rec.sub_id)))
                    {
                        InfoForApprove.AccountHistoryPrepayChange[Convert.ToInt32(rec.sub_id)] += Convert.ToDecimal(rec.change_amount);
                    }
                    else
                    {
                        InfoForApprove.AccountHistoryPrepayChange[Convert.ToInt32(rec.sub_id)] = Convert.ToDecimal(rec.change_amount);
                    }
                    if (InfoForApprove.AccountHistoryPrepayChange.Count == 0)
                    {
                        InfoForApprove.AccountHistoryPrepayChange = null;
                    }
                }

            }
        }
        public void GetAccountHistoryHappenTimePrepayBalance(CInfoForApproveBase info, string supcustID, decimal bal, decimal totalBal, string prepay_sub_id, out string balance, out string totalBalance)
        {
            decimal now_balance_happen_time = bal;
            decimal now_prepay_balance_happen_time = totalBal;
            if (!HappenNow && info.AccountHistoryPrepayChange != null)
            {
                foreach (var kp in info.AccountHistoryPrepayChange)
                {
                    int sub_id = kp.Key;
                    decimal change = kp.Value;
                    if (sub_id.ToString() == prepay_sub_id)
                    {
                        now_balance_happen_time -= change;
                    }
                    now_prepay_balance_happen_time -= change;
                }
            }
            balance = now_balance_happen_time.ToString();
            totalBalance = now_prepay_balance_happen_time.ToString();
        }
        protected virtual async Task<CInfoForApproveBase> GetInfoForSave(CMySbCommand cmd)
        {
            SQLQueue QQ = new SQLQueue(cmd);
            if (sheet_id != "")
            {
                if (!FIXING_ARREARS)
                { 
                    string check_sql = $"select approve_time from {MainTable} where {this.SheetIdFld}={sheet_id} and company_id = {company_id}";
                    QQ.Enqueue("check_sheet", check_sql);
                }
            }

            GetInfoForSave_SetQQ(QQ);
            string errMsg = "";
            if (QQ.Count > 0)
            {
                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                try
                {
                    while (QQ.Count > 0)
                    {
                        string tbl = QQ.Dequeue();
                        if (tbl == "check_sheet")
                        {
                            dynamic checkSheet = CDbDealer.Get1RecordFromDr(dr, false);
                            if (checkSheet != null && checkSheet.approve_time != "")
                            {
                                errMsg = "单据已审核过,不能再次保存";
                                break;
                            }
                        }
                        else
                        {
                            GetInfoForSave_ReadData(dr, tbl, false);
                            if (InfoForApprove != null && InfoForApprove.ErrMsg != "")
                            {
                                errMsg = InfoForApprove.ErrMsg;
                                break;
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    errMsg = "读取数据失败";
                    MyLogger.LogMsg($"in GetInfoForSave. error:${e.Message}, ${e.StackTrace},sql:${QQ.SQL}", company_id, "approve");
                }
                QQ.Clear();
            }

            if (InfoForApprove == null)
            {
                throw new Exception("In GetInfoForSave, InfoForApprove not created");
                //InfoForApprove = new CInfoForApproveBase();
            }

            //GetInfoForApprove_CheckData();
            if (InfoForApprove != null && InfoForApprove.ErrMsg != "")
            {
                errMsg = InfoForApprove.ErrMsg;
            }

            InfoForApprove.ErrMsg = errMsg;
            return InfoForApprove;
        }
        protected virtual async Task<CInfoForApproveBase> GetInfoForApprove(CMySbCommand cmd)
        {
            SQLQueue QQ = new SQLQueue(cmd);
            if (sheet_id != "")
            {
                if (!FIXING_ARREARS)
                { 
					string check_sql = $"select approve_time from {MainTable} where {this.SheetIdFld}={sheet_id} and company_id = {company_id}";
					QQ.Enqueue("check_sheet", check_sql);
                }
            }

            GetInfoForApprove_SetQQ(QQ);
            string errMsg = "";
            if (QQ.Count > 0)
            {
                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                try
                {
                    while (QQ.Count > 0)
                    {
                        string tbl = QQ.Dequeue();
                        if (tbl == "check_sheet")
                        {
                            dynamic checkSheet = CDbDealer.Get1RecordFromDr(dr, false);
                            if (checkSheet != null && checkSheet.approve_time != "")
                            {
                                errMsg = "单据已审核过,不能再次审核";
                                break;
                            }
                        }
                        else
                        {
                            GetInfoForApprove_ReadData(dr, tbl,false);
                            if (InfoForApprove != null && InfoForApprove.ErrMsg != "")
                            {
                                errMsg = InfoForApprove.ErrMsg;
                                break;
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    errMsg = "读取数据失败";
                    MyLogger.LogMsg($"in GetInfoForApprove. error:${e.Message}, ${e.StackTrace},sql:${QQ.SQL}", company_id, "approve");
                }
                QQ.Clear();
            }
            else
            {

            }

            if (InfoForApprove == null)
            {
                throw new Exception("InfoForApprove not created");
                //InfoForApprove = new CInfoForApproveBase();
            }

            //GetInfoForApprove_CheckData();
            if (InfoForApprove != null && InfoForApprove.ErrMsg != "")
            {
                errMsg = InfoForApprove.ErrMsg;
            }

            InfoForApprove.ErrMsg = errMsg;
            return InfoForApprove;
        }
        public virtual string is_imported { get; set; } = "";
        public virtual bool IsImported
        {
            get
            {
                return is_imported != null && is_imported.Equals("true", StringComparison.OrdinalIgnoreCase);
            }
            set
            {
                if (value) is_imported = "true";
                else is_imported = "";
            }
        }
        internal bool IsRealTimeImported = false;
        /*public bool is_imported { get {
                return is_imported;
            }
            set {
                is_imported = value;
            }
        }*/
        protected virtual async Task<string> CheckSaveSheetValid(CMySbCommand cmd)
        {
            if (company_id == "")
                return "请指定公司ID";

            if (!FIXING_ARREARS && !SYNCHRONIZE_SHEETS)
            {
                if (OperID == "") return "请指定操作员";
                if (approve_time != "" && !IsImported && !IsRealTimeImported)
                {
                    return "单据已经审核完成，请勿重复提交";
                }
            }

            if (this.SheetRows != null && this.SheetRows.Count > 0)
            {
                int nRow = 0;
                foreach (var row in this.SheetRows)
                {
                    nRow++;
                    if (row == null)
                    {
                        return $"第{nRow}行明细行为空,请删除该行";
                    }
                }
            }
            if (this.happen_time != null && this.happen_time != "")
            {
                if (!CPubVars.IsDate(this.happen_time))
                {
                    return $"{this.happen_time}不是正确的时间格式";
                }
            }

            return "OK";
        }
        protected virtual async Task<string> CheckSheetValid(CMySbCommand cmd)
        {
            if (happen_time != "")
            {
                if (!CPubVars.IsDate(happen_time))
                {
                    return $"{happen_time}不是正确的时间格式";
                }
                string now = CPubVars.GetDateText(DateTime.Now.Date) + " 23:59";
                string happenTime = happen_time;
                DateTime n = Convert.ToDateTime(now);
                DateTime ap = Convert.ToDateTime(happenTime);
                if (sheet_type != SHEET_TYPE.SHEET_SALE_DD && sheet_type != SHEET_TYPE.SHEET_SALE_DD && sheet_type != SHEET_TYPE.SHEET_OPENING_STOCK_IN)
                {
                    if (n < ap)
                    {
                        if(company_id!="4423" && company_id!= "3631" && company_id!= "1846" && company_id != "1062" && company_id != "4102" && company_id != "3371")//临时放行某些公司
                        {
                            return "交易时间请勿超过当日";
                        }
                       
                    }
                }

            }
            return await CheckSaveSheetValid(cmd);
        }
        /*
        protected virtual async Task<string> CheckSheetRowValid(CMySbCommand cmd)
        {
            //foreach (dynamic row in this.SheetRows)
            //{
            //    if (row.GetType().GetProperty("branch_position") == null || row.branch_position == "0" || row.branch_position==null||row.branch_position=="") continue;
            //    dynamic record = await CDbDealer.Get1RecordFromSQLAsync($"select flow_id from info_branch_position where company_id = {company_id} and branch_position = {row.branch_position} and branch_id = {row.branch_id};", cmd);
            //    if (record == null)
            //    {
            //        return $"{row.branch_name}不存在库位：{row.branch_position_name}";
            //    }
            //}
            return "OK";

        }*/
        protected virtual string GetApproveSQL(CInfoForApproveBase info)
        {
            return "";
        }
        public virtual async Task<JsonResult> ToVoucherRows(CMySbCommand cmd, string sheetID, SheetCwVoucher sheetCwVoucher, Dictionary<string, decimal> payways)
        {
            return new JsonResult(new { result = "OK" });
        }
        public virtual void OnSheetNoGot(string sheetNo,dynamic companySetting)
        {

        }
		public virtual void OnGettingSheetNo(out string sheetNo, dynamic companySetting)
		{
            sheetNo = "";
		}

		public virtual async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info)
        {

        }
        public virtual async Task<string> OnSheetSaved(CMySbCommand cmd, string sheetID)
        {
			return "";
		}
		public virtual async Task<string> OnSheetBeforeSave(CMySbCommand cmd,  CInfoForApproveBase info)
		{
            return "";
		}
		public virtual async Task<string> OnSheetCreated(CMySbCommand cmd, CInfoForApproveBase info)
		{
              return "";
		}
        public virtual async Task<string> OnSheetBeforeApprove(CMySbCommand cmd,  CInfoForApproveBase info)
		{
            return "";
		}
		//public virtual async Task ManageOrderPlaceholderOrder(CMySbCommand cmd, string sheetID, CInfoForApproveBase info)
        //{

        //}

        protected async Task<String> ToVoucher(CMySbCommand cmd, VoucherOperType voucherType)
        {
            string err = "";
            string[] sheetTypeArr = new string[] { "X", "T", "CG", "CT", "YS", "YF", "DH", "DHTZ", "SK", "FK", "ZC", "SR", "CBTJ", "YK", "BS", "TR", "RK", "CK", "FYFT", "DK", "HDK" };
            if (Array.IndexOf(sheetTypeArr, SheetType) <= -1) return err;

            string voucherTypeStr = "";
            dynamic setting = await CDbDealer.Get1RecordFromSQLAsync($"select coalesce(setting ->> 'useAccounting','false') useaccounting, coalesce(setting->>'openAccountPeriod','') openaccountperiod, COALESCE(setting ->>  'accountingPeriod','') accountingperiod, coalesce(setting ->> 'autoCreateVoucher','false') autocreatevoucher, COALESCE(setting ->>  'cwPeriodFromTo','') cwperiodfromto from company_setting where company_id = {company_id}", cmd);
            if (setting == null) return "";
            if (!Convert.ToBoolean(setting.useaccounting)) return "";
            red_sheet_id = red_sheet_id == "" ? "0" : red_sheet_id;//原biz sheet_id

            DateTime cwPeriodStart = Convert.ToDateTime(setting.accountingperiod + "-01");
            DateTime cwPeriodEnd = cwPeriodStart.AddMonths(1).AddSeconds(-1);
            string thisPeriodFromTo = $"{setting.accountingperiod}-01 ~ {cwPeriodEnd.ToString("yyyy-MM-dd")}";
            if (setting.accountingperiod == setting.openaccountperiod && setting.cwperiodfromto != "")
            {
                cwPeriodStart = Convert.ToDateTime(setting.cwperiodfromto.Split(" ~ ")[0]);
                thisPeriodFromTo = setting.cwperiodfromto;
            }
            DateTime happenTime = Convert.ToDateTime(happen_time);//对于红冲来说就是Now

            //1. 先拦截时间（包含没启用业务期间，只用财务期间来扎帐的情况）
            //审核/红冲早于当前财务期间的情况
            if (happenTime < cwPeriodStart)
            {
                voucherTypeStr = voucherType == VoucherOperType.SaveAndApprove ? "审核" : "红冲";
                if (cwPeriodStart <= happenTime)
                {
                    err = $"当前会计期间为【{thisPeriodFromTo}】，如需在该期间前{voucherTypeStr}单据，请在【财务-期末结转】反结账";//1.不管有没有启用业务期间，只要在会计开账日后，当前会计期间前，就提示财务反结账
                }
                else
                {
                    dynamic g_co = await CDbDealer.Get1RecordFromSQLAsync($"select business_start_period, business_period from g_company where company_id={company_id}", cmd);
                    if (g_co.business_period == "")
                    {
                        err = $"财务开账日为【{cwPeriodStart.ToString("yyyy-MM-dd")}】，如需在该时间前{voucherTypeStr}单据，请在【设置-业务结账】开启业务期间";//2.在财务开账日前，未启用业务期间，提示启用
                    }
                    else
                    {
                        return "";//3.在财务开账日前，已启用业务期间，在业务期间前后的判断前面sheetbase已拦截，业务反结账判断在业务结账里，这边不生成凭证直接让单据过
                    }
                }
                await CwLog.Save(company_id, OperID, null, "SheetToVoucher", $"Error: {err}; sheet_type: {sheet_type}; ", cmd);
                return err;
            }

            //如果是导入单据，只有启用财务才会走到这里，仅用上面这段判断财务扎帐，业务扎帐在前面已拦截
            //IsImported表示导入且不影响库存，IsRealTimeImported表示导入且影响库存
            //导入且不影响库存：IsImported=false, sheet_attribute->>'imported'='1'
            //导入且影响库存：IsImported=true, sheet_attribute->>'imported'='1'
            //数据库只看IsImported即可
            if (IsImported)
            {
                return "";
            }

            //2. 再拦截autocreatevoucher=false
            dynamic voucher = await CDbDealer.Get1RecordFromSQLAsync($"select sheet_id,approve_time from cw_voucher_main cvm left join cw_voucher_sheet_mapper cvmap on cvm.company_id=cvmap.company_id and cvm.sheet_id=cvmap.voucher_id where cvm.company_id={company_id} and business_sheet_id={red_sheet_id} and business_sheet_type='{SheetType}' and cvm.red_flag is null;", cmd);//原单据红冲时的原单据凭证
            if (!Convert.ToBoolean(setting.autocreatevoucher))
            {
                if (voucher != null && voucherType == VoucherOperType.Red)//原单据生成凭证后，又关闭单据自动生凭证，再红冲单据（只红冲单据不红冲凭证会造成业财不一致）
                {
                    await CwLog.Save(company_id, OperID, null, "SheetToVoucher", $"Error, create no voucher because having origin voucher but close auto create voucher; red business sheet, origin sheet_id: {red_sheet_id}, sheet_type: {sheet_type}; ", cmd);
                    return "原单据已生成凭证，请先红冲原单关联凭证，或开启【开账-业务单据自动创建凭证】再红冲";
                }
                return "";
            }


            if (voucherType == VoucherOperType.SaveAndApprove)
            {
                Dictionary<string, string> sheetDic = new Dictionary<string, string>();
                sheetDic.Add($"{SheetType} {OperID}", $"{sheet_id} {SheetType}");//用单个单据去调批量生凭证Save
                List<CwBizInfo> sheet_info = new List<CwBizInfo>();//储存在sheet_attribute的信息
                CwBizInfo one_sheet_info = new CwBizInfo();
                one_sheet_info.biz_sheet_type = SheetType;
                one_sheet_info.biz_sheet_id = sheet_id;
                one_sheet_info.biz_sheet_no = sheet_no;
                one_sheet_info.biz_make_brief = make_brief;
                sheet_info.Add(one_sheet_info);
                dynamic jrd = (await CwVoucherController.ToVoucherListFromSheetsSave(cmd, company_id, OperID, sheetDic, sheet_info, "", "SheetToVoucher") as JsonResult).Value;
                if (jrd.result != "OK" && !jrd.msg.StartsWith("成本为0")) err = jrd.msg;//成本为0的报错，默认跳过（该报错仅用于单选成本为0赠品的批量生凭证,）；其他的错才会报出来
            }
            else if (voucherType == VoucherOperType.Red)
            {
                if (voucher == null)//原单据未生成凭证的，直接跳过（本身就是赠品成本为0无法生成的，或之前没开自动生凭证的）
                {
                    await CwLog.Save(company_id, OperID, null, "SheetToVoucher", $"OK, create no voucher because no origin voucher; red business sheet, origin sheet_id: {red_sheet_id}, sheet_type: {sheet_type};  ", cmd);
                    return err;
                }
                SheetCwVoucher sheetCwVoucher = new SheetCwVoucher(MyCW.LOAD_PURPOSE.APPROVE);
                sheetCwVoucher.auto_created = true;
                sheetCwVoucher.happen_time = happen_time;
                sheetCwVoucher.company_id = company_id;
                sheetCwVoucher.OperID = OperID;
                if (voucher.approve_time != null && voucher.approve_time != "")
                {
                    err = await sheetCwVoucher.Red(cmd, company_id, voucher.sheet_id.ToString(), OperID, false);//原凭证已审核
                }
                else
                {
                    err = await sheetCwVoucher.Delete(cmd, company_id, voucher.sheet_id.ToString(), OperID, false);//原凭证未审核
                }

                string log_msg = err == "" ? "OK" : "Error: " + err;
                await CwLog.Save(company_id, OperID, null, "SheetToVoucher", $"{log_msg}; red business sheet, sheet_id: {sheet_id}, sheet_type: {sheet_type}; red voucher, sheet_id: {voucher.sheet_id}; {((voucher.approve_time != null && voucher.approve_time != "") == true ? "create red voucher" : "delete origin voucher because origin voucher not approved")};", cmd);
            }
            return err;//FixData->Approve->VoucherOperType.Approve没有涵盖在上述判断中，所以重算成本价没有重复生成凭证
        }

        public string UpdateCashBankBalance(Dictionary<string, decimal> payways)
        {
            //SheetType: "X", "T", "CG", "CT", "YS", "YF", "DH", "SK", "FK", "ZC", "SR", "TR"
            string sql = "";
            foreach (KeyValuePair<string, decimal> pw in payways)
            {
                int flag = money_inout_flag;
                sql += $@"insert into info_pay_qrcode (company_id,sub_id) select s.company_id, s.sub_id from cw_subject s left join info_pay_qrcode q on s.company_id=q.company_id and s.sub_id=q.sub_id where s.company_id={company_id} and s.sub_id={pw.Key} and q.qrcode_id is null; ";
                sql += $@"insert into cashbank_balance (company_id, sub_id, balance) values ({company_id}, {pw.Key}, {money_inout_flag * pw.Value}) on conflict (company_id, sub_id) do update set balance=coalesce(cashbank_balance.balance,0)+({money_inout_flag * pw.Value}); ";
                sql += $@"insert into cashbank_balance_log (company_id, sub_id, oper_id, make_time, amount, sheet_type, sheet_id) 
                    values ({company_id}, {pw.Key}, {OperID}, '{DateTime.Now}', {money_inout_flag * pw.Value}, '{SheetType}', {sheet_id}); ";
            }

            return sql;
        }


        public virtual string GetSheetCharactor()
        {
#if DEBUG
//不要注释这行代码，为了在审核时防止重复提交，所有单据必须重写GetSheetCharactor
            throw new Exception("必须重写GetSheetCharactor");

#endif
            return "";
        }
        public static List<ApprovingSheet> g_approvingSheets = new List<ApprovingSheet>();
        public class ApprovingSheet
        {
            public string Charactor = "";
            public DateTime ApproveTime = DateTime.Now;
            public bool Done = false;
            public DateTime DoneTime = DateTime.Now;
        }

        public async Task<string> Save(CMySbCommand cmd, bool bAutoCommit = true)
        {
            string sql;
            //if(!maker_id.IsValid())
            //    maker_id = OperID;
            cmd.oper_id = OperID;
            string sError = "";
            if (red_flag != "")
            {
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Info($"In sheetBase.Save,red_flag={red_flag}");
                return "红字单/红冲单不能保存";
            }

            string checkResult = await CheckSaveSheetValid(cmd);
            if (checkResult != "OK") return checkResult;
            
            string redisKey = GetRedisLockKey(out bool hasSheetID);

            if (redisKey != "")
            {
                string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
                await RedisHelper.ExpireAsync(redisKey, 10);
                if (redisValue == "1")
                {
                    return "请勿重复保存!";
                }

            }

            InitForSave();

            cmd.ActiveDatabase = "";
            cmd.company_id = this.company_id;

            /*
            if (sheet_no == "")
            {
             
                CallResult res = await GetNewSheetNo(cmd);
                if (res.result != "OK")
                {
                    return res.msg;
                }
                sheet_no = res.data;
            }*/

            
            var info = await GetInfoForSave(cmd);
            if (info.ErrMsg != "") { 
                sError = info.ErrMsg;
                string errMsg = $"In Save,after GetInfoForSave:{sheet_type},sheet_id{sheet_id},err:{sError}";
                NLogger.Error(errMsg);
                MyLogger.LogMsg(errMsg, company_id);
                goto END; 
            }

            CMySbTransaction tran = null;
            if (bAutoCommit)
                tran = await cmd.Connection.BeginTransactionAsync();

            if (sheet_no == "")
            {
				
               
                CallResult res = await GetNewSheetNo(cmd);

                if (res.result != "OK")
                {
                    return res.msg;
                }
                sheet_no = res.data;
                
			}

            string err = await OnSheetBeforeSave(cmd, info);
            if (err != "")
            {
                sError = err;
                string errMsg = $"In Save,after OnSheetBeforeSave:{sheet_type},sheet_id{sheet_id},err:{sError}";
                NLogger.Error(errMsg);
                MyLogger.LogMsg(errMsg, company_id);
                goto END;
            }

			sql = GetSaveSQL(false, out err);
            if (err != "")
            {
                sError = err;
                string errMsg = $"In Save,after GetSaveSQL:{sheet_type},sheet_id{sheet_id},err:{sError}";
                NLogger.Error(errMsg);
                MyLogger.LogMsg(errMsg, company_id);
                goto END;
            }

            
             

            cmd.CommandText = sql;
            try
            {
                object ov = await cmd.ExecuteScalarAsync();
                if (sheet_id == "")
                {
                    if (ov != null && ov != DBNull.Value)
                    {
                        sheet_id = ov.ToString().Split(",")[0];
                    }
                    err = await OnSheetCreated(cmd, info);
                    if (err != "")
                    {
                        sError = err;
                        string errMsg = $"In Save,after OnSheetCreated:{sheet_type},sheet_id{sheet_id},err:{sError}";
                        NLogger.Error(errMsg);
                        MyLogger.LogMsg(errMsg, company_id);
                        goto END;
                    }
				}
                err = await OnSheetSaved(cmd, this.sheet_id);
                if (err != "")
                {
                    sError = err;
                    string errMsg = $"In Save,after OnSheetSaved:{sheet_type},sheet_id{sheet_id},err:{sError}";
                    NLogger.Error(errMsg);
                    MyLogger.LogMsg(errMsg, company_id);
                    goto END;
                }

                /*//SheetPlaceholderOrder placeholderOrderSheet = await SavePlaceholderOrderSheet(cmd);
				//if (msg == "")
				//{
				//    msg = await placeholderOrderSheet.SaveAndApprove(cmd);
				//}
				//if (info.ErrMsg != "")//注意，这里不能移走，否则OnSheetIDGot里面抛出的错误就不会被捕获到
				//{
				//    if (bAutoCommit && tran != null) tran.Rollback();
				//    sError = info.ErrMsg; goto END;
				//}*/

                if (tran != null) tran.Commit();
            }
            catch (Exception e)
            {
                if (tran != null) tran.Rollback();
                string errMsg = $"In Save,sheet_type:{sheet_type},sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite},sql:{sql}";
                NLogger.Error(errMsg);
                MyLogger.LogMsg(errMsg, company_id);
                sError = "保存发生了错误";
            }
        END:

            if (redisKey != "")
            {
                if (hasSheetID)
                    await RedisHelper.DelAsync(redisKey);
                else
                    await RedisHelper.ExpireAsync(redisKey, 5);

            }

            return sError;
        }
        string GetRedRedisLockKey(string redSheetID)
        {
            string redisKey = "";

            if (redSheetID == "") throw new Exception("redSheetID should not be empty");
            
         
            redisKey = $"sheetRed{this.company_id}{this.SheetType}{redSheetID}"; 
            
            return redisKey;
        }

        string GetRedisLockKey(out bool hasSheetID)
        {
            string redisKey = "";

            if (this.sheet_id != "")
            {
                hasSheetID = true;
                // sheetKey = this.SheetType + this.sheet_id;
                redisKey = $"sheetDuplicate{this.company_id}{this.SheetType}{this.sheet_id}";
            }
            else
            {
                hasSheetID = false;
                string charactor = GetSheetCharactor();
                charactor = Security.MyEncrypt(charactor);

                if (!IsImported && !IgnoreRepeatedSubmitCheck && charactor != "")
                {
                    if (charactor.Length > 20)
                        redisKey = charactor.Substring(0, 20);
                    else redisKey = charactor;
                    redisKey = $"sheetDuplicate{this.company_id}{this.SheetType}{charactor}";
                }
            }
            return redisKey;
        }
        public virtual string GetDataLockKey()
        {
            return "";
        }
        protected SheetBase<TROW> OldSheetToRed =null;
        protected SheetBase<TROW> RedChangeSheet = null;

        public async virtual Task<string> BeforeRedAndChange(CMySbCommand cmd)
        {
            return "";
        }
        public async virtual Task<string> RedAndChange<TSheet>(CMySbCommand cmd, bool bAutoCommit = true) where TSheet:SheetBase<TROW>,new ()
        {
            string sError = "";

            cmd.ActiveDatabase = "";
            CMySbTransaction tran = null;
            //TSheet oldSheet = JsonConvert.DeserializeObject<TSheet>(JsonConvert.SerializeObject(this));
            //SheetBase<TROW> oldSheet = new SheetBase<TROW>(  this.MainTable, this.DetailTable, LOAD_PURPOSE.SHOW);// JsonConvert.DeserializeObject(JsonConvert.SerializeObject(this));
            TSheet oldSheet = new TSheet();// JsonConvert.DeserializeObject(JsonConvert.SerializeObject(this));
            OldSheetToRed = oldSheet;

            sError=await BeforeRedAndChange(cmd);
            if (sError != "") return sError;
            //await oldSheet.Load(cmd,company_id, this.old_sheet_id,true);
            oldSheet.isRedAndChange = true;

            if (bAutoCommit)
                tran = await cmd.Connection.BeginTransactionAsync();
            oldSheet.RedChangeSheet = this;
            if(sError=="")
                sError = await oldSheet.Red(cmd, this.company_id, this.old_sheet_id, this.OperID, "", false);
            if (sError == "")
            {
                sError = await SaveAndApprove(cmd, false);
            }

            if (sError == "")
            {
               
                string statusTb = "";
                string statusFld = "";
                if (",X,T,".Contains("," + this.SheetType + ","))
                {
					statusTb = "sheet_status_sale";
					statusFld = "status"; 
				}
                    
				else if (",XD,TD,".Contains("," + this.SheetType + ","))
                {
					statusTb = "sheet_status_order";
					statusFld = "order_status";
				}
					

                if (statusTb != "")
                {
					if (this.company_setting != null)
					{
						string reservePrintCountOnRedChange = this.company_setting.reservePrintCountOnRedChange;
						if (reservePrintCountOnRedChange != null && reservePrintCountOnRedChange.ToLower() == "true")
						{
							cmd.CommandText = @$"
insert into {statusTb} (company_id,      sheet_id, print_time, sheet_print_count,sum_print_count,{statusFld}) 
                 select company_id,{this.sheet_id},print_time, sheet_print_count,sum_print_count,{statusFld}
                 from {statusTb}
                 where company_id={this.company_id} and sheet_id={this.old_sheet_id} on conflict (sheet_id) do nothing;";
							await cmd.ExecuteNonQueryAsync();
						}
					}
				
				}
                
            }

            if (bAutoCommit && tran != null)
            {
                if (sError == "") tran.Commit();
                else tran.Rollback();
            }

            string redResicLockKey = oldSheet.GetRedRedisLockKey(this.old_sheet_id);
            await RedisHelper.DelAsync(redResicLockKey);//冲改完成后要释放红冲的锁

            return sError;
        }
        public async Task<string> SaveAndApprove(CMySbCommand cmd, bool bAutoCommit = true)
        {
            if (!bAutoCommit && cmd.ActiveDatabase != "")
            {
                throw new Exception("if bAutoCommit false,activeDatabase  should be ''");
            }
            InitForSave();
            cmd.oper_id = OperID;
            string sError = "";
            cmd.ActiveDatabase = "";
            cmd.company_id = this.company_id;
            string checkResult = await CheckSheetValid(cmd);
            if (checkResult != "OK") return checkResult;
           

            if (red_flag != "")
            {
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Info($"In sheetBase.SaveAndApprove,red_flag={red_flag}");
                return "红字单/红冲单不能审核";
            }

            string redisKey = GetRedisLockKey(out bool hasSheetID);

            if (redisKey != "")
            {
               // string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
                int expireSeconds = 30;
                if (hasSheetID) expireSeconds = 120;
                /*await RedisHelper.ExpireAsync(redisKey, expireSeconds);
                if (redisValue == "1")
                {
                    return "请勿重复审核!";
                }*/
               
                bool lockAcquired = await RedisHelper.SetAsync(redisKey, "1", expireSeconds, RedisExistence.Nx);
                if (!lockAcquired)
                {
                    return "请勿重复审核!";
                }
            }

            string dataLockKey = GetDataLockKey();
            if (!IsImported && dataLockKey != "")
            {
                using (var Lock = RedisHelper.Lock(dataLockKey, 10))
                {
                    await approveFunc();
                }
            }
            else
            {
                await approveFunc();
            }

            async Task approveFunc()
            {
                CMySbTransaction tran = null;
                CInfoForApproveBase info = null;
                try
                {
                    //让冲改时红字单时间和产生的新单据的审核时间相差1s，不然库存变化明细表推算会有问题
                    if (approve_time == "" && isRedAndChange) approve_time = CPubVars.GetDateText(DateTime.Now.AddSeconds(1));
                    if (approve_time == "") approve_time = CPubVars.GetDateText(DateTime.Now);
                    approver_id = OperID;

                   

                    info = await GetInfoForApprove(cmd);
                    if (info.ErrMsg != "") { sError = info.ErrMsg; goto END; }

                    //approve_time = CPubVars.GetDateText(DateTime.Now);
                    //approver_id = maker_id;
                  
                    // NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                    //logger.Info("in SaveAndApprove:sql=" + cmd.CommandText);
                }
                catch (Exception e)
                {
                    MyLogger.LogMsg($"In SaveAndApprove,sheet_type:{sheet_type},sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}", company_id);
                    sError = "审核发生错误"; goto END;
                }
                 

                if (bAutoCommit)
                    tran = await cmd.Connection.BeginTransactionAsync(); 

                try
                {
                    
                    cmd.company_id = company_id;

                    if (sheet_no == "")
                    {
                        CallResult res = await GetNewSheetNo(cmd);
                        if (res.result != "OK")
                        {
                            sError = res.msg;
                            goto END;
                        }
                        sheet_no = res.data;
                    }
					sError=await OnSheetBeforeSave(cmd, info);
                    if (sError != "") goto END;

                    sError=await OnSheetBeforeApprove(cmd, info);
                    if (sError != "") goto END;
                    
					string sqlSave = GetSaveSQL(true, out sError);
                    if (sError != "") goto END;

                    string sqlApprove = GetApproveSQL(info);
                    if (info.ErrMsg != "") { sError = info.ErrMsg; goto END; }
                    cmd.CommandText = sqlSave + sqlApprove;

                    object ov = await cmd.ExecuteScalarAsync();
                    if (sheet_id == "")
                    {
                        if (ov != DBNull.Value && ov != null)
                            sheet_id = ov.ToString().Split(',')[0];
                        sError = await OnSheetCreated(cmd, info);
                        if (sError != "") goto END;
					}
                    else
                    {
                        if (ov == DBNull.Value || ov == null)
                        {
                            MyLogger.LogMsg($"In SaveAndApprove,sheet_type:{sheet_type},sheet_id{sheet_id},sheet deleted before approve", company_id);
                            sError = "单据审核前已被删除"; goto END;
                        }
                    }

                    await OnSheetIDGot(cmd, sheet_id, info);
                    if (info.ErrMsg != "")//注意，这里不能移走，否则OnSheetIDGot里面抛出的错误就不会被捕获到
                    {
                        if (bAutoCommit && tran != null) tran.Rollback();
                        sError = info.ErrMsg; goto END;
                    }
                    if (!IsImported)
                    {
                        info.ErrMsg = await ToVoucher(cmd, VoucherOperType.SaveAndApprove);
                        if (info.ErrMsg != "")
                        {
                            if (bAutoCommit && tran != null) tran.Rollback();
                            sError = info.ErrMsg; goto END;
                        }
                    }

                    if (bAutoCommit && tran != null) tran.Commit();
                }                
                catch (Exception e)
                {
                    
                    MyLogger.LogMsg($"In SaveAndApprove,sheet_type:{sheet_type},sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}", company_id);
                    sError = "审核发生了错误";
                }
            END:
                if (bAutoCommit && tran != null && sError!="")
                {
                    try { tran.Rollback(); }
                    catch (Exception) { }
                }
            }


            if (redisKey != "")
            {
                if (sError != "")
                {
                    await RedisHelper.DelAsync(redisKey);
                }
                else if (hasSheetID)
                    await RedisHelper.DelAsync(redisKey);
                else
                    await RedisHelper.ExpireAsync(redisKey, 10); //d将这里注释，可能会出现问题x

            }
            return sError;
        }

        public async virtual Task<string> SaveAndApply(CMySbCommand cmd, bool bAutoCommit = true)
        {
            if (!bAutoCommit && cmd.ActiveDatabase != "")
            {
                throw new Exception("if bAutoCommit false,activeDatabase  should be ''");
            }
            InitForSave();

            string sError = "";
            cmd.ActiveDatabase = "";
            cmd.company_id = this.company_id;
            string checkResult = await CheckSheetValid(cmd);
            if (checkResult != "OK") return checkResult;
          

            if (red_flag != "")
            {
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                logger.Info($"In sheetBase.SaveAndApply,red_flag={red_flag}");
                return "红字单/红冲单不能申请";
            }

            string redisKey = GetRedisLockKey(out bool hasSheetID);

            if (redisKey != "")
            {
                string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
                int expireSeconds = 10;
                if (hasSheetID) expireSeconds = 120;
                if (redisValue == "1")
                {
                    return "请勿重复申请!";
                }
                await RedisHelper.ExpireAsync(redisKey, expireSeconds);
            }
            string dataLockKey = GetDataLockKey();
            if (dataLockKey != "")
            {
                using (var Lock = RedisHelper.Lock(dataLockKey, 10))
                {
                    await applyFunc();
                }
            }
            else
            {
                await applyFunc();
            }

            async Task applyFunc()
            {
                CInfoForApproveBase info = InfoForApprove;
                // CInfoForApproveBase info = null;
                try
                {
                    if (sheet_no == "")
                    {
                        /*string sheetType = StrFromSheetType(sheet_type);
                        sql = $"select yj_getnewsheetno({company_id},'{sheetType}',{maker_id})";
                        cmd.CommandText = sql;
                        object ov = await cmd.ExecuteScalarAsync();
                        if (ov != null && ov != DBNull.Value)
                            sheet_no = ov.ToString();
                        */
                        CallResult res = await GetNewSheetNo(cmd);
                        if (res.result != "OK")
                        {
                            sError = res.result;
                            goto END;
                        }
                        sheet_no = res.data;
                    }
                    // TODO 是否加字段apply_time?
                    //让冲改时红字单时间和产生的新单据的审核时间相差1s，不然库存变化明细表推算会有问题
                    // if (approve_time == "" && isRedAndChange) approve_time = CPubVars.GetDateText(DateTime.Now.AddSeconds(1));


                    // if (approve_time == "") approve_time = CPubVars.GetDateText(DateTime.Now);
                    // TODO 是否加字段 applyier_id 
                    // approver_id = OperID;
                    // info = await GetInfoForApprove(cmd);
                    // TODO GetInfoForApply(cmd)
                    // info = await GetInfoForApprove(cmd);
                    // if (info.ErrMsg != "") { sError = info.ErrMsg; goto END; }

                    //approve_time = CPubVars.GetDateText(DateTime.Now);
                    //approver_id = maker_id;
                    info = await GetInfoForSave(cmd);
                    if (info.ErrMsg != "") { sError = info.ErrMsg; goto END; }
					await OnSheetBeforeSave(cmd, info);
					string sqlSave = GetSaveSQL(false, out sError);
                    if (sError != "") goto END;

                    // string sqlApprove = GetApproveSQL(info);
                    // if (info.ErrMsg != "") { sError = info.ErrMsg; goto END; }
                    cmd.CommandText = sqlSave;
                    // NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                    //logger.Info("in SaveAndApprove:sql=" + cmd.CommandText);
                }
                catch (Exception e)
                {
                    MyLogger.LogMsg($"In SaveAndApply,sheet_type:{sheet_type},sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}", company_id);
                    sError = "申请发生错误"; goto END;
                }
                CMySbTransaction tran = null;
                try
                {
                    if (bAutoCommit)
                        tran = await cmd.Connection.BeginTransactionAsync();

                    object ov = await cmd.ExecuteScalarAsync();

                    if (sheet_id == "")
                    {
                        if (ov != DBNull.Value && ov != null)
                            sheet_id = ov.ToString().Split(',')[0];
                    }
                    else
                    {
                        if (ov == DBNull.Value || ov == null)
                        {
                            MyLogger.LogMsg($"In SaveAndApply,sheet_type:{sheet_type},sheet_id{sheet_id},sheet deleted before apply", company_id);
                            sError = "单据申请前已被删除"; goto END;
                        }
                    }
                    
                    await OnSheetIDGot(cmd, sheet_id, info);
                    if (info != null && info.ErrMsg != "")//注意，这里不能移走，否则OnSheetIDGot里面抛出的错误就不会被捕获到
                    {
                        if (bAutoCommit && tran != null) tran.Rollback();
                        sError = info.ErrMsg; goto END;
                    }
                    // if (!IsImported)
                    // {
                    //     info.ErrMsg = await ToVoucher(cmd, VoucherOperType.SaveAndApprove);
                    //     if (info.ErrMsg != "")
                    //     {
                    //         if (bAutoCommit && tran != null) tran.Rollback();
                    //         sError = info.ErrMsg; goto END;
                    //     }
                    // }

                    if (bAutoCommit && tran != null) tran.Commit();
                }
                catch (Exception e)
                {
                    if (bAutoCommit && tran != null)
                    {
                        try { tran.Rollback(); }
                        catch (Exception) { }

                    }
                    MyLogger.LogMsg($"In SaveAndApply,sheet_type:{sheet_type},sheet_id{sheet_id},msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}", company_id);
                    sError = "申请发生了错误";
                }
            END:
                int i = 1;
            }


            if (redisKey != "")
            {
                if (sError != "")
                {
                    await RedisHelper.DelAsync(redisKey);
                }
                else if (hasSheetID)
                    await RedisHelper.DelAsync(redisKey);
                else
                    await RedisHelper.ExpireAsync(redisKey, 5);

            }
            return sError;
        }
    
        public async Task<string> Approve(CMySbCommand cmd, bool bAutoCommit = true)
        {
            string sError = "";
            string checkResult = await CheckSheetValid(cmd);
            if (checkResult != "OK") return checkResult;
            if (!FIXING_ARREARS)
                approve_time = CPubVars.GetDateText(DateTime.Now);
            CInfoForApproveBase info = this.InfoForApprove;
            if (info == null)
            {
                info = await GetInfoForApprove(cmd);
            }

            string sql = GetApproveSQL(info);
            if (info.ErrMsg != "") return info.ErrMsg;
            if (!FIXING_ARREARS)
                sql += $"update {MainTable} set approver_id={OperID}, approve_time={approve_time} where sheet_id={sheet_id} and company_id = {company_id};";

            cmd.CommandText = sql;

            CMySbTransaction tran = null;
            if (bAutoCommit)
                tran = cmd.Connection.BeginTransaction();

            try
            {
                if (cmd.CommandText != "")
                    await cmd.ExecuteNonQueryAsync();
                await OnSheetIDGot(cmd, sheet_id, info);
                if (info.ErrMsg != "")//注意，这里不能移走，否则OnSheetIDGot里面抛出的错误就不会被捕获到
                {
                    if (bAutoCommit && tran != null) tran.Rollback();
                    sError = info.ErrMsg; return sError;
                }
                //info.ErrMsg = await ToVoucher(cmd, VoucherOperType.Approve);//重算往来账走Approve与凭证无关
                //if (info.ErrMsg != "")
                //{
                //    if (bAutoCommit && tran != null) tran.Rollback();
                //    return info.ErrMsg;
                //}
                if (bAutoCommit && tran != null) tran.Commit();
            }
            catch (Exception e)
            {
                if (bAutoCommit && tran != null) tran.Rollback();
                return e.Message;
            }
            return sError;
        }
        public string GetSqlForArrearsQQ(string custID, string seller_id)
        {
            string sql = @$"
SELECT ab.balance AS sup_balance,          ab.pend_amount AS sup_pend_amount,                                   ab.first_arrears_time,                                 ab.first_arrears_sheet_id,                                 ab.first_arrears_sheet_no,                                     ab.first_arrears_sheet_type,
  ab_acct.balance AS sup_acct_balance,ab_acct.pend_amount AS sup_acct_pend_amount, ab_acct.first_arrears_time acct_first_arrears_time,ab_acct.first_arrears_sheet_id acct_first_arrears_sheet_id,ab_acct.first_arrears_sheet_no acct_first_arrears_sheet_no,  ab_acct.first_arrears_sheet_type acct_first_arrears_sheet_type,
  sup.max_arrears AS sup_max_arrears, 
 acct.max_arrears AS sup_acct_max_arrears, aa.auxiliary_balance AS seller_balance, aa.auxiliary_pend_amount AS seller_pend_amount, o.seller_max_arrears,
  sup.acct_cust_id,aclass.aclass_max_arrears,aclass.aclass_max_arrears_day,aclient.aclient_max_arrears,aclient.aclient_max_arrears_day,ao.ao_max_arrears,ao.ao_max_arrears_day
FROM info_supcust sup
LEFT JOIN info_supcust acct ON acct.company_id = {company_id} AND sup.acct_cust_id = acct.supcust_id
LEFT JOIN arrears_balance ab ON {custID} = ab.supcust_id AND ab.company_id = {company_id}
LEFT JOIN arrears_balance ab_acct ON sup.acct_cust_id = ab_acct.supcust_id AND ab_acct.company_id = {company_id}
LEFT JOIN arrears_balance_auxiliary aa ON aa.company_id = {company_id} AND aa.auxiliary_id = {seller_id} AND aa.auxiliary_type = 'seller'
LEFT JOIN info_operator o ON o.company_id = {company_id} AND o.oper_id = {seller_id}
LEFT JOIN LATERAL
(
    SELECT max_arrears AS aclass_max_arrears,max_arrears_days AS aclass_max_arrears_day
    FROM arrears_strategy_class ac
    WHERE ac.company_id = {company_id} AND (COALESCE(ac.group_id, 0) = COALESCE(sup.sup_group, 0) OR ac.group_id is null ) AND (COALESCE(ac.region_id, 0) = COALESCE(sup.region_id, 0) OR ac.region_id is null) AND (COALESCE(ac.rank_id, 0) = COALESCE(sup.sup_rank, 0) OR ac.rank_id is null)
	ORDER BY flow_id desc LIMIT 1
) aclass ON TRUE
LEFT JOIN LATERAL
(
    SELECT max_arrears AS aclient_max_arrears,max_arrears_days AS aclient_max_arrears_day
    FROM arrears_strategy_client aclient
    WHERE aclient.company_id = {company_id} AND (aclient.supcust_id = {custID} OR aclient.supcust_id = acct.supcust_id) ORDER BY aclient.flow_id desc LIMIT 1
) aclient ON TRUE
LEFT JOIN LATERAL
(
    SELECT max_arrears AS ao_max_arrears,max_arrears_days AS ao_max_arrears_day
    FROM arrears_strategy_operator ao
    WHERE ao.company_id = {company_id} AND ao.oper_id = {seller_id} ORDER BY ao.flow_id desc LIMIT 1
) ao ON TRUE
WHERE sup.company_id = {company_id} AND sup.supcust_id = {custID};
";



	/*
             
            LEFT JOIN
(
    SELECT ceil( EXTRACT( EPOCH FROM AGE(NOW(), approve_time)) / 86400) AS max_days_since_approve,sheet_no,approve_time
    FROM
    (
        SELECT sheet_no, approve_time
        FROM
        (
            SELECT combined_result.sheet_no, combined_result.approve_time,ROW_NUMBER() OVER (ORDER BY combined_result.approve_time) AS rn
            FROM
            ( 
                SELECT sm.approve_time, sm.sheet_no
                FROM client_account_history ch
                LEFT JOIN sheet_sale_main sm ON {company_id} = sm.company_id AND ch.sheet_id = sm.sheet_id AND ch.sheet_type = sm.sheet_type
                WHERE
                  ch.company_id = {company_id} AND ch.supcust_id = {custID} and ch.sheet_type='X' AND ch.sub_type = 'QK' AND sm.total_amount - sm.paid_amount - sm.disc_amount > 0.01  AND ch.red_flag IS NULL
             
             ) AS combined_result
        ) AS t
        WHERE t.rn = 1
      ) tt
) AS un ON TRUE

      */

			return sql;
        }

        public string GetSqlCheckHasInventoryBeforeApprove()
        {
            string condition = "sm.branch_id = imt.branch_id";
            if (sheet_type == SHEET_TYPE.SHEET_MOVE_STORE)
            {
                condition = "(sm.to_branch_id = imt.branch_id or sm.from_branch_id=imt.branch_id)";
            };

            string sql = $@"
     select idt.sheet_item_name item_name from 
			(select * from {MainTable} where company_id={company_id} and sheet_id={sheet_id}) sm
     LEFT JOIN {DetailTable} sd on sm.company_id=sd.company_id and sm.sheet_id=sd.sheet_id
		 LEFT JOIN sheet_inventory_main imt on {condition} and sm.company_id = imt.company_id
     LEFT JOIN sheet_inventory_detail idt on imt.company_id=idt.company_id and sd.item_id=idt.item_id and imt.sheet_id=idt.sheet_id
     where imt.happen_time>sm.happen_time and imt.approve_time is not null and imt.red_flag is null";
            return sql;

        }
        public async Task<List<string>> DealHasInventory(string sql, CMySbCommand cmd)
        {
            List<string> list = new List<string>();
            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            if (records != null)
            {
                foreach (var rec in records)
                {
                    list.Add(Convert.ToString(rec.item_name));
                }
            }
            return list;
        }
        public void DealArrearReadQQ(CMySbDataReader dr, CInfoForApproveBase info, decimal nowLeftAmount, string supcustId)
        {
            dynamic record = CDbDealer.Get1RecordFromDr(dr, false);
            if (record != null)
            {
                //acct_cust_id = record.acct_cust_id;
                info.SellerHasMaxArrears = record.ao_max_arrears != "";//业务员是否有最大欠款
                if (record.acct_cust_id != "")//客户有父类，欠款额度和欠款占用就按父类的计算
                {
                    info.ArrearsBalance = record.sup_acct_balance;
                    if (record.sup_acct_pend_amount != "")
                        info.SupArrearsPendAmount = CPubVars.ToDecimal(record.sup_acct_pend_amount);
                    info.FirstArrearsTime = record.acct_first_arrears_time;
                  
                    info.FirstArrearsSheetID= record.acct_first_arrears_sheet_id;
					info.FirstArrearsSheetNo = record.acct_first_arrears_sheet_no;
					
					info.FirstArrearsSheetType = record.acct_first_arrears_sheet_type;

					//acct_cust_id = record.acct_cust_id;
					info.ClientIdForAcct = record.acct_cust_id;
                }
                else
                {
                    info.ClientIdForAcct = supcustId;
                    info.ArrearsBalance = record.sup_balance;
                    if (record.sup_pend_amount != "")
                        info.SupArrearsPendAmount = CPubVars.ToDecimal(record.sup_pend_amount);//客户的占用欠款，就是在开销售订单和占位单时，如果是欠款支付的话，先把欠款额度占用上。
					info.FirstArrearsTime = record.first_arrears_time;
					info.FirstArrearsSheetID = record.first_arrears_sheet_id;
					info.FirstArrearsSheetNo = record.first_arrears_sheet_no;
					info.FirstArrearsSheetType = record.first_arrears_sheet_type;
				}

                if (info.ArrearsBalance != "")
                {
                    decimal bal = CPubVars.ToDecimal(info.ArrearsBalance);
                    if (Math.Abs(bal) < 0.01m)
                    {
                        info.FirstArrearsTime = "";
                    }
                }

                if (info.OrderLeftAmount != 0)//订单的欠款不为0，就要把订单的欠款去掉，因为这个已经在pend_amount赋值占用欠款额度了，不能重复计算
                {
                    nowLeftAmount -= info.OrderLeftAmount;
                }

                if (Math.Abs(nowLeftAmount) > 0.01m && !FIXING_ARREARS)
                {
                    //最大欠款额度和最大欠款天数应当根据数据库字段的有无来判断，空字符串代表没有设置限额，自然不需要判断
                    string record_class_max_arrears = record.aclass_max_arrears;
                    string record_class_max_arrears_day = record.aclass_max_arrears_day;

                    string record_client_max_arrears = record.aclient_max_arrears;
                    string record_client_max_arrears_day = record.aclient_max_arrears_day;

                    string s_sup_max_arrears = record_class_max_arrears;
                    string s_sup_max_arrears_day = record_class_max_arrears_day;//额度和天数先按客户类别指定

                    decimal d_sup_max_arrears = -1;
                    int d_sup_max_arrears_day = -1;

                    if (!string.IsNullOrEmpty(record_client_max_arrears) || !string.IsNullOrEmpty(record_client_max_arrears_day))//按客户指定欠款额度或者欠款额度不为空的话，额度和天数都需要按客户指定走。按客户指定的优先级高
                    {
                        s_sup_max_arrears = record_client_max_arrears;
                        s_sup_max_arrears_day = record_client_max_arrears_day;
                    }

                    if (s_sup_max_arrears_day.IsValid())
                    {
						d_sup_max_arrears_day = Convert.ToInt32(s_sup_max_arrears_day);
					}
                  
                    decimal sup_balance = CPubVars.ToDecimal(info.ArrearsBalance == "" ? "0" : info.ArrearsBalance);
                    //decimal d_sup_max_arrears = CPubVars.ToDecimal(sup_max_arrears);
                    if (info.SupArrearsPendAmount != 0)//说明有欠款额度占用，也属于欠款
                    {
                        sup_balance += info.SupArrearsPendAmount;//数据库里的欠款应该是已经欠的钱和欠款额度占用的钱
                    }
                    decimal addArrears = CPubVars.ToDecimal(nowLeftAmount * this.money_inout_flag);
                    var newArrearsBalance = sup_balance + addArrears;
                 
                    decimal sellerMaxArrears = CPubVars.ToDecimal(record.ao_max_arrears == "" ? 0 : record.ao_max_arrears);
                    if (!string.IsNullOrEmpty(s_sup_max_arrears)) 
                    {
                         d_sup_max_arrears = CPubVars.ToDecimal(s_sup_max_arrears);
                    }
                  
                    if ((d_sup_max_arrears > 0 || sellerMaxArrears > 0 || d_sup_max_arrears_day > 0) && addArrears > 0 && newArrearsBalance > 0)
                    // if (addArrears > 0 && sup_balance>0 && newArrearsBalance >0)
                    { 
                        // if (!string.IsNullOrEmpty(s_sup_max_arrears))//在经过上面两个赋值之后有值的话，就要判断额度了
                        // {
                            // d_sup_max_arrears = CPubVars.ToDecimal(s_sup_max_arrears);

                            if (addArrears > 0 && d_sup_max_arrears > 0 && newArrearsBalance > d_sup_max_arrears)
                            {

                                decimal canLeftAmount = nowLeftAmount - (newArrearsBalance - d_sup_max_arrears);//最大欠款额度 - 算上这张单子的总欠款+这张单子的欠款 = 最大欠款额度 - 数据库里记录欠的钱
                                canLeftAmount = canLeftAmount < 0 ? 0 : canLeftAmount;
                                info.ErrMsg = $"该客户超过最大欠款额度{d_sup_max_arrears}元,还能欠{canLeftAmount}元";
                            }
                        // }

                        if (!string.IsNullOrEmpty(s_sup_max_arrears_day))//欠款天数不为空的话，就要判断欠款天数是否超限
                        {
                            
                            //  decimal sup_earliest_arrears_sheet_day = CPubVars.ToDecimal(record.sup_earliest_arrears_sheet_day == "" ? "0" : record.sup_earliest_arrears_sheet_day);
                            //dynamic sup_earliest_arrears_sheet_no = record.sup_earliest_arrears_sheet_no;
                            //dynamic sup_earliest_arrears_approve_time = record.sup_earliest_arrears_time;

                            if (info.FirstArrearsTime != "")
                            {
                                DateTime tm = Convert.ToDateTime(info.FirstArrearsTime);
                                if (tm.AddDays(d_sup_max_arrears_day) < DateTime.Now)
                                {
                                    info.ErrMsg += $"超过最大欠款天数{d_sup_max_arrears_day}天但仍存在欠款{newArrearsBalance},最早欠款单号是{info.FirstArrearsSheetNo}";
                                }
                            }
                       
                        }

                        if (info.SellerHasMaxArrears)
                        {
                            decimal seller_balance = CPubVars.ToDecimal(record.seller_balance == "" ? 0 : record.seller_balance);
                            decimal seller_max_arrears = CPubVars.ToDecimal(record.ao_max_arrears);
                            decimal seller_pend_amount = 0;
                            // string pendPrompt = "";
                            // decimal seller_now_pend_amount = 0;
                            if (record.seller_pend_amount != "")
                            {
                                seller_pend_amount = CPubVars.ToDecimal(record.seller_pend_amount);
                                seller_balance += seller_pend_amount;
                                // seller_now_pend_amount=seller_pend_amount+
                                //pendPrompt =$"(其中订单{seller_pend_amount元})"
                            }


                            var newBalance = seller_balance + CPubVars.ToDecimal(nowLeftAmount * this.money_inout_flag);
                            if (newBalance > seller_max_arrears)
                            {
                                decimal canLeftAmount = nowLeftAmount - (newBalance - seller_max_arrears);
                                info.ErrMsg += $"超过业务员欠款额度{seller_max_arrears}元,还能欠{canLeftAmount}元";
                            }
                        }
                    }

                    
                }
            }
        } 

        public async Task<string> GetSqlForArrearsChange(CMySbCommand cmd, CInfoForApproveBase info, decimal leftAmount, string sellerId)
        {
            string sql = "";
            decimal arrearsBal = 0;
            decimal changeBal = leftAmount * money_inout_flag;
            if (Math.Abs(changeBal) < 0.01m) changeBal = 0;
            if (info.ArrearsBalance != "") arrearsBal = CPubVars.ToDecimal(info.ArrearsBalance);
            arrearsBal += changeBal;
            string custID = info.ClientIdForAcct;
            string auxiliaryPendAmount = "";

           // pendAmount = "", 
			string ARREARS_TIME_INSERT_FLDS = "", ARREARS_TIME_INSERT_VALUES = "", ARREARS_TIME_UPDATE_FLDS_VALUES = "";

			if (",X,DH,YS,".Contains("," + this.SheetType + ",") && changeBal>0)
            {
				bool bChangeArrearsTime = false;
                string firstArrearsTime = "", firstArrearsSheetID = "", firstArrearsSheetType = "", firstArrearsSheetNo = "";
                if (this.red_flag == "2")
                {
                    if(this.happen_time== info.FirstArrearsTime)
                    {
						string sqlQuery = $"select happen_time,sheet_type,sheet_id, sheet_no from client_account_history where company_id={this.company_id} and sheet_type in ('X','DH','YS') and settle_time is null and sub_type='QK' and supcust_id={custID} and red_flag is null and happen_time>'{this.happen_time}' order by happen_time limit 1";
                        dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sqlQuery, cmd);
                        if(rec!=null)
                        {
                            firstArrearsTime = "'"+rec.happen_time+"'"; 
							firstArrearsSheetID = rec.sheet_id; 
							firstArrearsSheetType = "'"+ rec.sheet_type + "'";
                            firstArrearsSheetNo = "'" + rec.sheet_no + "'";
                            bChangeArrearsTime = true;
						}
					}
                }
                else
                {
					if (info.FirstArrearsTime != "")
					{
						DateTime tm = Convert.ToDateTime(this.happen_time);
						DateTime tm1 = Convert.ToDateTime(info.FirstArrearsTime);
						if (tm < tm1)
						{
							bChangeArrearsTime = true;
						}

					}
					else bChangeArrearsTime = true;

                    if (bChangeArrearsTime)
                    {
                        firstArrearsTime = "'" + this.happen_time + "'";
                        firstArrearsSheetID = this.sheet_id;
                        firstArrearsSheetType = "'" + this.SheetType + "'";
                        firstArrearsSheetNo = "'" + this.sheet_no + "'";
                    }
                }
               
                if (bChangeArrearsTime)
                {
                    ARREARS_TIME_INSERT_FLDS = "   ,first_arrears_time, first_arrears_sheet_id,first_arrears_sheet_type,first_arrears_sheet_no ";
                    ARREARS_TIME_INSERT_VALUES = $",{firstArrearsTime}, {firstArrearsSheetID} ,{firstArrearsSheetType},{firstArrearsSheetNo}";
                    ARREARS_TIME_UPDATE_FLDS_VALUES = $",first_arrears_time={firstArrearsTime},first_arrears_sheet_id={firstArrearsSheetID},first_arrears_sheet_type={firstArrearsSheetType},first_arrears_sheet_no = {firstArrearsSheetNo}";

                }

			}

			if (info.OrderLeftAmount != 0)
            {
               // pendAmount = $",pend_amount = arrears_balance.pend_amount-({info.OrderLeftAmount * money_inout_flag})";
                auxiliaryPendAmount = $",auxiliary_pend_amount = arrears_balance_auxiliary.auxiliary_pend_amount-({info.OrderLeftAmount * money_inout_flag})";
            }
            

            if (changeBal != 0 || info.OrderLeftAmount != 0)
            {
                sql += @$"insert into arrears_balance (company_id,supcust_id,   balance  {ARREARS_TIME_INSERT_FLDS}) 
                                               values ({company_id},{custID},{changeBal} {ARREARS_TIME_INSERT_VALUES})
                    on conflict(company_id,supcust_id) do update set balance = arrears_balance.balance+({changeBal}) {ARREARS_TIME_UPDATE_FLDS_VALUES};";
                if (info.OrderSupCustID != "" && info.OrderLeftAmount!=0)
                {
                    sql += $"update arrears_balance ab set pend_amount=ab.pend_amount-({info.OrderLeftAmount * money_inout_flag}) where company_id={company_id} and supcust_id={info.OrderSupCustID};";
                }
            }
             

            if (red_flag == "2")
            {
                sql += @$"update client_account_history set red_flag = '1' where company_id = {company_id} and sheet_id = {red_sheet_id} and sheet_type = '{SheetType}' and sub_type = 'QK';";
            }
            string sRedFlag = "null";
            if (red_flag == "2") sRedFlag = "'" + red_flag + "'";
            if (changeBal != 0)
            {
                sql += @$"
insert into client_account_history(company_id,                           happen_time,                          approve_time,  sheet_type,   sheet_id,change_amount,now_balance,                         now_balance_happen_time,supcust_id,sub_type,red_flag) 
                          values ({company_id},'{CPubVars.GetDateText(happen_time)}','{CPubVars.GetDateText(approve_time)}','{SheetType}',{sheet_id},{changeBal}  ,{arrearsBal},{arrearsBal - info.AccountHistoryArrearsChange},  {custID},'QK',   {sRedFlag});";
                if (!HappenNow)
                    sql += $"update client_account_history set now_balance_happen_time=now_balance_happen_time+{changeBal} where company_id={company_id} and sub_type='QK' and supcust_id={custID} and happen_time>'{CPubVars.GetDateText(happen_time)}' ;";
            }

            if (info.SellerHasMaxArrears && (leftAmount != 0 || info.OrderLeftAmount != 0))
            {
                if (",CG,CT,SR,YF,".IndexOf("," + this.SheetType + ",") == -1)
                {
                    sql += $"insert into arrears_balance_auxiliary(company_id,auxiliary_type,auxiliary_id,auxiliary_balance) values ({company_id},'seller',{sellerId},{changeBal}) on conflict(company_id,auxiliary_type,auxiliary_id) do update set auxiliary_balance=arrears_balance_auxiliary.auxiliary_balance+({changeBal}){auxiliaryPendAmount};";
                }
            }
            return sql;

        }
        /*
        public async Task<string> Approve(CMySbCommand cmd, string companyID, string sheetID, string approverID,   bool bAutoCommit = true)
        {
            string sError = "";
            if (sheetID == "")
            {
                sError = "审核单据必须指定单据号"; return sError;
            }
         
            string checkResult = await CheckSheetValid();
            if (checkResult != "OK") return checkResult;
            approve_time = CPubVars.GetDateText(DateTime.Now);          
            
            CInfoForApproveBase info = await Load(cmd, companyID, sheetID);

            string sql = GetApproveSQL(info);

            sql += $"update {MainTable} set approver_id={approverID}, approve_time={CPubVars.GetDateText(DateTime.Now)} where sheet_id={sheet_id} and company_id = {companyID};";
             
            cmd.CommandText = sql;

            CMySbTransaction tran = null;
            if (bAutoCommit)
                tran = cmd.Connection.BeginTransaction();

            try
            {
                await cmd.ExecuteNonQueryAsync();
                await OnSheetIDGot(cmd, sheet_id, info);
                if (info.ErrMsg != "")
                {
                    if (tran != null) tran.Rollback();
                    return info.ErrMsg;
                }
                if (tran != null) tran.Commit();
            }
            catch(Exception e)
            {
                if (tran != null) tran.Rollback();
            }
            return sError;
        }
        */
        protected virtual async Task<string> CheckForRed(CMySbCommand cmd)
        {
            return "";
        }

        protected virtual async Task<string> CheckForCancel(CMySbCommand cmd)
        {
            return "";
        }
        protected async Task<string> CheckForRed_MoneySheet(CMySbCommand cmd)
        {
            string err = "";
            string sheet_type_db = StrFromSheetType(sheet_type);
            string unable_red_sql = @$"select d.sheet_id,sheet_type from sheet_get_arrears_detail d left join sheet_get_arrears_main m on m.sheet_id= d.sheet_id where d.company_id = {company_id} and mm_sheet_id={sheet_id} and m_sheet_type= '{sheet_type_db}' and m.red_flag is null and m.approve_time is not null and (d.now_pay_amount<>0 or d.now_disc_amount<>0)
                                        union 
                                       select d.sheet_id,'JZ' sheet_type from sheet_check_sheets_detail d left join sheet_check_sheets_main m on m.sheet_id = d.sheet_id where d.company_id={company_id} and business_sheet_id ={sheet_id} and business_sheet_type = '{sheet_type_db}' and m.red_flag is null and m.approve_time is not null";
            dynamic data = await CDbDealer.Get1RecordFromSQLAsync(unable_red_sql, cmd);
            if (data != null)
            {
                if (data.sheet_type == "SK" || data.sheet_type == "FK") err = "该单据后续已被收款，请先红冲对应收款单";
                else if (data.sheet_type == "JZ") err = "该单据已交账，请先反交账再红冲";
                if (err != "") return err;
            }
            return "";
        }



        public static Dictionary<string, string> OperatingSheetsID = new Dictionary<string, string>();
        public async Task<string> Red(CMySbCommand cmd, string companyID, string sheetID, string rederID, string redBrief, bool bAutoCommit = true)
        {
            string sError = "";
            if (sheetID == "")
            {
                sError = "红冲单据必须指定单据号"; return sError;
            }
            if (!bAutoCommit && cmd.ActiveDatabase != "")
            {
                throw new Exception("if bAutoCommit false,activeDatabase should be ''");
            }
            object ov = null;
            // 添加日志
            NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
            logger.Info($"尝试红冲单据: sheet_type={this.SheetType}, sheet_id={sheetID}, rederID={rederID}");
            
            this.company_id = companyID;
            
            // 先尝试获取Redis锁，避免不必要的数据库操作
            string redRedisKey = GetRedRedisLockKey(sheetID);
            bool lockAcquired = false;
            
            try
            {
                if (redRedisKey != "")
                {
                    // 使用SetNx确保只有一个进程能获取锁
                    lockAcquired = await RedisHelper.SetAsync(redRedisKey, "1", 120, RedisExistence.Nx);
                    if (!lockAcquired)
                    {
                        logger.Warn($"无法获取Redis锁，可能有其他进程正在红冲: sheet_type={this.SheetType}, sheet_id={sheetID}");
                        return "请勿重复红冲!";
                    }
                    logger.Info($"成功获取Redis锁: sheet_type={this.SheetType}, sheet_id={sheetID}");
                }
                
                
                // 加载单据并执行红冲
                CInfoForApproveBase info = null;
                string sql = "";
                CMySbTransaction tran = null;
                
                try
                {
                    cmd.ActiveDatabase = "";
                    this.red_sheet_id = sheetID;
                    this.OperID = rederID;
                    
                    info = await Load(cmd, companyID, sheetID, true);
                    if (info.ErrMsg != "")
                    {
                        sError = info.ErrMsg; 
                        goto RED_END;
                    }
                    
                    string reviewer_id_fld = "";
                    if (",X,XD,T,TD,SK,FK,".Contains("," + this.SheetType + ","))
                    {
                        reviewer_id_fld = ",reviewer_id";
                    }
                    
                    string sql1 = $"select red_flag,approve_time {reviewer_id_fld} from {MainTable} where {this.SheetIdFld}={sheetID} and company_id = {companyID}";
                    cmd.CommandText = sql1;
                    dynamic origSheet = await CDbDealer.Get1RecordFromSQLAsync(sql1, cmd);
                    if (origSheet == null)
                    {
                        sError = "单据不存在"; 
                        goto RED_END;
                    }
                    if (origSheet.approve_time == "")
                    {
                        sError = "该单据未被审核，不能红冲"; 
                        goto RED_END;
                    }
                    if (origSheet.red_flag != "")
                    {
                        sError = "该单据已被红冲，不能再次红冲"; 
                        goto RED_END;
                    }
                    
                    string RedSheetAfterReview = "";
                    cmd.CommandText = $"select rights->'delicacy'->'allowRedSheetAfterReview'->'value' from info_operator o left join info_role r on o.company_id=r.company_id and o.role_id=r.role_id where oper_id={rederID}";
                    object arsar = await cmd.ExecuteScalarAsync();
                    if (arsar != null && arsar != DBNull.Value) RedSheetAfterReview = arsar.ToString();
                    if (RedSheetAfterReview == "\"notAllow\"")
                    {
                        if (reviewer_id_fld != "")
                        {
                            if (origSheet.reviewer_id != "" && origSheet.reviewer_id != rederID)
                            {
                                sError = "该单据已被他人复核，不能红冲"; 
                                goto RED_END;
                            }
                        }
                    }

                    if (origSheet.approve_time != "")
                    {
                        DateTime approveTime = Convert.ToDateTime(origSheet.approve_time);

                        string limitDays = "";
                        cmd.CommandText = $"select rights->'delicacy'->'redSheetDayLimit'->'value' from info_operator o left join info_role r on o.company_id=r.company_id and o.role_id=r.role_id where oper_id={rederID}";
                        ov = await cmd.ExecuteScalarAsync();
                        if (ov != null && ov != DBNull.Value) limitDays = ov.ToString();

                        if (limitDays.Contains("days")) limitDays = limitDays.Replace("days", ""); else limitDays = "";
                        limitDays = limitDays.Replace("\"", "");
                        int nLimitDays = 90;
                        if (limitDays != "") nLimitDays = Convert.ToInt32(limitDays);

                        DateTime sevenDaysBefore = DateTime.Now.Date.AddDays(-(nLimitDays - 1));

                        if (approveTime < sevenDaysBefore)
                        {
                            sError = $"只能红冲最近{nLimitDays}天内审核的单据"; 
                            goto RED_END;
                        }
                    }
                    
                    make_brief += redBrief ?? "";
                    
                    // 检查其他红冲条件
                    sError = await CheckForRed(cmd);
                    if (sError != "") 
                        goto RED_END;
                    
                    // 生成红冲单据
                    this.sheet_id = "";
                    this.approver_id = rederID;
                    this.approve_time = CPubVars.GetDateText(DateTime.Now);
                    
                    string sqlSave = GetSaveSQL(true, out sError);
                    if (sError != "")
                        goto RED_END;
                    
                    string sqlApprove = GetApproveSQL(info);
                    if (info.ErrMsg != "")
                    {
                        sError = info.ErrMsg; 
                        goto RED_END;
                    }
                    
                    if (sqlSave.Trim() == "")
                    {
                        sError = "红字单保存错误"; 
                        goto RED_END;
                    }
                    
                    // 允许子类在红冲前执行自定义逻辑
                    sError = await BeforeRed(cmd, sheetID, rederID,redBrief,info);
                    if (sError != "") 
                    {
                        logger.Warn($"BeforeRed检查失败: sheet_type={this.SheetType}, sheet_id={sheetID}, 错误={sError}");
                        goto RED_END;
                    }                

                    sql = sqlSave + sqlApprove;

                  
                    sql += $"update {MainTable} set red_flag='1',make_brief='{make_brief}' where {this.SheetIdFld}={sheetID} and company_id = {companyID};";
                    
                    // 执行SQL
                    cmd.CommandText = sql;
                    logger.Info($"执行红冲SQL: sheet_type={this.SheetType}, sheet_id={sheetID}");
                    
                    if (bAutoCommit)
                        tran = await cmd.Connection.BeginTransactionAsync();
                    
                    ov = await cmd.ExecuteScalarAsync();
                    if (sheet_id == "")
                    {
                        if (ov != DBNull.Value && ov != null)
                            sheet_id = ov.ToString().Split(',')[0];
                    }
                    
                    await OnSheetIDGot(cmd, sheet_id, info);
                    if (info.ErrMsg != "")
                    {
                        if (bAutoCommit && tran != null) tran.Rollback();
                        sError = info.ErrMsg;
                        goto RED_END;
                    }
                    
                    info.ErrMsg = await ToVoucher(cmd, VoucherOperType.Red);
                    if (info.ErrMsg != "")
                    {
                        if (bAutoCommit && tran != null) tran.Rollback();
                        sError = info.ErrMsg;
                        goto RED_END;
                    }
                    sError = await AfterRed(cmd, sheetID, sheet_id,rederID,redBrief);
                    if (sError != "") 
                    {
                        logger.Warn($"AfterRed: sheet_type={this.SheetType}, sheet_id={sheetID}, 错误={sError}");
                       goto RED_END;
                    }
                    
                    if (bAutoCommit && tran != null)
                    {
                        tran.Commit();
                        logger.Info($"红冲成功，事务已提交: sheet_type={this.SheetType}, 原单据ID={sheetID}, 新单据ID={sheet_id}");
                    }
                }
                catch (Exception e)
                {
                    if (bAutoCommit && tran != null)
                    {
                        try { tran.Rollback(); }
                        catch (Exception) { }
                    }
                    
                    string msg = $"红冲异常: sheet_type={this.SheetType}, sheet_id={sheetID}, 错误={e.Message}, 堆栈={e.StackTrace}";
                    logger.Error(msg);
                    logger.Error($"红冲SQL: {sql}");
                    
                    MyLogger.LogMsg(msg, companyID);
                    sError = "红冲发生错误";
                }
                
            RED_END:
                if (sError != "")
                {
                    logger.Warn($"红冲失败: sheet_type={this.SheetType}, sheet_id={sheetID}, 错误={sError}");
                }
                return sError;
            }
            finally
            {
                // 确保在方法结束时释放Redis锁
                if (redRedisKey != "" && lockAcquired && !this.isRedAndChange)
                {
                    await RedisHelper.DelAsync(redRedisKey);
                    logger.Info($"释放红冲锁: sheet_type={this.SheetType}, sheet_id={sheetID}");
                }
            }
        }

        // 允许子类重写的钩子方法
        protected virtual async Task<string> BeforeRed(CMySbCommand cmd, string sheetID, string rederID,string redBrief,CInfoForApproveBase info)
        {
            // 默认实现为空，返回成功
            return "";
        }

        protected virtual async Task<string> AfterRed(CMySbCommand cmd,string origSheetID, string redSheetID, string rederID,string redBrief)
        {
            // 默认实现为空，返回成功
            return "";
        }

        protected virtual async Task<string> AfterDelete(CMySbCommand cmd,string sheetID, string operID)
        {
            // 默认实现为空，返回成功
            return "";
        }
        /* public async Task<string> GetNewSheetNo(CMySbConnection conn, CMySbCommand cmd)
        {
            if (conn == null)
            {
                conn = new CMySbConnection(CPubVars.ConnString);
                conn.Open();
            }
            if (cmd == null)
            {
                cmd = new CMySbCommand("", conn);
            }

            FieldInfo fi = typeof(SHEET_TYPE).GetField(sheet_type.ToString());
            DBValue[] attrs = fi.GetCustomAttributes(typeof(DBValue), false) as DBValue[];
            string sheet_type_db = "";
            if (attrs.Length > 0)
            {
                sheet_type_db = attrs[0].Value;
            }

            cmd.CommandText = $"select yj_getNewSheetNo({company_id},'{sheet_type_db}',{maker_id})";
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
                return ov.ToString();
            return "";
        }*/


        public object InvokeStaticMethod(Type clsType, string method, Type[] arrT, object[] arrParam)
        {
            MethodInfo mi = clsType.GetMethod(method).MakeGenericMethod(arrT);
            return mi.Invoke(null, arrParam);
        }
        public object InvokeMethod(Object obj, string method, Type[] arrT, object[] arrParam)
        {
            Type clsType = obj.GetType();
            MethodInfo mi = clsType.GetMethod(method).MakeGenericMethod(arrT);
            return mi.Invoke(null, arrParam);
        }

        public static string StrFromSheetTypeString(string sheetType)
        {
            SHEET_TYPE st = (SHEET_TYPE)Enum.Parse(typeof(SHEET_TYPE), sheetType, true);
            return StrFromSheetType(st);
        }
        public static string StrFromSheetType(SHEET_TYPE sheetType)
        {
            System.Type type = sheetType.GetType();
            System.Reflection.FieldInfo fi = type.GetField(sheetType.ToString());
            DBValue[] attrs = fi.GetCustomAttributes(typeof(DBValue), false) as DBValue[];
            if (attrs.Length > 0)
            {
                var str = attrs[0].Value;
                return str;
            }
            return "";
        }
        public static SHEET_TYPE SheetTypeFromStr(string trans_flag)
        {
            System.Type type = typeof(SHEET_TYPE);
            foreach (var enumValue in Enum.GetValues(type))
            {
                System.Reflection.FieldInfo fi = type.GetField(enumValue.ToString());
                DBValue[] attrs = fi.GetCustomAttributes(typeof(DBValue), false) as DBValue[];
                if (attrs.Length > 0)
                {
                    string str = attrs[0].Value;
                    if (str == trans_flag)
                    {
                        return (SHEET_TYPE)enumValue;
                    }
                }
            }

            return SHEET_TYPE.EMPTY;

        }

        public async virtual Task<string> Delete(CMySbCommand cmd, string companyID, string sheetID, string operID, bool bAutoCommit = true)
        {
            string sError = "";
            if (sheetID == "")
            {
                sError = "删除必须指定单据号"; return sError;
            }

            #region 判断是否有一个正在进行的操作
            /*string sheetKey = this.SheetType + sheetID;
            lock (OperatingSheetsID)
            {
                if (OperatingSheetsID.ContainsKey(sheetKey))
                {
                    return "该单据正在操作中,请勿重复操作";
                }
                OperatingSheetsID.Add(sheetKey, sheetKey);
            }
            */
			string redisKey = GetRedisLockKey(out bool hasSheetID);

			if (redisKey != "")
			{
				string redisValue = await RedisHelper.GetSetAsync(redisKey, "1");
				int expireSeconds = 60;				
				await RedisHelper.ExpireAsync(redisKey, expireSeconds);
				if (redisValue == "1")
				{
					return "单据正在操作中,请稍后操作!";
				}
			}

			#endregion

			// string sql1 = $"select approve_time from {MainTable} where sheet_id={sheetID}";
			string sql = $"select approve_time from {MainTable} where company_id={companyID} and sheet_id={sheetID}";
            cmd.CommandText = sql;
            object ov = await cmd.ExecuteScalarAsync();
            if (ov != null && ov != DBNull.Value)
            {
                sError = "该单据已审核,不能删除";
                goto RED_END;
            }
            
           
            if(this.SheetType?.ToLower() == "xd" || this.SheetType?.ToLower() == "td")
            {
                sql = $"update {MainTable} set is_del = true where company_id = {companyID} and sheet_id = {sheetID};";
            }
            else
            {
                sql = $"delete from {MainTable} where  company_id={companyID} and sheet_id={sheetID};";
                if (DetailTable.IsValid())
                {
                    sql += $"delete from {DetailTable} where  company_id={companyID} and sheet_id={sheetID};";
                }
            }
            string now = CPubVars.GetDateText(DateTime.Now);

            await Load(cmd, companyID, sheetID);
            string jsonSheet = JsonConvert.SerializeObject(this);

            sql += @$"insert into sheet_log(company_id, sheet_type,       sheet_id, oper_id, happen_time,action,  sheet_info)
                                    values({companyID},'{this.SheetType}',{sheetID},{operID},'{now}',   'del','{jsonSheet}');";
            cmd.CommandText = sql;
            CMySbTransaction tran = null;
            try
            {
                if (bAutoCommit)
                    tran = cmd.Connection.BeginTransaction();

                ov = await cmd.ExecuteScalarAsync();
                sError = await AfterDelete(cmd, sheetID, operID);
                if (sError!= "") goto RED_END;
                if (tran != null) tran.Commit();
            }
            catch (Exception e)
            {
                if (tran != null)
                    tran.Rollback();
                MyLogger.LogMsg("Error in sheetbase delete:" + e.Message + e.StackTrace, company_id);
                sError = "删除单据发生错误";
            }
        RED_END:

			if (redisKey != "")
			{
				await RedisHelper.DelAsync(redisKey);
			}

			/*lock (OperatingSheetsID)
            {
                if (OperatingSheetsID.ContainsKey(sheetKey)) OperatingSheetsID.Remove(sheetKey);
            }*/
            return sError;
        }

        public async virtual Task<string> Cancel(CMySbCommand cmd, bool bAutoCommit = true)
        {
            string sError = "";
            if (sheet_id == "")
            {
                sError = "撤销单据必须指定单据号"; return sError;
            }
            if (!bAutoCommit && cmd.ActiveDatabase != "")
            {
                throw new Exception("if bAutoCommit false,activeDatabase  should be ''");
            }

            #region 判断是否有一个正在进行的撤销操作
            string sheetKey = this.SheetType + sheet_id;
            lock (OperatingSheetsID)
            {
                if (OperatingSheetsID.ContainsKey(sheetKey))
                {
                    return "该单据正在操作中,请勿重复操作";
                }
                OperatingSheetsID.Add(sheetKey, sheetKey);
            }
            #endregion

            InitForSave();

            cmd.ActiveDatabase = "";
            cmd.company_id = this.company_id;
            string checkResult = await CheckSheetValid(cmd);
            if (checkResult != "OK") return checkResult;
            // checkResult = await CheckSheetRowValid(cmd);
            // if (checkResult != "OK") return checkResult;

            CInfoForApproveBase info = InfoForApprove;
            // CInfoForApproveBase info = null;
            string sql = "";
            try
            {

                if (approve_time != "")
                {
                    sError = "该单据已被审核，不能撤销"; goto CANCEL_END;
                }

                if (red_flag != "")
                {
                    sError = "该单据已被红冲，不能撤销"; goto CANCEL_END;
                }



                this.OperID = this.OperID;
                // info = await GetInfoForApprove(cmd);

                await LoadCompanySetting(cmd);
                if (info != null && info.ErrMsg != "")
                {
                    sError = info.ErrMsg; goto CANCEL_END;
                }

                sError = await CheckForCancel(cmd);
                if (sError != "") goto CANCEL_END;


                // 撤销操作是否要保存？
                string sqlSave = GetSaveSQL(false, out sError);
                if (sError != "") goto CANCEL_END;


                string sqlCancel = GetCancelSQL();
                if (info != null && info.ErrMsg != "")
                {
                    sError = info.ErrMsg; goto CANCEL_END;
                }


                sql = sqlSave + sqlCancel;


            }
            catch (Exception e)
            {
                NLog.Logger logger = NLog.LogManager.GetCurrentClassLogger();
                string msg = $"In Cancel,sheet_type:{sheet_type},sheet_id{sheet_id},,msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}";
                logger.Info(msg);
                logger.Info("In sheetBase.cancel,sql:" + sql);

                MyLogger.LogMsg(msg, company_id);
                sError = "撤销发生错误"; goto CANCEL_END;
            }

            if (sql.Trim() != "")
            {
                cmd.CommandText = sql;
            }
            CMySbTransaction tran = null;

            try
            {
                if (bAutoCommit)
                    tran = await cmd.Connection.BeginTransactionAsync();



                object ov = await cmd.ExecuteScalarAsync();


                if (sheet_id == "")
                {
                    if (ov != DBNull.Value && ov != null)
                        sheet_id = ov.ToString().Split(',')[0];
                }
                await OnSheetIDGot(cmd, sheet_id, info);
                /*if (info.ErrMsg == "")
                {
                    info.ErrMsg = await ToVoucher(cmd, VoucherOperType.Red);
                }
*/
                if (info != null && info.ErrMsg != "")
                {
                    if (bAutoCommit && tran != null) tran.Rollback();
                    sError = info.ErrMsg;
                    goto CANCEL_END;
                }
                if (bAutoCommit && tran != null)
                {
                    tran.Commit();
                }
            }
            catch (Exception e)
            {
                sError = "撤销发生了错误";
                if (bAutoCommit && tran != null)
                {
                    try { tran.Rollback(); }
                    catch (Exception) { }
                }
                MyLogger.LogMsg($"In Cancel commit error,sheet_type:{sheet_type},sheet_id{sheet_id},,msg:{e.Message},code:{e.StackTrace},targetSite:{e.TargetSite}", company_id);

            }

        CANCEL_END:
            lock (OperatingSheetsID)
            {
                if (OperatingSheetsID.ContainsKey(sheetKey)) OperatingSheetsID.Remove(sheetKey);
            }
            return sError;
        }
        public async Task LoadCompanySetting(CMySbCommand cmd)
        {
            var sql = @$"select setting,setting->>'companyName' as company_name,setting->>'contactTel' as company_tel,setting->>'companyAddress' as company_address from company_setting where company_id={company_id}";
            dynamic rec = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);
            if (rec != null)
            {
                company_name = rec.company_name;
                company_tel = rec.company_tel;
                company_address = rec.company_address;
                if (rec.setting != "")
                    company_setting = JsonConvert.DeserializeObject(rec.setting);
            }
        }

        #region 追加备注
        public async virtual Task<string> AppendBrief(CMySbCommand cmd, string companyID, string sheetID, string newBrief, bool bAutoCommit = true)
        {
            string sError = "";
            if (sheetID == "")
            {
                sError = "删除必须指定单据号";
                return sError;
            }

            string sql = "";
            if (newBrief != null && newBrief != "")
            {

                //if (oldBrief != null && oldBrief != "") newBrief += ',' + newBrief;
                // make_brief += newBrief;
                //if (make_brief != oldBrief)
                sql = $"update {MainTable} set make_brief= coalesce(make_brief,'') || ' {newBrief}' where company_id = {companyID} and sheet_id = {sheetID}";
            }
            cmd.CommandText = sql;
            CMySbTransaction tran = null;
            try
            {
                if (bAutoCommit)
                    tran = cmd.Connection.BeginTransaction();

                await cmd.ExecuteNonQueryAsync();

                if (tran != null) tran.Commit();
            }
            catch (Exception e)
            {
                if (tran != null)
                    tran.Rollback();
                return e.Message;
            }
            return sError;
        }
        #endregion

        #region 添加发票号
        public async virtual Task<string> AppendInvoice(CMySbCommand cmd, string companyID, string sheetID, string newInvoice, bool bAutoCommit = true)
        {
            string sError = "";
            if (sheetID == "")
            {
                sError = "删除必须指定单据号";
                return sError;
            }

            string sql = "";
            if (newInvoice != null && newInvoice != "")
            {

                //if (newInvoice != null && newInvoice != "") newInvoice += ',' + newInvoice;
                // invoice_no += newInvoice;
                //if (invoice_no != oldInvocie)
                sql = $"update {MainTable} set invoice_no= coalesce(invoice_no,'') || ' {newInvoice}' where company_id = {companyID} and sheet_id = {sheetID}";
            }
            cmd.CommandText = sql;
            CMySbTransaction tran = null;
            try
            {
                if (bAutoCommit)
                    tran = cmd.Connection.BeginTransaction();

                await cmd.ExecuteNonQueryAsync();

                if (tran != null) tran.Commit();
            }
            catch (Exception e)
            {
                if (tran != null)
                    tran.Rollback();
                return e.Message;
            }
            return sError;
        }
        #endregion


        #region Old Version 更新加权价
        public async Task UpdateCostPriceAvg(CMySbCommand cmd, List<SheetRowCostPrice> lstSheetRows, string dealing_happen_time)
        {
            string updateItemProp = "";
            string updateSaleSql = "";
            string log_sql = "";
            bool ignoreHistory = false;

            if (this.happen_time.IsValid())
            {
                if (this.sheet_type == SHEET_TYPE.SHEET_SALE || this.sheet_type == SHEET_TYPE.SHEET_SALE_RETURN || this.sheet_type == SHEET_TYPE.SHEET_BUY || this.sheet_type == SHEET_TYPE.SHEET_BUY_RETURN)
                {
                    var tm = Convert.ToDateTime(this.happen_time);
                    if (tm < DateTime.Now.AddDays(-30))
                    {
                        ignoreHistory = true;
                    }
                }

                if (this.sheet_type == SHEET_TYPE.SHEET_SALE || this.sheet_type == SHEET_TYPE.SHEET_SALE_RETURN || this.sheet_type == SHEET_TYPE.SHEET_COMBINE_ITEMS || this.sheet_type == SHEET_TYPE.SHEET_SPLIT_ITEMS || this.sheet_type == SHEET_TYPE.SHEET_BUY || this.sheet_type == SHEET_TYPE.SHEET_BUY_RETURN)
                {
                    
                        if (this.company_setting != null)
                        {
                            string useAccounting = this.company_setting.useAccounting;
                            if (useAccounting !=null && useAccounting.ToLower()== "true")
                            {
                                ignoreHistory=true;
                            }
                        } 
                }

            }


            // CInfoForApprove info = (CInfoForApprove)infoB;
            //List<SheetRowMM> lstRows = new List<SheetRowMM>();

            var mergeRowAvg = MergeSheetRowsForAvg(lstSheetRows);//单据里相同商品的合并，当前单据
            
            //获取从 单据发生时间的前一刻 至今 该单据商品的变化的数量和成本额（采购单，销售单，盘点盈亏单)

            List<dynamic> lstHistorySheetItems = new List<dynamic>();

            if (!HappenNow && !ignoreHistory)
            {
                dynamic res = (await GetHistorySheetItemsAfterMe(cmd, lstSheetRows, dealing_happen_time)).Value;

                // res.data:[{ sheet_id,sheet_type,money_inout_flag, t.happen_time, t.item_id,quantity,t.unit_factor,inout_flag,t.cost_price_avg,    cost_price_suspect,                        cost_price_buy,ip.cost_price_avg as cost_price_avg_info}]

                if (res.result != "OK")
                {
                    //info.ErrMsg = "GetHistorySheetItemsAfterMe return error";
                    return;
                }

                List<ExpandoObject> tmp = res.data;
                List<dynamic> lst = new List<dynamic>();
                foreach (dynamic d in tmp)
                {
                    // if (red_flag == "2"  ) condi = $" and sd.sheet_id<>{this.red_sheet_id} ";
                    lst.Add(d);
                }
                var sumLst = lst.GroupBy(r => new { r.sheet_id, r.sheet_type, r.red_flag, r.inout_flag, r.quantity, r.item_id, r.happen_time, r.cost_price_avg, r.cost_price_avg_info, r.unit_factor, r.s_real_price })
                    .Select(g =>
                        new
                        {
                            sheet_id = (string)g.Key.sheet_id,
                            red_flag = (string)g.Key.red_flag,
                            inout_flag = (string)g.Key.inout_flag,
                            quantity = (string)g.Key.quantity,
                            item_id = (string)g.Key.item_id,
                            happen_time = (string)g.Key.happen_time,
                            sheet_type = (string)g.Key.sheet_type,
                            unit_factor = (string)g.Key.unit_factor,
                            cost_price_avg = (string)g.Key.cost_price_avg,
                            cost_price_avg_info = (string)g.Key.cost_price_avg_info,
                            s_real_price = (string)g.Key.s_real_price,
                            sheet_qty = g.Sum(c => {
                                return (double)(CPubVars.ToDecimal(c.quantity) * CPubVars.ToDecimal(c.unit_factor) * CPubVars.ToDecimal(c.inout_flag));
                            }
                            ),
                            sheet_amount = g.Sum(c => {
                                if (c.sheet_type == "CG" || c.sheet_type == "ZZ" || c.sheet_type == "CF")
                                {
                                    if (c.sub_amount == "") c.sub_amount = 0;
                                    decimal d = CPubVars.ToDecimal(c.sub_amount) * CPubVars.ToDecimal(c.inout_flag);
                                    return d;
                                }
                                else
                                {
                                    decimal d = CPubVars.ToDecimal(c.quantity) * CPubVars.ToDecimal(c.unit_factor) * CPubVars.ToDecimal(c.inout_flag);
                                    if (c.cost_price_avg != "")
                                        d *= CPubVars.ToDecimal((string)c.cost_price_avg);
                                    else if (c.cost_price_avg_info != "")
                                        d *= CPubVars.ToDecimal((string)c.cost_price_avg_info);
                                    else d = 0;
                                    return d;
                                }

                            })
                        }
                   ).OrderBy(g => g.happen_time).ThenBy(g => g.item_id).ToList();

                sumLst.ForEach(r =>
                {
                    lstHistorySheetItems.Add(r);
                });
            }


            foreach (SheetRowCostPrice sheetRow in mergeRowAvg)
            {
                decimal sum_qty_change = 0;

                decimal firstCostPriceAvg = -1;//从本单据向后 第一个 有效成本价
                if (lstHistorySheetItems.Count > 0)
                {
                    sum_qty_change = lstHistorySheetItems.Sum(i => {//计算红冲单据之后的商品变化总数
                        if (i.item_id == sheetRow.item_id && i.sheet_type!="XD" && i.sheet_type!="TD")
                        {
                            return CPubVars.ToDecimal(i.sheet_qty);
                        }
                        else return 0m;


                    });//红冲单据之后的所有单据 该商品的数量变化

                    foreach (var r in lstHistorySheetItems)
                    {
                        if (r.item_id == sheetRow.item_id && r.cost_price_avg != "" && r.sheet_type != "XD" && r.sheet_type != "TD")
                        {
                            firstCostPriceAvg = CPubVars.ToDecimal((string)r.cost_price_avg);
                            break;
                        }
                    }
                }
                #region 合并当前单据之后单据的同一商品
                List<dynamic> mergelstDealSheetItems = new List<dynamic>();
                var groupedBySheetId = lstHistorySheetItems.GroupBy(a => (a.sheet_id, a.item_id)).ToList();
                foreach (var item in groupedBySheetId)
                {
                    try
                    {
                        var sheets = item.ToList();
                        dynamic quantity = 0;
                        dynamic sheet_amount = 0;
                        if (sheets.Count > 1)
                        {
                            // 创建一个对象，就收合并商品之后的数据
                            dynamic newSheet = new ExpandoObject();
                            //将同张单据的相同商品进行合并（数量、总额）
                            foreach (var sheet in sheets)
                            {
                                quantity += sheet.sheet_qty;
                                sheet_amount += sheet.sheet_amount;
                            }
                            newSheet.sheet_qty = quantity;
                            newSheet.quantity = quantity;
                            newSheet.sheet_amount = sheet_amount;
                            newSheet.cost_price_avg = sheets[0].cost_price_avg; //因为商品除了数量和总额其他属性都一样，故取第一个
                            newSheet.cost_price_avg_info = sheets[0].cost_price_avg_info;
                            newSheet.happen_time = sheets[0].happen_time;
                            newSheet.inout_flag = sheets[0].inout_flag;
                            newSheet.item_id = sheets[0].item_id;
                            newSheet.red_flag = sheets[0].red_flag;
                            newSheet.sheet_id = sheets[0].sheet_id;
                            newSheet.sheet_type = sheets[0].sheet_type;
                            newSheet.unit_factor = sheets[0].unit_factor;
                            mergelstDealSheetItems.Add(newSheet);   //添加到存放合并之后的list里面
                        }
                        else
                        {
                            mergelstDealSheetItems.Add(sheets[0]);
                        }


                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }

                }
                #endregion

                decimal earliest_stock_qty = sheetRow.old_total_qty; //审核单据/红冲前的库存数
                decimal earliest_cost_amt = sheetRow.old_total_qty * sheetRow.cost_price_avg;  //sheetRow.old_cost_amt;//单据审核/红冲前的库存金额
                decimal earliest_cost_price_avg = sheetRow.cost_price_avg;//(在其它单据没有问题，但是期初库存单可以编辑加权成本价)

                //算出发生时间之前的平均价 
                if (firstCostPriceAvg != -1)
                {
                    earliest_cost_price_avg = firstCostPriceAvg;
                    earliest_stock_qty -= sum_qty_change;
                    earliest_cost_amt = earliest_cost_price_avg * earliest_stock_qty;
                }

                decimal now_qty = earliest_stock_qty;
                decimal now_cost_amt = earliest_cost_amt;
                decimal now_cost_price_avg = earliest_cost_price_avg;
                decimal pre_cost_price_avg = now_cost_price_avg;
                bool bSuspecting = false;
                now_qty = Math.Round(now_qty, 3);
                if (now_qty < 0)
                {
                   // bSuspecting = true;
                }

                //遍历某个商品 从本单据插入前一刻 至今 的所有单据

                List<dynamic> lstDealSheetItems = new List<dynamic>();//当前商品在 当前单据 及 以后的单据中的列表

                //将该商品在 当前单据 的信息 加入 待处理列表
                dynamic row = new ExpandoObject();
                row.item_id = sheetRow.item_id;
                row.sheet_id = this.sheet_id;
                row.red_flag = this.red_flag;
                row.sheet_type = this.SheetType;
                row.inout_flag = sheetRow.inout_flag;
                row.quantity = sheetRow.quantity;
                row.happen_time = this.happen_time;
                row.sheet_qty = sheetRow.quantity;
                row.sheet_amount = sheetRow.sub_amount;
                row.cost_price_avg = sheetRow.cost_price_avg;
                row.unit_factor = sheetRow.unit_factor;
                row.s_real_price = sheetRow.real_price / sheetRow.unit_factor;
                // row.cost_price_buy=sheetRow.cost_price_buy
                lstDealSheetItems.Add(row);

                //将该商品在 当前单据以后的历史单据 的信息 加入 待处理列表
                if (firstCostPriceAvg != -1)
                {
                    foreach (dynamic item in mergelstDealSheetItems)
                    {
                        if (sheetRow.item_id == item.item_id)
                        {
                            bool isRededSheet = false;
                            if (this.red_flag == "2" && this.red_sheet_id == item.sheet_id)
                            {
                                isRededSheet = true;
                            }
                            if (!isRededSheet)
                                lstDealSheetItems.Add(item);
                        }
                    }
                }

                foreach (dynamic item in lstDealSheetItems)
                {
                    decimal qty_change = CPubVars.ToDecimal(item.sheet_qty);
                    if (item.sheet_type == "XD" || item.sheet_type == "TD") qty_change = 0;
                    decimal amount_change = 0;
                    if (",X,T,YK,BS,CT,".Contains("," + item.sheet_type + ","))
                    {
                        amount_change = qty_change * now_cost_price_avg;
                    }
                    else if (item.sheet_type == "QCKC" || item.sheet_type == "CG" || item.sheet_type == "FYFT")
                    {
                        amount_change = CPubVars.ToDecimal(item.sheet_amount);
                    }
                    else if (item.sheet_type == "ZZ" || item.sheet_type == "CF")
                    {
                        //amount_change = CPubVars.ToDecimal(item.sheet_amount);
                        if (item.red_flag == "" && item.sheet_amount < 0)//
                        {
                            amount_change = 0;
                            qty_change = 0;
                        }
                        else
                        {
                            amount_change = CPubVars.ToDecimal(item.sheet_amount);
                        }
                    }
                    else if (item.sheet_type == "CBTJ") amount_change = (CPubVars.ToDecimal(item.s_real_price) - now_cost_price_avg) * now_qty;


                    decimal pre_qty = now_qty;
                    now_qty += qty_change;
                    now_cost_amt += amount_change;

                    now_qty = Math.Round(now_qty, 3);
                    if (now_qty < 0 || pre_qty < 0 || now_cost_amt < 0)
                    {
                        if(this.isRedAndChange && this.red_flag == "2" && this.sheet_type==SHEET_TYPE.SHEET_BUY)
                        {

                        }
                        else
                        {
							bSuspecting = true;
							log_sql += @$"insert into cost_price_log (
          company_id,  sheet_id,       item_id,  happen_time,         sheet_type, infected_sheet_id,   suspect_status) 
 values ({company_id},{sheet_id},{item.item_id},'{happen_time}','{item.sheet_type}','{item.sheet_id}',             '1')
on conflict(company_id,sheet_id,       item_id,   happen_time,        sheet_type, infected_sheet_id) do update set suspect_status = '1';";

						}


					}

                    //if (!bSuspecting)
                    {
                        //如果是采购单，需要重算加权平均价   
                        if (item.sheet_type == "QCKC" || item.sheet_type == "CG" || item.sheet_type == "ZZ" || item.sheet_type == "CF" || item.sheet_type == "FYFT")
                        {
                            //如果采购后导致货值或库存为0 取本次采购的进价,防止出现加权价为0的情况
                            if (pre_qty>=0 && now_qty > 0 && now_cost_amt >= 0)
                            {
                                pre_cost_price_avg = now_cost_price_avg;
                                now_cost_price_avg = now_cost_amt / now_qty;
                                bSuspecting = false;
                            }
                            else if( this.red_flag!="2" && (item.sheet_type == "QCKC" || item.sheet_type == "CG") && qty_change>0)
                            {
                                now_cost_price_avg = amount_change / qty_change;
							}


                            /*
                            double buy_price = sheetRow.sub_amount / sheetRow.quantity;

                            //发现加权价可能计算错误，就取预设进价
                            if (now_cost_price_avg < 0 || (buy_price > 0 && now_cost_price_avg > 10 * buy_price))
                            {
                                now_cost_price_avg = buy_price;
                                now_cost_amt = now_qty * now_cost_price_avg;
                            }*/
                        }
                        else if (item.sheet_type == "CBTJ") now_cost_price_avg = CPubVars.ToDecimal(item.s_real_price);
                    }

                    if (!HappenNow && firstCostPriceAvg != -1 && !ignoreHistory)
                    {
                        if (item.sheet_type == "X" || item.sheet_type == "T")
                        {
                            CDbDealer db = new CDbDealer();
                            db.AddField("cost_price_suspect", bSuspecting.ToString());
                            db.AddField("cost_price_avg", now_cost_price_avg.ToString());
                            updateSaleSql += db.GetUpdateSQL("sheet_sale_detail", $"company_id = {company_id} and item_id = {item.item_id} and sheet_id = {item.sheet_id}") + ";";

                        }
                        else if (item.sheet_type == "XD" || item.sheet_type == "TD")
                        {
                            CDbDealer db = new CDbDealer();
                            db.AddField("cost_price_avg", now_cost_price_avg.ToString());
                            updateSaleSql += db.GetUpdateSQL("sheet_sale_order_detail", $"company_id = {company_id} and item_id = {item.item_id} and sheet_id = {item.sheet_id}") + ";";
                        }
                        else if (item.sheet_type == "CG" || item.sheet_type == "CT")
                        {
                            //if (!bSuspecting)
                            {
                                updateSaleSql += @$"update sheet_buy_detail set cost_price_avg = {pre_cost_price_avg} where company_id = {company_id} and item_id = {item.item_id} and sheet_id = {item.sheet_id};";
                            } 
                        }
                        //else if (item.sheet_type == "QCKC")
                        //{
                        //    if (!bSuspecting)
                        //    {
                        //        updateSaleSql += @$"update sheet_stock_opening_detail set cost_price_avg = {pre_cost_price_avg} where company_id = {company_id} and item_id = {item.item_id} and sheet_id = {item.sheet_id};";
                        //    }
                        //}
                        else if (item.sheet_type == "ZZ" || item.sheet_type == "CF")
                        {
                            if (!bSuspecting)
                            {
                                // 如果出仓商品成本变化=》入仓产品及出仓产品更新cost_price cost_amount
                                if (Convert.ToString(item.inout_flag) == "-1" && item.red_flag == "")
                                {
                                    // var costPrice = CPubVars.ToDecimal(item.unit_factor) * now_cost_price_avg;
                                    //var costAmount = costPrice * CPubVars.ToDecimal(item.sheet_qty);
                                    //更新对应出仓商品数据
                                    //  updateSaleSql += @$"update sheet_combine_detail set cost_price_avg = {now_cost_price_avg},cost_price = {costPrice},cost_amount = {costAmount} where company_id = {company_id} and sheet_id={item.sheet_id} and item_id = {item.item_id} and inout_flag = -1;";
                                    updateSaleSql += @$"update sheet_combine_detail set cost_price_avg = {now_cost_price_avg},cost_price =unit_factor * {now_cost_price_avg},cost_amount =quantity * unit_factor * {now_cost_price_avg} where company_id = {company_id} and sheet_id={item.sheet_id} and item_id = {item.item_id} and inout_flag = -1;";

                                    SQLQueue QQ = new SQLQueue(cmd);
                                    QQ.Enqueue("sheetRowsData", $"select * from sheet_combine_detail where company_id ={this.company_id} and sheet_id = {item.sheet_id} ");
                                    List<ExpandoObject> sheetRowsData = null;
                                    var inRows = new List<ExpandoObject>();
                                    var outRows = new List<ExpandoObject>();
                                    var dr = await QQ.ExecuteReaderAsync();
                                    while (QQ.Count > 0)
                                    {
                                        var sqlName = QQ.Dequeue();
                                        if (sqlName == "sheetRowsData") sheetRowsData = CDbDealer.GetRecordsFromDr(dr, false);
                                    }
                                    QQ.Clear();
                                    foreach (dynamic rowdata in sheetRowsData)
                                    {
                                        if (rowdata.inout_flag == "1") inRows.Add(rowdata);
                                        if (rowdata.inout_flag == "-1") outRows.Add(rowdata);
                                    }
                                    decimal totalCost = 0;//差额
                                    foreach (dynamic rowdata in outRows)
                                    {
                                        if (item.item_id == rowdata.item_id)
                                        {
                                            if (rowdata.cost_price != "")
                                            {
                                                rowdata.cost_amount = CPubVars.ToDecimal(rowdata.cost_price) * CPubVars.ToDecimal(rowdata.quantity);
                                            }
                                            else rowdata.cost_amount = 0;

                                            totalCost += ((now_cost_price_avg * CPubVars.ToDecimal(rowdata.quantity) * CPubVars.ToDecimal(rowdata.unit_factor)) - CPubVars.ToDecimal(rowdata.cost_amount));
                                        }
                                    }
                                    decimal totalOldToBranchCost = 0;
                                    foreach (dynamic rowdata in inRows)
                                    {
                                        if (rowdata.cost_amount == "") rowdata.cost_amount = 0;
                                        totalOldToBranchCost += CPubVars.ToDecimal(rowdata.cost_amount);
                                    }
                                    foreach (dynamic rowdata in inRows)
                                    {
                                        decimal newCostAmount = 0;
                                        if (totalOldToBranchCost != 0) newCostAmount = CPubVars.ToDecimal(rowdata.cost_amount) / totalOldToBranchCost * totalCost;
                                        decimal newCostPrice = newCostAmount / CPubVars.ToDecimal(rowdata.quantity);
                                        updateSaleSql += @$"update sheet_combine_detail set cost_price =cost_price + {newCostPrice},cost_amount =cost_amount + {newCostAmount}  where company_id = {company_id} and item_id = {rowdata.item_id} and sheet_id = {rowdata.sheet_id} and inout_flag = 1;";
                                    }
                                }
                                else
                                {
                                    updateSaleSql += @$"update sheet_combine_detail set cost_price_avg = {pre_cost_price_avg} where company_id = {company_id} and item_id = {item.item_id} and sheet_id = {item.sheet_id};";
                                }
                            }
                        }
                        else if (item.sheet_type == "YK" || item.sheet_type == "BS")
                        {
                            //if (!bSuspecting)
                            {
                                updateSaleSql += @$"update sheet_invent_change_detail set cost_price_avg = {now_cost_price_avg} where company_id = {company_id} and item_id = {item.item_id} and sheet_id = {item.sheet_id};";
                            }
                        }
                    }
                }

                if (sheetRow.item_cost_price_suspect != bSuspecting || now_cost_price_avg != sheetRow.cost_price_avg)
                {
                    CDbDealer db = new CDbDealer();
                    if (sheetRow.item_cost_price_suspect != bSuspecting) db.AddField("item_cost_price_suspect", bSuspecting.ToString());
                    if (!bSuspecting && now_cost_price_avg != sheetRow.cost_price_avg) db.AddField("cost_price_avg", now_cost_price_avg.ToString());
                    if (db.FieldCount > 0)
                    {
                        updateItemProp += db.GetUpdateSQL("info_item_prop", $"company_id = {this.company_id} and item_id = {sheetRow.item_id}") + ";";

                        log_sql += @$"insert into cost_price_log (
          company_id,           sheet_id,       item_id,      happen_time,         sheet_type,        infected_sheet_id,   new_cost_price) 
 values ({this.company_id},{this.sheet_id},{sheetRow.item_id},'{this.happen_time}','{this.SheetType}','{this.sheet_id}',  {now_cost_price_avg.ToString()})
on conflict(company_id,sheet_id,       item_id,   happen_time,        sheet_type, infected_sheet_id) do nothing;";

                    }
                }
            }

            string sql = updateItemProp + updateSaleSql + log_sql;
            if (sql != "")
            {
                cmd.CommandText = sql;
                await cmd.ExecuteNonQueryAsync();
            }
        }

        /// <summary>
        /// 获取此单据中的商品在 发生时间 后面的所有单据(采购/盘点盈亏/销售)中出现的记录
        /// </summary>
        /// <param name="cmd"></param>
        /// <param name="dealing_happen_time">此单据的发生时间,如果是红冲单,就是被红冲的单据的发生时间</param>
        /// <returns></returns>
        public async Task<JsonResult> GetHistorySheetItemsAfterMe(CMySbCommand cmd, List<SheetRowCostPrice> lstSheetRows, string dealing_happen_time)
        {
            string sql = "";
            string items_id = string.Join(",", lstSheetRows.Select(r => r.item_id));
            SQLQueue QQ = new SQLQueue(cmd);
            var condi = $" and sd.sheet_id<>{this.sheet_id} ";

            // if (red_flag == "2") condi = $" and sd.sheet_id<>{this.red_sheet_id} ";
            //找出插入单据那一刻
            //订单仅改自身成本，不加入计算
            sql = @$" 
select        sheet_id,sheet_type,red_flag,money_inout_flag, t.happen_time, t.item_id,quantity,t.unit_factor,inout_flag,t.cost_price_avg,    cost_price_suspect,                        cost_price_buy,  sub_amount,    s_real_price,      ip.cost_price_avg as cost_price_avg_info from 
(
    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,sd.cost_price_avg ,   cost_price_suspect,                        cost_price_buy, sub_amount,real_price/unit_factor s_real_price , m.red_flag
    from sheet_sale_detail sd 
    left join sheet_sale_main m on m.sheet_id = sd.sheet_id and m.company_id = sd.company_id
    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>'{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}
     
    UNION

    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,sd.cost_price_avg , false  cost_price_suspect,                        cost_price_buy, sub_amount,real_price/unit_factor s_real_price , m.red_flag
    from sheet_sale_order_detail sd 
    left join sheet_sale_order_main m on m.sheet_id = sd.sheet_id and m.company_id = sd.company_id
    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>'{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}
   
    UNION

  select sd.sheet_id,sheet_type,0 as money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,cost_price_avg , false cost_price_suspect,buy_price as cost_price_buy, sd.cost_amount as sub_amount,(sd.cost_price/sd.unit_factor) as s_real_price,m.red_flag
    from sheet_combine_detail sd 
    left join sheet_combine_main m on m.sheet_id = sd.sheet_id and m.company_id = sd.company_id
    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>'{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}
     
    UNION
    
    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,sd.cost_price_avg,false cost_price_suspect, real_price/unit_factor cost_price_buy, coalesce(sub_amount,0)+coalesce(allocate_amount,0) as sub_amount,real_price/unit_factor s_real_price  , m.red_flag
    from sheet_buy_detail sd 
    left join sheet_buy_main m on sd.sheet_id = m.sheet_id and m.company_id = sd.company_id
    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>'{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}
     
    UNION
     
    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,sd.cost_price_avg,false cost_price_suspect,           sd.buy_price  cost_price_buy,0 sub_amount,0 s_real_price    , m.red_flag
    from sheet_invent_change_detail sd left join sheet_invent_change_main m on sd.sheet_id = m.sheet_id  and m.company_id = sd.company_id
    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>'{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}
    
    UNION
     
    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,0 quantity,  unit_factor,inout_flag,old_avg_price/unit_factor cost_price_avg,false cost_price_suspect,   0 cost_price_buy,0 sub_amount, (case when red_flag is null then real_price/unit_factor else old_avg_price/unit_factor end) s_real_price   , m.red_flag
    from  sheet_cost_price_adjust_detail sd left join sheet_cost_price_adjust_main m on sd.sheet_id = m.sheet_id  and m.company_id = sd.company_id
    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>'{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}
    
    UNION

    select sd.sheet_id,sheet_type,money_inout_flag,sd.happen_time,sd.item_id,quantity,  unit_factor,inout_flag,sd.cost_price_avg,false cost_price_suspect,   0 cost_price_buy,0 sub_amount, 0 s_real_price   , m.red_flag
    from  sheet_stock_in_out_detail sd left join sheet_stock_in_out_main m on sd.sheet_id = m.sheet_id  and m.company_id = sd.company_id
    where sd.company_id = {this.company_id} and sd.item_id in ({items_id}) and sd.happen_time>'{dealing_happen_time}' and m.red_flag is null and m.approve_time is not null {condi}

) t 
left join info_item_prop ip on t.item_id = ip.item_id and ip.company_id={this.company_id}
order by t.item_id,t.happen_time";
            QQ.Enqueue("data", sql);
            // sql = $@"select t.item_id,sum(coalesce(sheet_qty,0)) qty_change,sum(coalesce(sheet_amount,0)) amount_change from ({sql}) t 
            //             group by t.item_id ";
            // QQ.Enqueue("total", sql);
            List<ExpandoObject> data = null;
            // List<ExpandoObject> total = null;
            var dr = await QQ.ExecuteReaderAsync();
            while (QQ.Count > 0)
            {
                var sqlName = QQ.Dequeue();
                if (sqlName == "data") data = CDbDealer.GetRecordsFromDr(dr, false);
                // else if (sqlName == "total") total = CDbDealer.GetRecordsFromDr(dr, false);
            }
            QQ.Clear();
            return new JsonResult(new { result = "OK", data });
        }
        /// <summary>
        /// 将单据中相同item_id的商品合并到一行
        /// </summary>
        /// <param name="rows"></param>
        /// <returns></returns>
     


        #endregion
        public struct GetSheetsUsage
        {
            public bool GetSumSheet;
            public bool GetReturnSumSheet;
            public bool GetSheetsMainInfo;
            public bool GetEachSheet;
            public bool GetSmallUnitSheet;
            public bool GetBigUnitSumSheet;
            public bool ForPrint;
            public bool SplitUnitRows;
            public string OptionToRemember;

        }
        public class GetSheetsResult//<TSheet>
        {
            public class SheetGroup
            {
                public dynamic template;
                //  public List<dynamic> sheets = new List<dynamic>();
                public string groupType = "";
                public dynamic sheets = new List<dynamic>();
               // public Dictionary<string, string> variables = new Dictionary<string, string>();
            }
            public string result = "";
            public string msg = "";
            public string sheetIDs = "";
            public string operName = "";
            public Dictionary<string, string> templVariables = new Dictionary<string, string>();
            public List<SheetGroup> sheetGroup = new List<SheetGroup>();
            public List<ExpandoObject> cloudPrinters = new List<ExpandoObject>();
        }
        public static Type typeFromName(string typeName)
        {
            Type type = null;
            Assembly[] assemblyArray = AppDomain.CurrentDomain.GetAssemblies();
            int assemblyArrayLength = assemblyArray.Length;
            for (int i = 0; i < assemblyArrayLength; ++i)
            {
                type = assemblyArray[i].GetType(typeName);
                if (type != null)
                {
                    return type;
                }
            }

            for (int i = 0; (i < assemblyArrayLength); ++i)
            {
                Type[] typeArray = assemblyArray[i].GetTypes();
                int typeArrayLength = typeArray.Length;
                for (int j = 0; j < typeArrayLength; ++j)
                {
                    if (typeArray[j].Name.Equals(typeName))
                    {
                        return typeArray[j];
                    }
                }
            }
            return type;
        }
        public virtual List<TROW> MergeSheetRows(List<TROW> rows, bool bIgnoreNativeQty = false)
        {
            return null;

        }
        protected static T DeepCopy<T>(T obj)
        {
            var type = obj.GetType();
            var o = Activator.CreateInstance(type);
            var pi = type.GetProperties();
            foreach (var p in pi)
            {
                if (p.CanWrite)
                    p.SetValue(o, p.GetValue(obj));
            }
            return (T)o;
        }
        public static List<TSheetRow> SplitToMultiUnitRows<TSheetRow>(List<TSheetRow> sheetRows) where TSheetRow : SheetRowItem
        {
            List<TSheetRow> lstNewRows = new List<TSheetRow>();
            foreach (var row in sheetRows)
            {
                decimal bUnitFactor, quantityB, leftQuantity = row.quantity, mUnitFactor, quantityM;
                var unitExits = new Dictionary<string, bool>
                {
                    ["s"] = !string.IsNullOrWhiteSpace(row.s_unit_no),
                    ["m"] = !string.IsNullOrWhiteSpace(row.m_unit_no),
                    ["b"] = !string.IsNullOrWhiteSpace(row.b_unit_no)
                };
                if (unitExits["b"] && leftQuantity >= CPubVars.ToDecimal(row.b_unit_factor))
                {
                    bUnitFactor = CPubVars.ToDecimal(row.b_unit_factor);
                    quantityB = Math.Floor(leftQuantity / bUnitFactor);
                    leftQuantity -= bUnitFactor * quantityB;

                    var newRow = DeepCopy(row);
                    newRow.quantity = quantityB;
                    newRow.unit_no = row.b_unit_no;
                    newRow.unit_factor = CPubVars.ToDecimal(row.b_unit_factor);
                    lstNewRows.Add(newRow);
                }

                if (unitExits["m"] && leftQuantity >= CPubVars.ToDecimal(row.m_unit_factor))
                {
                    mUnitFactor = CPubVars.ToDecimal(row.m_unit_factor);
                    quantityM = Math.Floor(leftQuantity / mUnitFactor);
                    leftQuantity -= mUnitFactor * quantityM;

                    var newRow = DeepCopy(row);
                    newRow.quantity = quantityM;
                    newRow.unit_no = row.m_unit_no;
                    newRow.unit_factor = CPubVars.ToDecimal(row.m_unit_factor);
                    lstNewRows.Add(newRow);
                }

                if (leftQuantity > 0)
                {
                    var newRow = DeepCopy(row);
                    newRow.quantity = leftQuantity;
                    newRow.unit_no = row.s_unit_no;
                    newRow.unit_factor = 1;
                    lstNewRows.Add(newRow);
                }
            }
            return lstNewRows;
        }
        public class ItemUnitQty
        {
            public string unit_no = "";
            public decimal unit_factor = 1;
            public decimal quantity = 0;
        }
        public static TSheet GetSumSheet<TSheet, TSheetRow>(List<TSheet> sheets, bool bForReturn, bool smallUnitBarcode, bool splitUnitRows) where TSheetRow : SheetRowItem, new() where TSheet : SheetBase<TSheetRow>, new()
        {
            TSheet sumSheet = null;

            string sheet_nos = "";
            string sup_names = "";
            decimal total_amount = 0;
            string sumUnitQty = "";
            List<dynamic> lstSheetsInfo = new List<dynamic>();
            //string sheetType = "";
            Dictionary<string, TSheetRow> dicRows = new Dictionary<string, TSheetRow>();
            string sheet_ids = "";
            string senders_name = "";
            string sender_mobile = "";
            List<string> lstVanName = new List<string>();
            List<string> lstMoveSheetId = new List<string>();
            List<string> lstMoveSheetNo = new List<string>();
            decimal now_disc_amount = 0, now_pay_amount = 0;//, left_amount=0;
															//Dictionary<string, string> branchIDsDic= new Dictionary<string, string>();
															//string branch_ids = "";
															//string branch_names = "";
			Dictionary<TSheetRow, Dictionary<string, ItemUnitQty>> dicRowUnits = new Dictionary<TSheetRow, Dictionary<string, ItemUnitQty>>();
	        Dictionary<string, ItemUnitQty> dicSumRowUnits = new Dictionary<string, ItemUnitQty>();


			foreach (var sheet1 in sheets)
            {
                if (sheet_nos != "") sheet_nos += ", ";
                if (sup_names != "") sup_names += ", ";



                if (sumSheet == null) sumSheet = JsonConvert.DeserializeObject<TSheet>(JsonConvert.SerializeObject(sheet1));
                if (sheet1.red_flag != "") continue;
                int flag = 1;
                if (bForReturn)
                {
                    if (sheet1.SheetType == "X" || sheet1.SheetType == "XD") flag = -1;
                }
                else
                {
                    if (sheet1.SheetType == "T" || sheet1.SheetType == "TD") continue;
                }
                dynamic dSheet = sheet1;
                if (typeof(SheetSaleOrder) == sheet1.GetType() || typeof(SheetSale) == sheet1.GetType())
                {
                    if (dSheet.senders_name != null && dSheet.senders_name != "")
                    {
                        if (!("," + senders_name + ",").Contains("," + dSheet.senders_name + ","))
                        {
                            if (senders_name != "") senders_name += ",";
                            senders_name += dSheet.senders_name;
                        }
                    }
                    if (dSheet.sender_mobile != null && dSheet.sender_mobile != "")
                    {
                        if (!("," + sender_mobile + ",").Contains("," + dSheet.sender_mobile + ","))
                        {
                            if (sender_mobile != "") sender_mobile += ",";
                            sender_mobile += dSheet.sender_mobile;
                        }
                    }
                    if (",XD,TD,".Contains("," + sheet1.SheetType + ","))
                    {
                        if (!lstVanName.Contains(dSheet.van_name))
                        {
                            lstVanName.Add(dSheet.van_name);
                        }
                        if (!lstMoveSheetId.Contains(dSheet.move_sheet_id))
                        {
                            lstMoveSheetId.Add(dSheet.move_sheet_id);
                        }
                        if (!lstMoveSheetNo.Contains(dSheet.move_sheet_no))
                        {
                            lstMoveSheetNo.Add(dSheet.move_sheet_no);
                        }
                    }
                }

                if (sheet1.sheet_no.Length > 6)
                {
                    sheet_nos += sheet1.sheet_no.Substring(sheet1.sheet_no.Length - 6, 6);
                }

                if (sheet_ids != "") sheet_ids += ",";
                sheet_ids += sheet1.sheet_id;

                if (",X,T,XD,TD,CG,CT,CD,".Contains("," + sheet1.SheetType + ","))
                {
                    if (dSheet.sup_name != null && dSheet.sup_name != "")
                    {
                        sup_names += dSheet.sup_name;
                    }
                    if (((dynamic)sheet1).now_disc_amount != null)
                    {
                        now_disc_amount += (decimal)((dynamic)sheet1).now_disc_amount;
                    }
                    if (((dynamic)sheet1).now_pay_amount != null)
                    {
                        now_pay_amount += (decimal)((dynamic)sheet1).now_pay_amount;
                    }
                }

                lstSheetsInfo.Add(new { sheet1.sheet_id, sheet1.SheetType });

                total_amount += sheet1.total_amount * sheet1.money_inout_flag;

                //if (((dynamic)sheet1).left_amount != null)
                //{
                //    left_amount += (decimal)((dynamic)sheet1).left_amount;
                //}
                decimal b_qty = 0, m_qty = 0, s_qty = 0;
                // Dictionary<string, decimal> dicRealUnitQty = new Dictionary<string, decimal>();

               

				foreach (var row in sheet1.SheetRows)
                {
                    string rowBranchId = "";
                    string rowBranchName = "";
                    string rowBranchPosition = "";
                    string rowBranchPositionName = "";
                    if (sheet1.SheetType == "DB")
                    {
                        rowBranchId = sheet1.GetType().GetProperty("from_branch_id").GetValue(sheet1).ToString();
                        rowBranchName = sheet1.GetType().GetProperty("from_branch_name").GetValue(sheet1).ToString();
                        rowBranchPosition = row.GetType().GetProperty("from_branch_position").GetValue(row).ToString();
                        rowBranchPositionName = row.GetType().GetProperty("from_branch_position_name").GetValue(row).ToString();
                    }
                    else
                    {
                        rowBranchId = row.branch_id;
                        rowBranchName = row.branch_name;
                        if (rowBranchId.IsInvalid())
                        {
                            rowBranchId = sheet1.GetType().GetProperty("branch_id").GetValue(sheet1).ToString();
                            rowBranchName = sheet1.GetType().GetProperty("branch_name").GetValue(sheet1).ToString();
                            row.branch_id = rowBranchId;//
                            row.branch_name = rowBranchName;
                        }
                        //rowBranchPosition = row.GetType().GetProperty("branch_position").GetValue(row).ToString();
                        //rowBranchPositionName = row.GetType().GetProperty("branch_position_name").GetValue(row).ToString();
                        rowBranchPosition = row.branch_position;
                        rowBranchPosition = row.branch_position_name;
                    }

                    row.SetInfoForPrint(smallUnitBarcode);
                    decimal qty = row.quantity * flag;
                    if (qty < 0) continue;
                    b_qty += row.b_quantity;
                    m_qty += row.m_quantity;
                    s_qty += row.s_quantity;
					
                     


                    TSheetRow sumRow;
                    if (dicRows.ContainsKey(row.item_name + row.batch_id + rowBranchId + rowBranchPosition))
                    {
                        sumRow = dicRows[row.item_name + row.batch_id + rowBranchId + rowBranchPosition];
                        if (sumRow is SheetRowMM sumRowMM)
                        {
                            if (row is SheetRowMM rowMM)
                            {
                                sumRowMM.sub_amount += rowMM.sub_amount;
                            }
                        }

                        if (row.remark != "" && row.remark != null)
                        {
                            if (sumRow.remark != "" && sumRow.remark != null && !sumRow.remark.Contains(row.remark + ";")) sumRow.remark += ";";
                            sumRow.remark += row.remark;
                        }

                    }
                    else
                    {
                        sumRow = JsonConvert.DeserializeObject<TSheetRow>(JsonConvert.SerializeObject(row));

                        sumRow.quantity = 0;
                        sumRow.unit_no = row.s_unit_no;
                        sumRow.unit_factor = 1;
                        if (sumRow is SheetRowMM sumRowMM)
                        {
                            //     sumRowMM.real_price= sumRowMM.real_price/
                        }
                        sumRow.b_unit_factor = row.b_unit_factor;
                        sumRow.m_unit_factor = row.m_unit_factor;
                        sumRow.s_unit_no = row.s_unit_no;
                        sumRow.b_unit_no = row.b_unit_no;
                        sumRow.m_unit_no = row.m_unit_no;
                        
                        //sumRow.unit_no = row.unit_no;
                        //sumRow.unit_factor = row.unit_factor;
                        //sumRow.batch_id = row.batch_id;
                        //sumRow.batch_no = row.batch_no;
                        //sumRow.batch_level = row.batch_level;
                        //sumRow.produce_date =row.produce_date;
                        dicRows.Add(row.item_name + row.batch_id + rowBranchId + rowBranchPosition, sumRow);
                    }

                    sumRow.quantity += row.quantity * row.unit_factor;

                    sumRow.barcode = row.s_barcode;
                    dicRowUnits.TryGetValue(sumRow, out var dicUnitQty);
                    if (dicUnitQty == null) dicUnitQty=dicRowUnits[sumRow] = new Dictionary<string, ItemUnitQty>();
                   /* ItemUnitQty unitQty = null;

					if (row.b_quantity > 0)
                    {
                        string cur_unit_no = row.b_unit_no;
                        decimal cur_unit_factor = CPubVars.ToDecimal(row.b_unit_factor);
                        decimal cur_qty = row.b_quantity;
						dicUnitQty.TryGetValue(cur_unit_no, out unitQty);
						if (unitQty == null)
						{
							unitQty = dicUnitQty[cur_unit_no] = new ItemUnitQty();
							unitQty.unit_no = cur_unit_no;
							unitQty.unit_factor = cur_unit_factor;
							unitQty.quantity = cur_qty;
						}
						else
						{
							unitQty.quantity += cur_qty;
						}
					}
					if (row.m_quantity > 0)
					{
						string cur_unit_no = row.m_unit_no;
						decimal cur_unit_factor = CPubVars.ToDecimal(row.m_unit_factor);
						decimal cur_qty = row.m_quantity;
						dicUnitQty.TryGetValue(cur_unit_no, out unitQty);
						if (unitQty == null)
						{
							unitQty = dicUnitQty[cur_unit_no] = new ItemUnitQty();
							unitQty.unit_no = cur_unit_no;
							unitQty.unit_factor = cur_unit_factor;
							unitQty.quantity = cur_qty;
						}
						else
						{
							unitQty.quantity += cur_qty;
						}
					}
					if (row.s_quantity > 0)
					{
						string cur_unit_no = row.s_unit_no;
						decimal cur_unit_factor = 1;
						decimal cur_qty = row.s_quantity;
						dicUnitQty.TryGetValue(cur_unit_no, out unitQty);
						if (unitQty == null)
						{
							unitQty = dicUnitQty[cur_unit_no] = new ItemUnitQty();
							unitQty.unit_no = cur_unit_no;
							unitQty.unit_factor = cur_unit_factor;
							unitQty.quantity = cur_qty;
						}
						else
						{
							unitQty.quantity += cur_qty;
						}
					}
                    */
					dicUnitQty.TryGetValue(row.unit_no, out ItemUnitQty unitQty);
                    if(unitQty == null)
                    {
                        unitQty= dicUnitQty[row.unit_no]= new ItemUnitQty();
                        unitQty.unit_no= row.unit_no;
                        unitQty.unit_factor = row.unit_factor;
                        unitQty.quantity = row.quantity;
					}
                    else
                    {
                        unitQty.quantity += row.quantity; 
					}

					

					{
                        if (sumRow is SheetRowMM sumRowMM)
                        {
                            if (row is SheetRowMM rowMM)
                            {
                                 
								if (rowMM.real_price > 0)
                                {
                                    sumRowMM.sale_quantity += rowMM.quantity * rowMM.unit_factor;
                                }
                                else
                                {
                                    sumRowMM.give_quantity += rowMM.quantity * rowMM.unit_factor;
                                }
                            }
                        }
                    }
                }

                sumUnitQty = "";
                if (b_qty != 0) sumUnitQty += b_qty.ToString() + "大";
                if (m_qty != 0) sumUnitQty += m_qty.ToString() + "中";
                if (s_qty != 0) sumUnitQty += s_qty.ToString() + "小";
                sheet1.sum_quantity_unit_conv = sumUnitQty;
			 


			}

            decimal sum_b_qty = 0, sum_m_qty = 0, sum_s_qty = 0;
            decimal sum_b_sale_qty = 0, sum_m_sale_qty = 0, sum_s_sale_qty = 0;
            decimal sum_b_give_qty = 0, sum_m_give_qty = 0, sum_s_give_qty = 0;
			decimal sum_sub_amount = 0m;  

			Dictionary<string, decimal> dic_sum_quantity_unit_conv_real = new Dictionary<string, decimal>();
			List<TSheetRow> sumRows = new List<TSheetRow>();
            foreach (var k in dicRows)
            {
                var row = k.Value;
                //row.quantity_unit_conv =
                GetUnitQtyFromSmallUnitQty(row, row.quantity, out decimal b_qty, out decimal m_qty, out decimal s_qty);
                {
                    if (row is SheetRowMM rowMM)
                    {
                        rowMM.sale_quantity_unit_conv = GetUnitQtyFromSmallUnitQty(rowMM, rowMM.sale_quantity, out decimal b_sale_qty, out decimal m_sale_qty, out decimal s_sale_qty); ;
                        rowMM.give_quantity_unit_conv = GetUnitQtyFromSmallUnitQty(rowMM, rowMM.give_quantity, out decimal b_give_qty, out decimal m_give_qty, out decimal s_give_qty); ;

				 
						sum_b_sale_qty += b_sale_qty;
                        sum_m_sale_qty += m_sale_qty;
                        sum_s_sale_qty += s_sale_qty;

                        
                        sum_b_give_qty += b_give_qty;
                        sum_m_give_qty += m_give_qty;
                        sum_s_give_qty += s_give_qty;

                        if (rowMM.attr_qty != "")
                        {

                        }
                    }
                }

                sum_b_qty += b_qty;
                sum_m_qty += m_qty;
                sum_s_qty += s_qty;
                void addUnitQty(string unitNo,decimal qty)
                {
					if (dic_sum_quantity_unit_conv_real.ContainsKey(unitNo))
					{
						dic_sum_quantity_unit_conv_real[unitNo] += qty;
					}
					else
						dic_sum_quantity_unit_conv_real[unitNo] = qty;
				}
                if (b_qty > 0) addUnitQty(row.b_unit_no, b_qty);
				if (m_qty > 0) addUnitQty(row.m_unit_no, m_qty);
				if (s_qty > 0) addUnitQty(row.s_unit_no, s_qty);

				{
                    if (row is SheetRowMM rowMM)
                    {
						sum_sub_amount += rowMM.sub_amount;
						if (rowMM.b_unit_factor.IsValid() && rowMM.sale_quantity != 0)
                            rowMM.real_price = rowMM.sub_amount / rowMM.sale_quantity;
                        /*
                        if (rowMM.b_unit_factor != "" && rowMM.sale_quantity > 0)
                        {
                            //rowMM.real_price = rowMM.sub_amount / (rowMM.sale_quantity / CPubVars.ToDecimal(rowMM.b_unit_factor));
                            rowMM.real_price = rowMM.sub_amount / rowMM.sale_quantity;
                            //rowMM.b_real_price=CPubVars.FormatMoney(rowMM.sub_amount / (rowMM.sale_quantity / Convert.ToSingle(rowMM.b_unit_factor)),2);
                        }
                        else
                        { 
                            rowMM.real_price = rowMM.sub_amount / rowMM.sale_quantity;
                        }
                        */
                        //if (rowMM.sale_quantity != 0)
                        //rowMM.real_price = rowMM.sub_amount / rowMM.sale_quantity;
                        //rowMM.real_price = Convert.ToSingle(CPubVars.FormatMoney(rowMM.real_price, 2));
                    }
                }
                sumRows.Add(row);

				

				dicRowUnits.TryGetValue(row, out var dicItemUnitQty);
                if (dicItemUnitQty != null)
                {
                    dicItemUnitQty.OrderByDescending(unit => unit.Value.unit_factor);
                    row.real_qty_unit = "";
                    foreach(var kp in dicItemUnitQty)
                    {
                        row.real_qty_unit += kp.Value.quantity + kp.Key;

                        dicSumRowUnits.TryGetValue(kp.Key, out ItemUnitQty sumItemUnitQty);
                        if (sumItemUnitQty == null)
                        {
                            sumItemUnitQty = dicSumRowUnits[kp.Key] = new ItemUnitQty();
                            sumItemUnitQty.unit_no = kp.Value.unit_no;
                            sumItemUnitQty.unit_factor=kp.Value.unit_factor;
                            sumItemUnitQty.quantity = kp.Value.quantity;

						}
                        else
                        {
                            sumItemUnitQty.quantity += kp.Value.quantity;
                        }
					}
				}
               
                
            }

			dicSumRowUnits.OrderByDescending(unit => unit.Value.unit_factor);
            foreach (var kp in dicSumRowUnits)
            {
              sumSheet.sum_real_qty_unit +=  kp.Value.quantity + kp.Key;
			}

			// dic_sum_quantity_unit_conv_real.OrderByDescending(unit => unit.Value.unit_factor);
			foreach (var kp in dic_sum_quantity_unit_conv_real)
			{
				sumSheet.sum_quantity_unit_conv_real += CPubVars.FormatMoney(kp.Value,3) + kp.Key;
			}

			//如果不更新sum_sub_amount，客户端会显示小计列的合计，因为 单据类增加了 sum_sub_amount，所以会依赖这个属性，而不是根据row重算
			if (sumSheet is SheetSale sheetSale)
			{
				sheetSale.sum_sub_amount = CPubVars.FormatMoney(sum_sub_amount);
			}
			else if (sumSheet is SheetSaleOrder sheetSalerOder)
			{
				sheetSalerOder.sum_sub_amount = CPubVars.FormatMoney(sum_sub_amount);
			}
			else if (sumSheet is SheetBuy sheeBuy)
			{
				sheeBuy.sum_sub_amount = CPubVars.FormatMoney(sum_sub_amount);
			}
			else if (sumSheet is SheetBuyOrder sheetBuyOrder)
			{
				sheetBuyOrder.sum_sub_amount = CPubVars.FormatMoney(sum_sub_amount);
			}

			sumRows.Sort((TSheetRow r1, TSheetRow r2) =>
            {

                int b = CPubVars.CompareString(r1.brand_name ?? "", r2.brand_name ?? "");
                if (b != 0) return b;


                if (r1 is SheetRowMM mmr1 && r2 is SheetRowMM mmr2)
                {
                    int class1_order_index1 = 100000, class1_order_index2 = 100000;
                    if (mmr1.class1_order_index != "") class1_order_index1 = Convert.ToInt32(mmr1.class1_order_index);
                    if (mmr2.class1_order_index != "") class1_order_index2 = Convert.ToInt32(mmr2.class1_order_index);
                    int ci = class1_order_index1.CompareTo(class1_order_index2);
                    if (ci != 0) return ci;
                    if (mmr1.class1_name != null && mmr2.class1_name != null)
                    {
                        int c = CPubVars.CompareString(mmr1.class1_name, mmr2.class1_name);
                        if (c != 0) return c;
                    }
                }

                {
                    int class_order_index1 = 100000, class_order_index2 = 100000;
                    if (r1.class_order_index != "") class_order_index1 = Convert.ToInt32(r1.class_order_index);
                    if (r2.class_order_index != "") class_order_index2 = Convert.ToInt32(r2.class_order_index);

                    int ci = class_order_index1.CompareTo(class_order_index2);
                    if (ci != 0) return ci;

                    if (r1.class_name != null && r2.class_name != null)
                    {
                        int c = CPubVars.CompareString(r1.class_name, r2.class_name);
                        if (c != 0) return c;
                    }


                    int item_order_index1 = 100000, item_order_index2 = 100000;
                    if (r1.item_order_index != "") item_order_index1 = Convert.ToInt32(r1.item_order_index);
                    if (r2.item_order_index != "") item_order_index2 = Convert.ToInt32(r2.item_order_index);
                    int ii = item_order_index1.CompareTo(item_order_index2);
                    if (ii != 0) return ii;

                    int nm = CPubVars.CompareString(r1.item_name, r2.item_name);
                    if (nm != 0) return nm;
                }
                return 0;

            });

            sumUnitQty = "";
            if (sum_b_qty != 0) sumUnitQty += sum_b_qty.ToString() + "大";
            if (sum_m_qty != 0) sumUnitQty += sum_m_qty.ToString() + "中";
            if (sum_s_qty != 0) sumUnitQty += sum_s_qty.ToString() + "小";

            string sumSaleUnitQty = "";
            if (sum_b_sale_qty != 0) sumSaleUnitQty += sum_b_sale_qty.ToString() + "大";
            if (sum_m_sale_qty != 0) sumSaleUnitQty += sum_m_sale_qty.ToString() + "中";
            if (sum_s_sale_qty != 0) sumSaleUnitQty += sum_s_sale_qty.ToString() + "小";

            string sumGiveUnitQty = "";
            if (sum_b_give_qty != 0) sumGiveUnitQty += sum_b_give_qty.ToString() + "大";
            if (sum_m_give_qty != 0) sumGiveUnitQty += sum_m_give_qty.ToString() + "中";
            if (sum_s_give_qty != 0) sumGiveUnitQty += sum_s_give_qty.ToString() + "小";

            if (sumSheet.SheetType == "TD") sumSheet.SheetType = "XD";
            if (sumSheet.SheetType == "T") sumSheet.SheetType = "X";


            //if (sumSheet == null) sumSheet = new TSheet();
            sumSheet.sheet_id = sheet_ids;
            sumSheet.sheet_no = sheet_nos;
            if (sup_names != "") ((dynamic)sumSheet).sup_name = sup_names;

            sumSheet.sum_quantity_unit_conv = sumUnitQty;
            {
                if (sumSheet is SheetMM<SheetRowSale> sumSheetMM)
                {
                    sumSheetMM.sum_give_quantity_unit_conv = sumGiveUnitQty;
                    sumSheetMM.sum_sale_quantity_unit_conv = sumSaleUnitQty;
                }
            }
            {
                if (sumSheet is SheetMM<SheetRowSaleOrder> sumSheetMM)
                {
                    sumSheetMM.sum_give_quantity_unit_conv = sumGiveUnitQty;
                    sumSheetMM.sum_sale_quantity_unit_conv = sumSaleUnitQty;
                }
            }
            sumSheet.total_amount = total_amount;
            if (splitUnitRows)
            {
                sumRows = SplitToMultiUnitRows(sumRows);
            }
            sumSheet.SheetRows = sumRows;
            dynamic dSumSheet = sumSheet;
            if (senders_name != "")
            {
                dSumSheet.senders_name = senders_name;
            }
            if (senders_name != "")
            {
                dSumSheet.sender_mobile = sender_mobile;
            }
            if (lstVanName.Count() > 0)
            {
                dSumSheet.van_name = string.Join(",", lstVanName);
            }
            if (lstMoveSheetId.Count() > 0)
            {
                dSumSheet.move_sheet_id = string.Join(",", lstMoveSheetId);
            }
            if (lstMoveSheetNo.Count() > 0)
            {
                dSumSheet.move_sheet_no = string.Join(",", lstMoveSheetNo);
            }
            if (now_disc_amount != 0)
                dSumSheet.now_disc_amount = now_disc_amount;
            if (now_pay_amount != 0)
                dSumSheet.now_pay_amount = now_pay_amount;
            sumSheet.IsSum = true;
            return sumSheet;
        }
        protected virtual string GetSQLForTemplates(string companyID, string mainTable, string sheetIDs)
        {
            throw new Exception("GetSQLForTemplates not overrided");
        }
        public static async Task<GetSheetsResult> GetItemSheets<TSheet, TSheetRow>(CMySbCommand cmd, string operKey, string sheetIDs, GetSheetsUsage usage, bool smallUnitBarcode, string clientVersion, string sortColumn, string sortDirection) where TSheetRow : SheetRowItem, new() where TSheet : SheetBase<TSheetRow>, new()
        {
            //  var clsType1 =  MethodBase.GetCurrentMethod().ReflectedType;
            //  System.Diagnostics.StackTrace st = new System.Diagnostics.StackTrace();
            //  string name = st.GetFrame(1).GetMethod().Name;
            //  var clsType=typeFromName(name);

            Security.GetInfoFromOperKey(operKey, out string companyID, out string operID);
            //TSheet sheet = (TSheet)Activator.CreateInstance(typeof(TSheet), new object[] { SHEET_RETURN.NOT_RETURN, LOAD_PURPOSE.SHOW });
            TSheet sheet = (TSheet)Activator.CreateInstance(typeof(TSheet));

            List<TSheet> sheets = null;
            TSheet bigUnitSumSheet = null;
            TSheet sumSheet = null;
            TSheet returnSumSheet = null;
            JObject sheetsMainInfo = null;
            List<TSheet> smallUnitSheets = new List<TSheet>();
            List<TSheet> bigUnitSheets = new List<TSheet>();

            sheets = (List<TSheet>)await sheet.LoadMultiSheets<TSheet>(cmd, companyID, sheetIDs, sortColumn, sortDirection);
            string sheetType = "";
            var firstSheet = sheets.First();
            sheetType = firstSheet.SheetType;

            Dictionary<string, TSheetRow> dicRows = new Dictionary<string, TSheetRow>();
            float nClientVersion = 0;
            if (!string.IsNullOrEmpty(clientVersion))
            {
                nClientVersion = Convert.ToSingle(clientVersion);
            }
            GetSheetsResult res = new GetSheetsResult();

            if (usage.ForPrint)
            {
                foreach (var sht in sheets)
                {
                    if (typeof(SheetSale) == sht.GetType() || typeof(SheetSaleOrder) == sht.GetType())
                    {
                        dynamic dSht = sht;
                        dSht.LoadAttrRowsForPrint(true);
                    }
                }
            }

            if (usage.GetSumSheet)
            {
                sumSheet = GetSumSheet<TSheet, TSheetRow>(sheets, false, smallUnitBarcode, usage.SplitUnitRows);
                sumSheet.sumType = SheetBase<TSheetRow>.SumType.AllSum;
            }

            if (usage.GetReturnSumSheet)
            {
                returnSumSheet = GetSumSheet<TSheet, TSheetRow>(sheets, true, smallUnitBarcode, usage.SplitUnitRows);
                returnSumSheet.sumType = SheetBase<TSheetRow>.SumType.AllSum;
            }

            if (usage.GetSheetsMainInfo)
            {
                //List<JObject> SheetRows = new List<JObject>();
                JArray SheetRows = new JArray();
                foreach (var sht in sheets)
                {
                    PropertyInfo[] properties = typeof(TSheet).GetProperties();
                    JObject row = new JObject();
                    //if (sht.sheet_type == SHEET_TYPE.SHEET_SALE_RETURN || sht.sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN) continue;
                    foreach (PropertyInfo prop in properties)
                    {
                        if ("sheet_no,sup_name,seller_name,senders_name,total_amount,sale_amount,return_amount,no_disc_amount,mobile,sup_addr,left_amount,total_sale_qty_unit,total_return_qty_unit,total_sale_qty_unit_type,total_return_qty_unit_type".Contains(prop.Name))
                        {
                            row[prop.Name] = new JValue(prop.GetValue(sht));
                            // row.Add(prop.Name, prop.GetValue(sht)); 
                        }
                    }
                    SheetRows.Add(row);
                }
                sheetsMainInfo = new JObject();
                sheetsMainInfo["SheetRows"] = SheetRows;


            }

            string company_name = "", company_tel = "", company_address = "";

            dynamic sumTemplate = null;
            dynamic printTemplate = null;
            dynamic sheetsMainTemplate = null;
            string sql = "";
            Dictionary<string, string> variables = new Dictionary<string, string>();
			Dictionary<string, string> sum_variables = new Dictionary<string, string>();
			//Dictionary<string, string> sheet_main_variables = new Dictionary<string, string>();
			
			if (usage.ForPrint)
            {
                sql = sheet.GetSQLForTemplates(companyID, sheet.MainTable, sheetIDs);
                SQLQueue QQ = new SQLQueue(cmd);
                QQ.Enqueue("map", sql);
                string sheetTypes = sheetType;
                string sumSheetType = "";
                if (",X,T,".Contains("," + sheetTypes + ","))
                {
                    sheetTypes = "'X','T'"; sheetType = "X";
					if (usage.GetSumSheet)
                    {
                        sumSheetType = "X_SUM";
					}
                    
				}
                else if (",XD,TD,".Contains("," + sheetTypes + ","))
                {
                    sheetTypes = "'XD','TD'"; sheetType = "XD";
                }
                else if (",CG,CT,".Contains("," + sheetTypes + ","))
                {
                    sheetTypes = "'CG','CT'"; sheetType = "CG";
                }
                else if(",JH,HH".Contains("," + sheetTypes + ","))
                {
                    sheetTypes = "'JH','HH'"; sheetType = "JH"; 
                }
                else
                    sheetTypes = "'" + sheetTypes + "'";

                sql = $"select template_id, template_content from print_template where company_id={companyID} and sheet_type in ({sheetTypes});";
                QQ.Enqueue("template", sql);

                string varSheetTypes = $"'{sheetType}'";
                if (sumSheetType != "") varSheetTypes += $",'{sumSheetType}'";
				sql = $"select avail_elements,sheet_type from print_template_avail_elements where sheet_type in ({varSheetTypes});";
                QQ.Enqueue("avail_elements", sql);
                sql = $"select oper_name from info_operator where oper_id='{operID}';";
                QQ.Enqueue("oper_name", sql);

                List<System.Dynamic.ExpandoObject> mapList = null;
                Dictionary<string, string> dicTemplate = new Dictionary<string, string>();
                CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                while (QQ.Count > 0)
                {
                    string tb = QQ.Dequeue();
                    if (tb == "map")
                    {
                        mapList = CDbDealer.GetRecordsFromDr(dr, false);
                    }
                    else if (tb == "template")
                    {
                        while (dr.Read())
                        {
                            string template_id = CPubVars.GetTextFromDr(dr, "template_id");
                            string template_content = CPubVars.GetTextFromDr(dr, "template_content");
                            dicTemplate.Add(template_id, template_content);
                        }
                    }
                    else if (tb == "avail_elements")
                    {
                        while (dr.Read())
                        {
                            string avail_elements = CPubVars.GetTextFromDr(dr, "avail_elements");
							string sheet_type = CPubVars.GetTextFromDr(dr, "sheet_type");
							dynamic elements = JsonConvert.DeserializeObject(avail_elements);


                            void putAreaVars(Dictionary<string,string>  vars, JArray eles, string prefix = "")
                            {
                                for (var i = 0; i < eles.Count; i++)
                                {
                                    dynamic obj = eles[i];
                                    string name = obj.name;
                                    string title = prefix + (string)obj.title;
                                    if (!vars.ContainsKey(title))
										vars.Add(title, name);
                                }
                            }
                            var curVars = variables;
                            if (sheet_type.Contains("_SUM"))
                            {
                                curVars = sum_variables;
                            }
                            if (elements.pageHead != null) putAreaVars(curVars, elements.pageHead);
                            if (elements.tableHead != null) putAreaVars(curVars, elements.tableHead);
                            if (elements.table != null) putAreaVars(curVars, elements.table, "col_");
                            if (elements.tableTail != null) putAreaVars(curVars, elements.tableTail);
                            if (elements.pageTail != null) putAreaVars(curVars, elements.pageTail);
                        }
                    }
                    else if (tb == "oper_name")
                    {
                        while (dr.Read())
                        {
                            res.operName = CPubVars.GetTextFromDr(dr, "oper_name");
                        }
                    }

                }
                QQ.Clear();

                Dictionary<string, string> dicSheets = new Dictionary<string, string>();

                dynamic dVariables = null;
                
                if(variables.Count>0) dVariables = JsonConvert.DeserializeObject(JsonConvert.SerializeObject(variables));
				int index = 0;
                foreach (dynamic tmp in mapList)
                {
                    string template_content = "";
                    if (dicTemplate.ContainsKey(tmp.template_id))
                    {
                        template_content = dicTemplate[tmp.template_id];
                    }
                    if (template_content == "") continue;

                    if (!dicSheets.ContainsKey(tmp.sheet_id))
                    {
                        dicSheets.Add(tmp.sheet_id, tmp.sheet_id);
                        var sheet1 = sheets.Find((c) => c.sheet_id == tmp.sheet_id);
                        if (tmp.company_name != "")
                            company_name = sheet1.company_name = tmp.company_name;
                        if (tmp.company_tel != "")
                            company_tel = sheet1.company_tel = tmp.company_tel;
                        if (tmp.company_address != "")
                            company_address = sheet1.company_address = tmp.company_address;


                        if (sheet1.sheet_type == SHEET_TYPE.SHEET_SALE_RETURN || sheet1.sheet_type == SHEET_TYPE.SHEET_SALE_DD_RETURN || (tmp.client_group_id != "" && tmp.client_group_id != "0") || (tmp.client_id != "" && tmp.client_id != "0"))
                        {
                            sheet1.printTemplate = JsonConvert.DeserializeObject(template_content);
							sheet1.printTemplate.variables = dVariables;
						}
                        else
                        {
                            printTemplate = JsonConvert.DeserializeObject(template_content);
                            printTemplate.variables = dVariables;
						}
                    }
                    index++;
                }


                if (usage.GetBigUnitSumSheet || usage.GetSumSheet || usage.GetReturnSumSheet)
                {
                    dynamic data = await CDbDealer.Get1RecordFromSQLAsync(@$"
select template_content from print_template t 
left join print_template_choose c on c.company_id={companyID} and t.template_id=c.template_id
where t.company_id={companyID} and t.sheet_type = 'X_SUM'  
order by case when c.template_id is not null then 0 else 1 end", cmd);
                    if (data != null && data.template_content != "")
                    {
                        sumTemplate = JsonConvert.DeserializeObject(data.template_content);
                        sumTemplate.variables =JsonConvert.DeserializeObject(JsonConvert.SerializeObject(sum_variables)); 
					}
                    else
                    {
                        res.msg = "销售商品汇总单模板没有设定";
                    }
                }

                if (usage.GetSheetsMainInfo)
                {
                    dynamic data = await CDbDealer.Get1RecordFromSQLAsync($"select template_content from print_template where company_id={companyID} and sheet_type = 'X_SHEETS_MAIN'", cmd);
                    if (data != null && data.template_content != "")
                    {
                        sheetsMainTemplate = Newtonsoft.Json.JsonConvert.DeserializeObject(data.template_content);
                    }
                    else
                    {
                        res.msg = "客户汇总单模板没有设定";
                    }
                }
            }


            if (usage.GetBigUnitSumSheet || usage.GetSmallUnitSheet)
            {
                foreach (var sheet1 in sheets)
                {
                    if (sheet1.SheetType == "T" || sheet1.SheetType == "TD") continue;

                    var mergedRows = sheet1.MergeSheetRows(sheet1.SheetRows, true);
                    TSheet smallUnitSheet = null;
                    TSheet bigUnitSheet = null;
                    foreach (var row in mergedRows)
                    {
                        decimal leftQty = row.quantity;
                        if (row.s_quantity > 0)
                        {
                            if (smallUnitSheet == null)
                            {
                                smallUnitSheet = JsonConvert.DeserializeObject<TSheet>(JsonConvert.SerializeObject(sheet1));
                                smallUnitSheet.SheetRows.Clear();
                            }
                            var smallUnitRow = JsonConvert.DeserializeObject<TSheetRow>(JsonConvert.SerializeObject(row));
                            // smallUnitRow.b_quantity = 0;
                            //  smallUnitRow.m_quantity = 0;
                            smallUnitRow.quantity = smallUnitRow.s_quantity;
                            smallUnitRow.unit_no = smallUnitRow.s_unit_no;
                            smallUnitRow.unit_factor = 1;
                            smallUnitRow.SetInfoForPrint(smallUnitBarcode);
                            smallUnitSheet.SheetRows.Add(smallUnitRow);
                        }

                        leftQty = row.quantity;
                        if (row.b_quantity > 0 || row.m_quantity > 0)
                        {
                            if (bigUnitSheet == null)
                            {
                                bigUnitSheet = JsonConvert.DeserializeObject<TSheet>(JsonConvert.SerializeObject(sheet1));
                                bigUnitSheet.SheetRows.Clear();
                            }
                            var bigUnitRow = JsonConvert.DeserializeObject<TSheetRow>(JsonConvert.SerializeObject(row));

                            bigUnitRow.quantity -= bigUnitRow.s_quantity;
                            //   bigUnitRow.SetInfoForPrint(smallUnitBarcode);
                            bigUnitRow.unit_no = bigUnitRow.s_unit_no;
                            bigUnitRow.unit_factor = 1;
                            bigUnitSheet.SheetRows.Add(bigUnitRow);
                        }

                    }
                    if (smallUnitSheet != null)
                    {
                        smallUnitSheets.Add(smallUnitSheet);
                    }
                    if (bigUnitSheet != null)
                    {
                        bigUnitSheets.Add(bigUnitSheet);
                    }

                }
                if (bigUnitSheets.Count > 0)
                {
                    bigUnitSumSheet = GetSumSheet<TSheet, TSheetRow>(bigUnitSheets, false, smallUnitBarcode, usage.SplitUnitRows);
                    bigUnitSumSheet.sumType = SheetBase<TSheetRow>.SumType.AllSum;
                }
            }

            if (usage.OptionToRemember.IsValid() && usage.OptionToRemember != "null")
            {
                string opt = usage.OptionToRemember;
                cmd.CommandText = $"insert into options_remembered (company_id,oper_id,options) values ({companyID},{operID},'{opt}'::jsonb) on conflict(company_id,oper_id) do update set options=jsonb_merge(options_remembered.options,'{opt}'::jsonb);";
                await cmd.ExecuteNonQueryAsync();
            }

            if (res.msg == "")
            {
                if (usage.GetSumSheet && sumSheet != null)
                {
                    sumSheet.company_name = company_name;
                    sumSheet.company_tel = company_tel;
                    sumSheet.company_address = company_address;
                    sumSheet.sheet_name = "销售商品汇总";
                    var grp = new GetSheetsResult.SheetGroup();
                    grp.groupType = "SUM";
                    //grp.variables = sum_variables;
                    grp.template = sumTemplate;
                    grp.sheets.Add(sumSheet);
                    res.sheetGroup.Add(grp);
                }
                if (usage.GetReturnSumSheet && returnSumSheet != null)
                {
                    returnSumSheet.company_name = company_name;
                    returnSumSheet.company_tel = company_tel;
                    returnSumSheet.company_address = company_address;
                    returnSumSheet.sheet_name = "退货商品汇总";
                    var grp = new GetSheetsResult.SheetGroup();
                    grp.groupType = "SUM";
                    grp.template = sumTemplate;
					//grp.variables = sum_variables;
					grp.sheets.Add(returnSumSheet);
                    res.sheetGroup.Add(grp);
                }

                if (usage.GetBigUnitSumSheet && bigUnitSumSheet != null)
                {
                    bigUnitSumSheet.company_name = company_name;
                    bigUnitSumSheet.company_tel = company_tel;
                    bigUnitSumSheet.company_address = company_address;
                    // bigUnitSumSheet.sheet_title=
                    bigUnitSumSheet.sheet_name = "整包商品汇总";
                    var grp = new GetSheetsResult.SheetGroup();
                    grp.groupType = "SUM";
                    grp.template = sumTemplate;
					//grp.variables = sum_variables;
					grp.sheets.Add(bigUnitSumSheet);
                    res.sheetGroup.Add(grp);
                }
                if (usage.GetSheetsMainInfo && sheetsMainInfo != null)
                {
                    sheetsMainInfo["company_name"] = new JValue(company_name);
                    sheetsMainInfo["company_tel"] = new JValue(company_tel);
                    sheetsMainInfo["company_address"] = new JValue(company_address);
                    sheetsMainInfo["sheet_name"] = "销售客户汇总单";
                    var grp = new GetSheetsResult.SheetGroup();
                    grp.groupType = "SHEETS_MAIN";
                    grp.template = sheetsMainTemplate;
                    grp.sheets.Add(sheetsMainInfo);
                    res.sheetGroup.Add(grp);
                }
                if (usage.GetEachSheet)
                {
                    var grp = new GetSheetsResult.SheetGroup();
                    grp.template = printTemplate;
                    grp.sheets = sheets;
                    grp.groupType = "EACH";
                    foreach (var sheet1 in sheets)
                    {
                        await sheet1.LoadInfoForPrint(cmd, smallUnitBarcode, false, sheet1.printTemplate ?? printTemplate);
                    }
                    res.sheetGroup.Add(grp);
                }
                if (usage.GetSmallUnitSheet)
                {
                    var grp = new GetSheetsResult.SheetGroup();
                    grp.groupType = "SMALL_UNIT";
                    grp.template = printTemplate;
                    grp.sheets = smallUnitSheets;
                    res.sheetGroup.Add(grp);
                }
                // sheetGroup.Add(new { template = printTemplate, sheets });
            }

            res.sheetIDs = sheetIDs;
            res.result = "OK";
            res.templVariables = variables;


            sql = @$"SELECT P.printer_id, P.printer_name, P.device_id, P.check_code, P.printer_brand
FROM info_cloud_printer P 
WHERE P.company_id = {companyID} AND P.status = 1
ORDER BY P.printer_id LIMIT 10;";
            res.cloudPrinters = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);

            if (res.msg != "")
            {
                res.result = "Error";
            }
            return res;

        }
        public static string GetUnitQtyFromSmallUnitQty(SheetRowItem row, decimal s_quantity, out decimal b_qty, out decimal m_qty, out decimal s_qty)
        {
            b_qty = 0m; m_qty = 0m; s_qty = 0m;

            decimal leftQty = s_quantity;
            string unitsQty = "";
            decimal absLeftQty = Math.Abs(leftQty);
            int flag = leftQty < 0 ? -1 : 1;
            if (row.b_unit_factor.IsValid() && row.b_unit_factor != "1" && row.b_unit_no != "")
            {
                b_qty = (int)(absLeftQty / CPubVars.ToDecimal(row.b_unit_factor));
                absLeftQty = absLeftQty % CPubVars.ToDecimal(row.b_unit_factor);
                if (b_qty < 0.001m) b_qty = 0;

                if (b_qty > 0)
                {
                    b_qty *= flag;
                    unitsQty += b_qty.ToString() + row.b_unit_no;
                }

            }

            if (row.m_unit_factor.IsValid())
            {
                m_qty = (int)(absLeftQty / CPubVars.ToDecimal(row.m_unit_factor));
                absLeftQty = absLeftQty % CPubVars.ToDecimal(row.m_unit_factor);
                if (m_qty < 0.001m) m_qty = 0;

                if (m_qty > 0)
                {
                    m_qty *= flag;
                    unitsQty += m_qty.ToString() + row.m_unit_no;
                }

            }

            s_qty = absLeftQty;
            if (s_qty < 0.001m) s_qty = 0;

            if (s_qty > 0)
            {
                s_qty *= flag; unitsQty += s_qty.ToString() + row.s_unit_no;
            }


            // row.quantity_unit_conv = unitsQty;
            return unitsQty;
        }
        public static string GetUnitQty(decimal s_quantity, string b_unit_no, string m_unit_no, string s_unit_no, string b_unit_factor, string m_unit_factor)
        {
            decimal b_qty, m_qty, s_qty;

            decimal leftQty = s_quantity;
            string unitsQty = "";
            decimal absLeftQty = Math.Abs(leftQty);
            int flag = leftQty < 0 ? -1 : 1;
            if (!string.IsNullOrWhiteSpace(b_unit_factor))
            {
                b_qty = (int)(absLeftQty / CPubVars.ToDecimal(b_unit_factor));
                absLeftQty = absLeftQty % CPubVars.ToDecimal(b_unit_factor);
                if (b_qty < 0.001m) b_qty = 0;

                if (b_qty > 0)
                {
                    b_qty *= flag;
                    unitsQty += CPubVars.FormatMoney(b_qty, 3) + b_unit_no;
                }

            }

            if (!string.IsNullOrWhiteSpace(m_unit_factor))
            {
                m_qty = (int)(absLeftQty / CPubVars.ToDecimal(m_unit_factor));
                absLeftQty = absLeftQty % CPubVars.ToDecimal(m_unit_factor);
                if (m_qty < 0.001m) m_qty = 0;

                if (m_qty > 0)
                {
                    m_qty *= flag;
                    unitsQty += CPubVars.FormatMoney(m_qty, 3) + m_unit_no;
                }

            }

            s_qty = absLeftQty;
            if (s_qty < 0.001m) s_qty = 0;

            if (s_qty > 0)
            {
                s_qty *= flag; unitsQty += CPubVars.FormatMoney(s_qty, 3) + s_unit_no;
            }
            // quantity_unit_conv = unitsQty;
            return unitsQty;
        }
        public static decimal GetQtyOfUnit(decimal s_quantity, string unit_type, string b_unit_factor, string m_unit_factor)
        {
            decimal b_qty, m_qty, s_qty;

            decimal leftQty = s_quantity;
            string unitsQty = "";
            decimal absLeftQty = Math.Abs(leftQty);
            int flag = leftQty < 0 ? -1 : 1;
            if (!string.IsNullOrWhiteSpace(b_unit_factor))
            {
                b_qty = (int)(absLeftQty / CPubVars.ToDecimal(b_unit_factor));
                absLeftQty = absLeftQty % CPubVars.ToDecimal(b_unit_factor);
                if (b_qty < 0.001m) b_qty = 0;

                if (b_qty > 0)
                {
                    b_qty *= flag;
                }
                if (unit_type == "b") return b_qty;
            }

            if (!string.IsNullOrWhiteSpace(m_unit_factor))
            {
                m_qty = (int)(absLeftQty / CPubVars.ToDecimal(m_unit_factor));
                absLeftQty = absLeftQty % CPubVars.ToDecimal(m_unit_factor);
                if (m_qty < 0.001m) m_qty = 0;

                if (m_qty > 0)
                {
                    m_qty *= flag;
                }
                if (unit_type == "m") return m_qty;

            }

            s_qty = absLeftQty;
            if (s_qty < 0.001m) s_qty = 0;

            if (unit_type == "s") return s_qty;
            return 0;
            // quantity_unit_conv = unitsQty;
            // return unitsQty;
        }

        private string SqlToExecute = "";

        protected void AddExecSQL(string addSql)
        {
            addSql = addSql.Trim();
            if (!addSql.EndsWith(";")) throw new Exception("sql should end with ;");
            SqlToExecute += addSql;
        }
        protected string GetExecSQL()
        {
            string sql = SqlToExecute;
            SqlToExecute = "";
            return sql;
        }
        protected void ClearExecSQL()
        {
            SqlToExecute = "";
        }

        public static string MoneyToUpper(string LowerMoney, bool numberOnly = false)
        {
            string functionReturnValue = null;
            bool IsNegative = false; // 是否是负数
            if (LowerMoney.Trim().Substring(0, 1) == "-")
            {
                // 是负数则先转为正数
                LowerMoney = LowerMoney.Trim().Remove(0, 1);
                IsNegative = true;
            }
            string strLower = null;
            string strUpart = null;
            string strUpper = null;
            int iTemp = 0;
            // 保留两位小数 123.489→123.49　　123.4→123.4
            LowerMoney = Math.Round(double.Parse(LowerMoney), 2).ToString();
            if (LowerMoney.IndexOf(".") > 0)
            {
                if (LowerMoney.IndexOf(".") == LowerMoney.Length - 2)
                {
                    LowerMoney = LowerMoney + "0";
                }
            }
            else
            {
                LowerMoney = LowerMoney + ".00";
            }
            strLower = LowerMoney;
            iTemp = 1;
            strUpper = "";
            while (iTemp <= strLower.Length)
            {
                switch (strLower.Substring(strLower.Length - iTemp, 1))
                {
                    case ".":
                        strUpart = "圆";
                        break;
                    case "0":
                        strUpart = "零";
                        break;
                    case "1":
                        strUpart = "壹";
                        break;
                    case "2":
                        strUpart = "贰";
                        break;
                    case "3":
                        strUpart = "叁";
                        break;
                    case "4":
                        strUpart = "肆";
                        break;
                    case "5":
                        strUpart = "伍";
                        break;
                    case "6":
                        strUpart = "陆";
                        break;
                    case "7":
                        strUpart = "柒";
                        break;
                    case "8":
                        strUpart = "捌";
                        break;
                    case "9":
                        strUpart = "玖";
                        break;
                }

                if (!numberOnly)
                {
                    switch (iTemp)
                    {
                        case 1:
                            strUpart = strUpart + "分";
                            break;
                        case 2:
                            strUpart = strUpart + "角";
                            break;
                        case 3:
                            strUpart = strUpart + "";
                            break;
                        case 4:
                            strUpart = strUpart + "";
                            break;
                        case 5:
                            strUpart = strUpart + "拾";
                            break;
                        case 6:
                            strUpart = strUpart + "佰";
                            break;
                        case 7:
                            strUpart = strUpart + "仟";
                            break;
                        case 8:
                            strUpart = strUpart + "万";
                            break;
                        case 9:
                            strUpart = strUpart + "拾";
                            break;
                        case 10:
                            strUpart = strUpart + "佰";
                            break;
                        case 11:
                            strUpart = strUpart + "仟";
                            break;
                        case 12:
                            strUpart = strUpart + "亿";
                            break;
                        case 13:
                            strUpart = strUpart + "拾";
                            break;
                        case 14:
                            strUpart = strUpart + "佰";
                            break;
                        case 15:
                            strUpart = strUpart + "仟";
                            break;
                        case 16:
                            strUpart = strUpart + "万";
                            break;
                        default:
                            strUpart = strUpart + "";
                            break;
                    }
                }
                strUpper = strUpart + strUpper;
                iTemp = iTemp + 1;
            }


            if (!numberOnly)
            {
                strUpper = strUpper.Replace("零拾", "零");
                strUpper = strUpper.Replace("零佰", "零");
                strUpper = strUpper.Replace("零仟", "零");
                strUpper = strUpper.Replace("零零零", "零");
                strUpper = strUpper.Replace("零零", "零");
                strUpper = strUpper.Replace("零角零分", "整");
                strUpper = strUpper.Replace("零分", "整");
                strUpper = strUpper.Replace("零角", "零");
                strUpper = strUpper.Replace("零亿零万零圆", "亿圆");
                strUpper = strUpper.Replace("亿零万零圆", "亿圆");
                strUpper = strUpper.Replace("零亿零万", "亿");
                strUpper = strUpper.Replace("零万零圆", "万圆");
                strUpper = strUpper.Replace("零亿", "亿");
                strUpper = strUpper.Replace("零万", "万");
                strUpper = strUpper.Replace("零圆", "圆");
                strUpper = strUpper.Replace("零零", "零");
            }



            // 对壹圆以下的金额的处理
            if (strUpper.Substring(0, 1) == "圆")
            {
                strUpper = strUpper.Substring(1, strUpper.Length - 1);
            }
            if (strUpper.Substring(0, 1) == "零")
            {
                strUpper = strUpper.Substring(1, strUpper.Length - 1);
            }
            if (strUpper.Length > 0)
            {
                if (strUpper.Substring(0, 1) == "角")
                {
                    strUpper = strUpper.Substring(1, strUpper.Length - 1);
                }
                if (strUpper.Substring(0, 1) == "分")
                {
                    strUpper = strUpper.Substring(1, strUpper.Length - 1);
                }
                if (strUpper.Substring(0, 1) == "整")
                {
                    strUpper = "零圆整";
                }
            }

            if (numberOnly)
            {
                strUpper = strUpper.Replace("圆", "").Replace("整", "");
            }
            functionReturnValue = strUpper;

            if (IsNegative == true)
            {
                return "负" + functionReturnValue;
            }
            else
            {
                return functionReturnValue;
            }

        }


    }
    public class SheetSum : SheetBase<SheetRowItem>
    {
        public SheetSum() : base("", "", LOAD_PURPOSE.SHOW)
        {

        }
    }
    public class A
    {
        //抽象属性不能有实现代码
        //public abstract string AbstractProperty { get; set; }

        string s;
        //虚拟属性可以有实现代码
        [SaveToDB][FromFld] public virtual string VritualProperty { get; set; }

    }

    public class B : A
    {
        string message;
        //在继承类（子类）中必须提供抽象属性的实现
        /*public override string AbstractProperty
        {
            get { return message; }
            set { this.message = value; }
        }*/

        //重写属性可以调用基类中的实现
        [SaveToDB] public override string VritualProperty { get; set; }

    }

    public class FromFld : System.Attribute
    {

        public FromFld(string fld, LOAD_PURPOSE purpose)
        {
            Field = fld;
            Purpose = purpose;
        }
        public FromFld(string fld)
        {
            Field = fld;
        }
        public FromFld(bool load)
        {
            this.Load = load;
        }
        public FromFld(LOAD_PURPOSE purpose)
        {
            Field = "";
            Purpose = purpose;
        }
        public FromFld()
        {
            Field = "";
        }
        public string Field { get; } = null;
        public bool Load = true;
        public bool ForLeftJoin { get; } = false;
        public LOAD_PURPOSE Purpose { get; } = LOAD_PURPOSE.SHOW_OR_APPROVE;
    }
}
