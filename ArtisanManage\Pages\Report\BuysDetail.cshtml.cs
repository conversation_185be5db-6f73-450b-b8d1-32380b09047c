﻿using ArtisanManage.Models;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ArtisanManage.MyJXC;
using ArtisanManage.Services;

namespace ArtisanManage.Pages.BaseInfo
{
    public class BuysDetailModel : PageQueryModel
    { 
        public BuysDetailModel(CMySbCommand cmd) : base(Services.MenuId.buysDetail)
        {
            this.cmd = cmd;
            this.PageTitle = "采购明细表";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time",   CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"item_id",new DataItem(){Title="商品名称",FldArea="divHead", LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="sd.item_id",DropDownWidth="300",
                  SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode  }},
                {"other_class",new DataItem(){Title="类别",FldArea="divHead",Checkboxes=true, LabelFld="class_name",CtrlType="jqxDropDownTree",TreePathFld="other_class",MumSelectable=true,CompareOperator="like",
                   SqlForOptions=CommonTool.selectClasses} },
                {"supcust_id",new DataItem(){FldArea="divHead",Title="供应商",LabelFld="sup_name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.supcust_id",
                    SqlForOptions=CommonTool.selectSuppliers}},
              {"branch_id",new DataItem(){FldArea="divHead",Title="仓库",LabelFld="branch_name",ButtonUsage="list",CompareOperator="=",SqlFld="(case when sd.branch_id is not null then sd.branch_id else sm.branch_id end)",
                SqlForOptions=CommonTool.selectBranch }},
                {"depart_path",new DataItem(){Title="部门",FldArea="divHead",LabelFld="depart_path_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", TreePathFld="depart_path",CompareOperator="like",LikeWrapper="/",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                {"sheet_no",new DataItem(){Title="单号编号",FldArea="divHead",  SqlFld="sm.sheet_no",CompareOperator="like"}},
                {"sheet_type",new DataItem(){FldArea="divHead",Title="单据类型",LabelFld="sheet_type_name",ButtonUsage="list",Source = "[{v:'CG',l:'采购单'},{v:'CT',l:'采购退货单'},{v:'',l:'所有'}]",CompareOperator="="}},
                {"remark",new DataItem(){Title="商品备注",FldArea="divHead",SqlFld="sd.remark",CompareOperator="like"
                }},
                 {"item_no",new DataItem(){Title="编号",FldArea="divHead",Hidden=true, CompareOperator="like"}},
                {"produce_date",new DataItem(){Title="生产日期",FldArea="divHead",Hidden=true, CompareOperator="="}},
                {"item_spec",new DataItem(){Title="规格",FldArea="divHead",Hidden=true, CompareOperator="like"}},

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates = true, Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",new DataItem(){SqlFld="sm.sheet_id",Hidden=true ,HideOnLoad = true}},
                       {"sheet_no",     new DataItem(){Title="单据编号",    Width="10%",SqlFld="sm.sheet_no" ,Linkable = true}},
                       {"make_brief",     new DataItem(){Title="单据备注",    Width="10%",SqlFld="concat(sm.make_brief,case when is_imported = true then '+导入单据' else '' end)"}},
                       {"sheet_type",   new DataItem(){Title="单据类型", Sortable=true,    Width="10%",SqlFld="(case WHEN sm.sheet_type='CG' THEN '采购单' ELSE '采购退货单' END)"}},
                       {"sup_name",     new DataItem(){Title="供应商",       Width="10%",SqlFld="sc.sup_name"}},
                       {"oper_name",    new DataItem(){Title="业务员",     Width="10%",SqlFld="io.oper_name"}},
                       {"recieves_name",    new DataItem(){Title="收货员",     Width="10%",SqlFld="sm.receivers_name"}},
                       {"branch_name",     new DataItem(){Title="仓库",       Width="10%"}},
                       {"happen_time",    new DataItem(){Title="交易时间",     Width="10%",SqlFld="sm.happen_time",Sortable=true}},
                       {"approve_time",    new DataItem(){Title="审核时间",     Width="10%",SqlFld="sm.approve_time",Sortable=true}},
                       {"item_name",    new DataItem(){Title="商品名称",   Width="10%",SqlFld="ip.item_name"}},
                       {"s_barcode",    new DataItem(){Title="条码(小)",  Width="100",Hidden=true, Sortable=false}},
                       {"b_barcode",    new DataItem(){Title="条码(大)",  Width="100",Hidden=true, Sortable=false}},
                       {"m_barcode",    new DataItem(){Title="条码(中)",  Width="100",Hidden=true, Sortable=false}},
                       {"quantity",     new DataItem() {Title="数量",  Width="5%",Hidden=false,
                           SqlFld="sd.quantity",CellsAlign="right",ShowSum=true,

                       }},
                       {"unit_no",      new DataItem(){Title="单位",       Width="50",SqlFld="sd.unit_no",Hidden=false}},
                       {"quantity_unit",     new DataItem() {Title="数量(单位)",  Width="80",
                           SqlFld="concat(round(sd.inout_flag*sd.quantity::numeric,2),sd.unit_no)",CellsAlign="right",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               string sQty ="";
                               if(sumColumnValues["quantity_b"]!="") sQty+= sumColumnValues["quantity_b"]+"大";
                               if(sumColumnValues["quantity_m"]!="") sQty+= sumColumnValues["quantity_m"]+"中";
                               if(sumColumnValues["quantity_s"]!="") sQty+= sumColumnValues["quantity_s"]+"小";
                               return sQty;
                           }

                       }},
                       {"s_quantity_total", new DataItem(){Title="总数量(小单位)",CellsAlign="right",Width="5%",Hidden=false,ShowSum=true,
                           SqlFld="(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric",
                        }},
                       {"s_unit_no", new DataItem(){Title="小单位",CellsAlign="rignt",Width="5%",Hidden=false,SqlFld="s_unit_no",
                        }},


                      {"quantity_b",   new DataItem(){Title="大数", Hidden=true,HideOnLoad = true,ShowSum=true, CellsAlign="right", Width="70",
                          SqlFld="case when itu.unit_type ='b' then sd.inout_flag*sd.quantity else null end"
                      }},
                      {"quantity_m",   new DataItem(){Title="中数",Hidden=true,HideOnLoad = true, ShowSum=true, CellsAlign="right", Width="70",
                          SqlFld="case when itu.unit_type ='m' then sd.inout_flag*sd.quantity else null end"
                      }},
                      {"quantity_s",   new DataItem(){Title="小数", Hidden=true,HideOnLoad = true,ShowSum=true, CellsAlign="right", Width="70",
                          SqlFld="case when itu.unit_type ='s' then sd.inout_flag*sd.quantity else null end"
                      }},
                      
                      {"real_price",   new DataItem(){Title="单价", CellsAlign="right", Width="80",SqlFld="round(sd.real_price::numeric,2)"}},
                      //{"real_price",   new DataItem(){Title="单价", CellsAlign="right", Width="10%",SqlFld="concat(round(sd.real_price::numeric,2),'/',sd.unit_no)"}},
                       {"total_amount",   new DataItem(){Title="金额", CellsAlign="right", Width="80",SqlFld="sub_amount*sd.inout_flag",ShowSum=true}},
                       {"remark", new DataItem(){Title="商品备注",    Width="100",SqlFld="sd.remark" }},
                       {"item_no",new DataItem(){Title="商品编号", Hidden=true, Width="100",}},
                       {"produce_date",new DataItem(){Title="生产日期",Hidden=true, Width="100",}},
                       {"item_spec",new DataItem(){Title="规格",Hidden=true, Width="70",}},
                       {"unit_conv",new DataItem(){Title="单位换算",SqlFld=@"(
         case when b_unit_factor is not null and m_unit_factor is     null then concat(s_unit_factor,b_unit_no,'=',b_unit_factor,s_unit_no)  
			  when b_unit_factor is not null and m_unit_factor is not null then concat(s_unit_factor,b_unit_no,'=',round(b_unit_factor::numeric/m_unit_factor::numeric,2),m_unit_no,'=',b_unit_factor,s_unit_no)
			  when b_unit_factor is null then concat(s_unit_factor,s_unit_no)  end
        )",Width="200"}},
                       //{"orig_price",   new DataItem(){Title="原价", CellsAlign="right", Width="10%",SqlFld="sd.orig_price",ShowSum=true}},
                       //{"cost_price",   new DataItem(){Title="成本价", CellsAlign="right", Width="10%",SqlFld="sd.cost_price",ShowSum=true}},
                     },
                     QueryFromSQL=@"
from sheet_buy_detail sd
left join 
(
    select item_id,     (s->>'f1')::numeric as s_unit_factor,s->>'f2' as s_unit_no,s->>'f3' as s_barcode,s ->> 'f4' as s_retail_price,s ->> 'f5'as s_wholesale_price,
                        (b->>'f1')::numeric as b_unit_factor,b->>'f2' as b_unit_no,b->>'f3' as b_barcode,
                        (m->>'f1')::numeric as m_unit_factor,m->>'f2' as m_unit_no,m->>'f3' as m_barcode
    from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no,barcode,weight,volume,retail_price,wholesale_price)) as json from info_item_multi_unit where company_id= ~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) 
    as errr(item_id int, s jsonb,m jsonb,b jsonb) 

) t
on sd.item_id=t.item_id  
LEFT JOIN info_item_multi_unit itu on itu.unit_no = sd.unit_no and itu.company_id = ~COMPANY_ID and itu.item_id = sd.item_id
LEFT JOIN sheet_buy_main sm on sd.sheet_id = sm.sheet_id and sm.company_id= ~COMPANY_ID
LEFT JOIN info_supcust sc ON sm.supcust_id = sc.supcust_id and sc.company_id= ~COMPANY_ID
LEFT JOIN info_branch ib ON COALESCE(sd.branch_id,sm.branch_id) = ib.branch_id and ib.company_id= ~COMPANY_ID
LEFT JOIN info_operator io on io.oper_id = sm.seller_id and io.company_id= ~COMPANY_ID
LEFT JOIN info_item_prop ip on ip.item_id = sd.item_id and ip.company_id= ~COMPANY_ID
LEFT JOIN info_item_batch itb on itb.batch_id = sd.batch_id and itb.company_id = ~COMPANY_ID
where sd.company_id= ~COMPANY_ID and sm.approve_time is not null and sm.red_flag is null",
                     QueryOrderSQL=" order by sd.happen_time desc"
                  }
                } 
            }; 
        }
        public async Task OnGet()
        {  
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }
            if (!seeInPrice)
            {
                var columns = await Grids["gridItems"].GetAllColumns();
                columns["real_price"].HideOnLoad = columns["real_price"].Hidden = true;
                columns["total_amount"].HideOnLoad = columns["total_amount"].Hidden = true;
            }

        }
    }



    [Route("api/[controller]/[action]")]
    public class BuysDetailController : QueryController
    { 
        public BuysDetailController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            BuysDetailModel model = new BuysDetailModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            BuysDetailModel model = new BuysDetailModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }



        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            BuysDetailModel model = new BuysDetailModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}
