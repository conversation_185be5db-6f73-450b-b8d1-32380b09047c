using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
	public class SalesSummaryByItemModel : PageQueryModel
    {

       
        public static Dictionary<string, DataItem> GetProfitColumns(Dictionary<string,DataItem> origColumns, bool hasDiscColumns,string sqlAreaToPlace="")
        {
        
            Dictionary<string, DataItem > cols= new Dictionary<string, DataItem>() {
                { "cost_amount_hasfree_cl_jh",  new DataItem() {ProfitRelated=true, Title = "成本(含赠|陈列|借还)",Hidden=true,  CellsAlign = "right", Width = "220", Sortable = true, SqlFld = "Will be changed by condition", ShowSum = true,SqlAreaToPlace=sqlAreaToPlace, FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},
                { "cost_amount_free_cl",  new DataItem() {ProfitRelated=true, Title = "成本(含赠|陈列)",Hidden=false,SqlAreaToPlace=sqlAreaToPlace,  CellsAlign = "right", Width = "200", Sortable = true, SqlFld = "Will be changed by condition", ShowSum = true, FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},
                { "cost_amount_hasfree",  new DataItem() {ProfitRelated=true, Title = "成本(含赠)",Hidden=true,SqlAreaToPlace=sqlAreaToPlace,  CellsAlign = "right", Width = "150", Sortable = true, SqlFld = "Will be changed by condition", ShowSum = true, FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},
                { "profit_hasfree_cl_jh", new DataItem() {ProfitRelated=true,Title = "利润(含赠|陈列|借还)", Hidden=true, CellsAlign = "right", Width = "220", Sortable = true, ShowSum = true, FuncDealMe = (value) => { return string.IsNullOrEmpty(value) ? "" : Math.Round(CPubVars.ToDecimal(value),2).ToString(); },SqlAreaToPlace=sqlAreaToPlace }},
                {"profit_rate_hasfree_cl_jh",new DataItem()
                {
					ProfitRelated=true,
                    Title = "利润率(%)(含赠|陈列|借还)",Hidden=true,
                    Sortable = true,
                    CellsAlign = "right",
                    Width = "200",
                    ShowAvg = true,SqlAreaToPlace=sqlAreaToPlace,
                    FuncDealMe = (value) => { return value == "0" ? "" : value; },
                    RelyColumns="profit_hasfree_cl_jh,net_amount",
                    FuncGetSumValue = (sumColumnValues) =>
                    {
                        string s_profit_hasfree_cl_jh = sumColumnValues["profit_hasfree_cl_jh"];
                        string s_net_amount = sumColumnValues["net_amount"];

                        double profit_hasfree_cl_jh = s_profit_hasfree_cl_jh != "" ? Convert.ToDouble(s_profit_hasfree_cl_jh) : 0.0;
                        double net_amount = s_net_amount != "" ? Convert.ToDouble(s_net_amount) : 0.0;
                        string rate = "";
                        if (net_amount != 0)
                        {
                            rate = CPubVars.FormatMoney(profit_hasfree_cl_jh / net_amount * 100, 1);
                        }
                        return rate;
                    }
                }},
           
                { "profit_hasfree", new DataItem() {ProfitRelated=true, Title = "利润(含赠)", Hidden=true,SqlAreaToPlace=sqlAreaToPlace, CellsAlign = "right", Width = "120", Sortable = true, ShowSum = true, FuncDealMe = (value) => { return string.IsNullOrEmpty(value) ? "" : Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},
                {"profit_rate_hasfree",new DataItem()   
                {   ProfitRelated=true,
                    Title = "利润率(%)(含赠)",Hidden=true,SqlAreaToPlace=sqlAreaToPlace,
                    Sortable = true,
                    CellsAlign = "right",
                    Width = "180",
                    ShowAvg = true,
                    FuncDealMe = (value) => { return value == "0" ? "" : value; },
                    RelyColumns="profit_hasfree,net_amount",
                    FuncGetSumValue = (sumColumnValues) =>
                    {
                        string s_profit_hasfree = sumColumnValues["profit_hasfree"];
                        string s_net_amount = sumColumnValues["net_amount"];

                        double profit_hasfree = s_profit_hasfree != "" ? Convert.ToDouble(s_profit_hasfree) : 0.0;
                        double net_amount = s_net_amount != "" ? Convert.ToDouble(s_net_amount) : 0.0;
                        string rate = "";
                        if (net_amount != 0)
                        {
                            rate = CPubVars.FormatMoney(profit_hasfree / net_amount * 100, 1);
                        }
                        return rate;
                    }
                }},
           
                { "profit_free_cl",       new DataItem() {
					ProfitRelated=true, Title = "利润(含赠|陈列)",Hidden=false,SqlAreaToPlace=sqlAreaToPlace,  CellsAlign = "right", Width = "180", Sortable = true,
                    ShowSum = true, FuncDealMe=(value)=>{
                        return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString();
                    } }
                },
                { "profit_rate_free_cl",new DataItem()
                {
					ProfitRelated=true,
                    Title = "利润率(%)(含赠|陈列)",
                    Hidden=false,SqlAreaToPlace=sqlAreaToPlace,
                    Sortable = true,
                    CellsAlign = "right",
                    Width = "210",
                    ShowAvg = true,
                    FuncDealMe = (value) => { return value == "0" ? "" : value; },
                    RelyColumns="profit_free_cl,net_amount",
                    FuncGetSumValue = (sumColumnValues) =>
                    {
                        string s_profit_hasfree = sumColumnValues["profit_free_cl"];
                        string s_net_amount = sumColumnValues["net_amount"];

                        double profit_hasfree = s_profit_hasfree != "" ? Convert.ToDouble(s_profit_hasfree) : 0.0;
                        double net_amount = s_net_amount != "" ? Convert.ToDouble(s_net_amount) : 0.0;
                        string rate = "";
                        if (net_amount != 0)
                        {
                            rate = CPubVars.FormatMoney(profit_hasfree / net_amount * 100, 1);
                        }
                        return rate;
                    }
                }},

                { "free_cost_amount",new DataItem() {ProfitRelated=true, Title = "赠品成本", Hidden=true,SqlAreaToPlace=sqlAreaToPlace, CellsAlign = "right", Width = "100", SqlFld = "", ShowSum = true, FuncDealMe = (value) => { return value == "0" ? "" : value; } } },
                { "cl_cost_amount",new DataItem() {ProfitRelated=true, Title = "陈列成本",Hidden=true,SqlAreaToPlace=sqlAreaToPlace, CellsAlign = "right", Width = "100", SqlFld = "", ShowSum = true, FuncDealMe = (value) => { return value == "0" ? "" : value; } } },
                { "cost_amount",  new DataItem() {ProfitRelated=true, Title = "成本(不含赠)",SqlAreaToPlace=sqlAreaToPlace,  CellsAlign = "right", Width = "150", Sortable = true, SqlFld = "Will be changed by condition", ShowSum = true,FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},
                { "profit",       new DataItem() {ProfitRelated=true, Title = "利润(不含赠)",SqlAreaToPlace=sqlAreaToPlace,  CellsAlign = "right", Width = "150", Sortable = true, ShowSum = true, FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},
 
               // { "rebate_amount", new DataItem() {Title = "补差额",SqlAreaToPlace=sqlAreaToPlace,  CellsAlign = "right", Width = "100", Hidden = true, Sortable = true,  ShowSum = true, FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},
               // { "rebate_amount_from_rbs", new DataItem() {Title = "补差额（补差单）",SqlAreaToPlace=sqlAreaToPlace,  CellsAlign = "right", Width = "100", Hidden = true, Sortable = true,  ShowSum = true, FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},
               // { "rebate_amount_from_rbs", new DataItem() { Title = "补差额（补差单）",SqlAreaToPlace=sqlAreaToPlace,  CellsAlign = "right", Width = "100", Hidden = true, Sortable = true,  ShowSum = true, FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},


                { "return_cost_amount",  new DataItem() {ProfitRelated=true, Title = "退货成本",SqlAreaToPlace=sqlAreaToPlace,  CellsAlign = "right", Width = "100", Sortable = true, SqlFld = "Will be changed by condition", ShowSum = true,Hidden=true}},
				{
				"profit_rate",new DataItem()
                {
					ProfitRelated=true,
                    Title = "利润率(%)(不含赠)",
                    Sortable = true,
                    CellsAlign = "right",
                    Width = "180",
                    ShowAvg = true,SqlAreaToPlace=sqlAreaToPlace,
                    RelyColumns="profit,net_amount",
                    FuncGetSumValue = (sumColumnValues) =>
                    {
                        string s_profit_hasfree = sumColumnValues["profit"];
                        string s_net_amount = sumColumnValues["net_amount"];

                        double profit_hasfree = s_profit_hasfree != "" ? Convert.ToDouble(s_profit_hasfree) : 0.0;
                        double net_amount = s_net_amount != "" ? Convert.ToDouble(s_net_amount) : 0.0;
                        string rate = "";
                        if (net_amount != 0)
                        {
                            rate = CPubVars.FormatMoney(profit_hasfree / net_amount * 100, 1);
                        }
                        return rate;
                    }
                }},
             };
			if (hasDiscColumns)
			{
                Dictionary<string, DataItem> cols1 = new Dictionary<string, DataItem>() {
                { "sheet_disc",  new DataItem() {Hidden=true, Title = "优惠金额", CellsAlign = "right",SqlAreaToPlace=sqlAreaToPlace, Width = "150", Sortable = true, ShowSum = true, FuncDealMe = (value) => { return string.IsNullOrEmpty(value)||value == "0" ? "" : Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},

                { "profit_has_free_cl_cut_disc",  new DataItem() {ProfitRelated=true,Hidden=true,Title = "利润(含赠|陈列,减优惠)", CellsAlign = "right",SqlAreaToPlace=sqlAreaToPlace, Width = "150", Sortable = true, ShowSum = true, FuncDealMe = (value) => { return value == "0" ? "" : value; } }},

                    
                { "profit_cut_disc",       new DataItem() {ProfitRelated=true,Title = "利润(不含赠,减优惠)",Hidden=true,  CellsAlign = "right",SqlAreaToPlace=sqlAreaToPlace, Width = "150", Sortable = true, ShowSum = true, FuncDealMe = (value) => { return value == "0" ? "" : value; } }},
                {
                    "profit_rate_cut_disc",new DataItem()
                    {
						ProfitRelated=true,
                        Hidden=true,
                        Title = "利润率(不含赠,减优惠)(%)",
                        Sortable = true,
                        CellsAlign = "right",
                        Width = "180",
                        ShowAvg = true,SqlAreaToPlace=sqlAreaToPlace,
                        RelyColumns="profit,net_amount",
                        FuncGetSumValue = (sumColumnValues) =>
                        {
                            string s_profit_hasfree = sumColumnValues["profit"];
                            string s_net_amount = sumColumnValues["net_amount"];

                            double profit_hasfree = s_profit_hasfree != "" ? Convert.ToDouble(s_profit_hasfree) : 0.0;
                            double net_amount = s_net_amount != "" ? Convert.ToDouble(s_net_amount) : 0.0;
                            string rate = "";
                            if (net_amount != 0)
                            {
                                rate = CPubVars.FormatMoney(profit_hasfree / net_amount * 100, 1);
                            }
                            return rate;
                        }
                    }
                 }
                };

                foreach(var kp in cols1)
				{
                    cols.Add(kp.Key, kp.Value);
				}
			}
            return cols;
        }
        public SalesSummaryByItemModel(CMySbCommand cmd) : base(Services.MenuId.salesSummaryByItem)
        {
            this.UsePostMethod = true;
            this.cmd = cmd;
            this.PageTitle = "销售汇总(商品)";
            this.NotQueryHideColumn = true;
            this.EnableBigDataMode = true;
            CanQueryByApproveTime = true;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sd.happen_time+sm.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sd.happen_time+sm.happen_time", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
              
            
                {"brand_id", CommonTool.GetDataItem("brand_id", new DataItemChange(){SqlFld="ip.item_brand"})},
				{"other_class",new DataItem(){Title="类别",FldArea="divHead",MaxRecords="1000", LabelFld="class_name",Checkboxes=true, CtrlType="jqxDropDownTree",TreePathFld="other_class",MumSelectable=true,CompareOperator="like",
                   SqlForOptions=CommonTool.selectClasses} },
                /*{"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",SqlFld="sd.item_id",DropDownWidth="300",
                   QueryByLabelLikeIfIdEmpty=true, SqlForOptions ="select item_id as v,item_name as l,py_str as z from info_item_prop" }},*/
                {"item_id",new DataItem(){Title="商品",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="sd.item_id",DropDownWidth="300",Pinned=true,
              SearchFields=CommonTool.itemSearchFields,
                SqlForOptions =CommonTool.selectItemWithBarcode   }},
                {"seller_id",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="seller_name",Checkboxes=true,ButtonUsage="list",CompareOperator="=",SqlFld="sm.seller_id",SqlForOptions=CommonTool.selectSellers } },
                    //SqlForOptions ="select oper_id as v,oper_name as l from info_operator"}},

                {"branch_id",new DataItem(){Title="仓库",FldArea="divHead",LabelFld="branch_name",Checkboxes=true,ButtonUsage="list",CompareOperator="=",SqlFld="sm.branch_id",
                SqlForOptions=CommonTool.selectBranch }},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客    户",LabelFld="sup_name",ButtonUsage="event",Checkboxes=true,QueryByLabelLikeIfIdEmpty=true, CompareOperator="=",SqlFld="sm.supcust_id",
                    SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag like '%C%' and company_id=~COMPANY_ID "}},
                {"group_id",new DataItem(){Title="渠道",FldArea="divHead", Checkboxes=true,LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_group",
                    SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},
                {"other_region",new DataItem(){FldArea="divHead",Title="片区",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index "
                }},
                //{"sup_rank",new DataItem(){Title="等级",FldArea="divHead",LabelFld="rank_name",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",CompareOperator="=",
                {"sup_rank",new DataItem(){Title="等级",FldArea="divHead",Checkboxes=true,LabelFld="rank_name",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",CompareOperator="=",
                    SqlForOptions="select rank_id as v,rank_name as l from info_supcust_rank"
                }},

                {"department_id",new DataItem(){Title="部门",TreePathFld="department_path",Hidden=true, FldArea="divHead",LabelFld="department_id_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=false,DropDownWidth="150", CompareOperator="=",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                {"depart_path",new DataItem(){Title="业务部门",Hidden=true, FldArea="divHead",LabelFld="depart_path_label",TreePathFld="path", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", CompareOperator="like",LikeWrapper="/",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                
                {"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'3',l:'预设进价'},{v:'2',l:'加权平均价'},{v:'1',l:'预设成本'},{v:'4',l:'最近平均进价'}]", CompareOperator="=" }},
                 /*{"trade_type_after_group",new DataItem(){FldArea="divHead",Title="销退赠",ForQuery=true,LabelFld="sale_ways",ButtonUsage="list",Value="all",Label="所有", AfterGroup=true,Hidden=true,
                   // Source = "[{v:'sale',l:'销售',condition:\"sum(case when sd.sub_amount<>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)<>'0'\"},{v:'return',l:'退货',condition:\"sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)<>'0'\"},{v:'free',l:'赠品',condition:\"sum(case when sd.sub_amount=0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)<>'0'\"},{v:'all',l:'所有',condition:'1=1'}]",
                    Source = "[{v:'sale',l:'销售',condition:\"sum(case when sd.sub_amount<>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)<>'0'\"},{v:'return',l:'退货',condition:\"sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)<>'0'\"},{v:'free',l:'赠品',condition:\"sum(case when sd.sub_amount=0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)<>'0'\"},{v:'all',l:'所有',condition:'1=1'}]",

                  CompareOperator="=",Checkboxes=true } },
                 */
                 {"queryTimeAccord",new DataItem(){
					FldArea="divHead", Title="时间类型", LabelFld = "time_status_name", ButtonUsage = "list",
					CompareOperator="=",Value="byHappenTime",Label="交易时间",ForQuery=false, AutoRemember=true,
					Source = @"[
                                {v:'byHappenTime',l:'交易时间'},
                                {v:'byMakeTime',l:'制单时间'},
                                {v:'byApproveTime',l:'审核时间'},
                                {v:'byCheckedTime',l:'交账时间'},
                                {v:'bySendTime',l:'送货时间'},
                                {v:'byClearArrearsTime',l:'欠款结清时间'}
                               ]"
				}},

				{
                    "senders_id",
                    new DataItem()
                    {
                        FldArea = "divHead", Title = "送货员",SqlFld = "senders_id", ButtonUsage = "list",
                        Checkboxes=true,
                        DealQueryItem = status => ""+status+"",
                        SqlForOptions=CommonTool.selectSenders,  //SqlForOptions = "select oper_id as v,oper_name as l,py_str as z from info_operator",
                        CompareOperator = "like"
                    }
                },
                {"make_brief",new DataItem(){Title="整单备注",FldArea="divHead",CompareOperator="ilike" } },
                {"remark",new DataItem(){Title="明细备注",SqlFld="sd.remark", FldArea="divHead",CompareOperator="ilike",Hidden=true } },//后面可以删掉
                {"remarks",new DataItem(){Title="行备注",SqlFld="sd.remark",Checkboxes=true, FldArea="divHead",Hidden=false, ButtonUsage = "list",TextAsValue=true,SqlForOptions="select brief_id as v,brief_text as l from info_sheet_detail_brief", CompareOperator="ilike" } },
                {"exclude_remarks",new DataItem(){Title="排除行备注",SqlFld="sd.remark",Checkboxes=true, FldArea="divHead",Hidden=true, ButtonUsage = "list",SqlForOptions="select brief_text as v,brief_text as l from info_sheet_detail_brief", CompareOperator="not ilike"} },
                {"sale_way",new DataItem(){FldArea="divHead",   Title="销售方式",Checkboxes=true, ButtonUsage = "list" ,Hidden=true, CompareOperator="=",Value="",Label="",
                        Source = @"[{v:'all',l:'所有',condition:""1=1""},
                                   {v:'directSale',l:'车销',condition:""sm.order_sheet_id is null""},
                                   {v:'byOrder',l:'访销',condition:""sm.order_sheet_id is not null""}]"

                }},
                {"status",new DataItem(){FldArea="divHead",Checkboxes=true,   Title="单据状态", ButtonUsage = "list",CompareOperator="=",Value="approved",Label="已审核",
                        Source = @"[{v:'normal',l:'所有',condition:""sm.red_flag is null""},
                                   {v:'unapproved',l:'未审核',condition:""sm.approve_time is null""},
                                   {v:'approved',l:'已审核',condition:""sm.approve_time is not null and sm.red_flag is null""}]"

                }},
                {"order_source",new DataItem(){FldArea="divHead",Title="来源",QueryOnChange=true,LabelFld = "order_source_name",ButtonUsage = "list",CompareOperator="=",
                    Source = @"[{v:'offline',l:'线下',condition:""sm.order_source is null""},
                                 {v:'online',l:'小程序',condition:""(sm.order_source =  'xcx')""},
                                 {v:'all',l:'所有',condition:""true""}]"

                }},
                 {"trade_type",new DataItem(){FldArea="divHead",Checkboxes=true, Title="交易类型",ButtonUsage = "list",CompareOperator="=",Value="",Label="",
                        Source = @"[{v:'ALL',l:'所有',condition:""true""},
                                    {v:'NJH',l:'非借还货',condition:""coalesce(trade_type,'X') not in ('J','H')""},
                                    {v:'X',l:'销售',condition:""coalesce(trade_type,'X')='X' and sd.quantity*sd.inout_flag<0""},
                                   {v:'T',l:'退货',condition:""coalesce(trade_type,'X') in ('T','TD','X') and sd.quantity*sd.inout_flag>0""},
                                   {v:'XT',l:'销退',condition:""coalesce(trade_type,'X') in ('X','T','XD','TD')""},
                                   {v:'DH',l:'定货还货',condition:""trade_type='DH'""},
                                   {v:'JH',l:'借还货',condition:""trade_type in ('J','H')""},
                                   {v:'CL',l:'陈列兑付',condition:""trade_type ='CL'""},
                                   {v:'HH',l:'换货',condition:""trade_type in ('HR','HC')""}]"
				}},
                 {"arrears_status",new DataItem(){FldArea="divHead",Title="欠款情况",Checkboxes=true, ButtonUsage = "list",CompareOperator="=",
                    Source = @"[{v:'cleared',l:'已结清',condition:""abs(total_amount-paid_amount-disc_amount)<0.1""},
                                 {v:'uncleared',l:'未结清',condition:""abs(total_amount-paid_amount-disc_amount)>0.1""},
                                 {v:'all',l:'所有',condition:""true""}]"
                }},
                {"showRebateProfit",new DataItem(){FldArea="divHead",Title="显示补差后利润",CtrlType="jqxCheckBox",Hidden=true,ForQuery=false,Value="false"}},
                {"sheetType",new DataItem(){Title="",FldArea="divHead",Hidden=true,ForQuery=false,HideOnLoad = true} }
            };
            //销量解释  销量:价格>0的销售数量
            string x_quantity =      "     sum(case when sd.quantity*sd.inout_flag < 0 and sd.real_price>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric";
			 
			//销量解释  退货量: 包含退的赠品
			string t_quantity =      "     sum(case when sd.quantity*sd.inout_flag > 0 and sd.real_price>0 then sd.quantity*sd.unit_factor*sd.inout_flag      else 0 end)::numeric";

			//销量解释  净销量: 价格>0 的销售数量 - 价格>0的 退货数量
			string net_quantity =    "     sum(case when sd.quantity*sd.inout_flag <>0 and sd.real_price>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric";
			//string net_quantity = "     sum(case when sd.quantity*sd.inout_flag<>0  then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric";


			// string t_quantity = "  sum(case when sd.quantity*sd.inout_flag> 0 and sd.real_price> 0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric";
			//价格为0的也算退货
 
		

			// string net_quantity_has_give = "sum(case when sd.quantity*sd.inout_flag<>0 and (sd.real_price>0 or (coalesce(trade_type,'X') in ('X','XD','T','TD','HR','HC') and not coalesce(sd.remark,'') like '%陈列%')) then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric";



			string x_quantity_rounded = $"round({x_quantity},4)";
            string t_quantity_rounded = $"round({t_quantity},4)";

            string net_quantity_rounded = $"round({net_quantity},4)";

            string x_quantity_has_give_cl = "  sum(case when sd.quantity*sd.inout_flag< 0 and (sd.real_price>0 or (coalesce(trade_type,'X') in ('X','T','XD','TD','HC','HR','DH','CL') or coalesce(sd.remark,'') like '%陈列%')) then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric";
            string t_quantity_has_give_cl = "  sum(case when sd.quantity*sd.inout_flag> 0 and (sd.real_price>0 or (coalesce(trade_type,'X') in ('X','T','XD','TD','HC','HR','DH','CL') or coalesce(sd.remark,'') like '%陈列%')) then sd.quantity*sd.unit_factor*sd.inout_flag      else 0 end)::numeric";
            string net_quantity_has_give_cl = "sum(case when sd.quantity*sd.inout_flag<>0 and (sd.real_price>0 or (coalesce(trade_type,'X') in ('X','T','XD','TD','HC','HR','DH','CL') or coalesce(sd.remark,'') like '%陈列%')) then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric";
            
            string x_quantity_has_give_h = "   sum(case when sd.quantity*sd.inout_flag< 0 and (sd.real_price>0 or coalesce(trade_type,'X') in ('X','T','XD','TD','HC','HR','DH','H'))                                            then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric";
            string t_quantity_has_give_j = "   sum(case when sd.quantity*sd.inout_flag> 0 and (sd.real_price>0 or coalesce(trade_type,'X') in ('X','T','XD','TD','HC','HR','DH','J'))                                            then sd.quantity*sd.unit_factor*sd.inout_flag      else 0 end)::numeric";
            string net_quantity_has_give_jh = "sum(case when sd.quantity*sd.inout_flag<>0 and (sd.real_price>0 or coalesce(trade_type,'X') in ('X','T','XD','TD','HC','HR','DH','J','H'))                                        then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric";

            string x_quantity_has_give_cl_h = "   sum(case when sd.quantity*sd.inout_flag< 0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric";
            string t_quantity_has_give_cl_j = "   sum(case when sd.quantity*sd.inout_flag> 0 then sd.quantity*sd.unit_factor*sd.inout_flag      else 0 end)::numeric";
            string net_quantity_has_give_cl_jh = "sum(case when sd.quantity*sd.inout_flag<>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric";

			string z_quantity = "sum(case when sd.quantity*sd.inout_flag<>0 and sd.real_price=0 and coalesce(trade_type,'X') in ('X','XD','HC','DH','T','TD')  and not coalesce(sd.remark,'') like '%陈列%'  then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric";
            string z_quantity_has_cl = "sum(case when sd.quantity*sd.inout_flag<0 and sd.sub_amount=0 and coalesce(trade_type,'X') in ('X','XD','HC','DH','CL')  then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric";
            string z_quantity_has_j = " sum(case when sd.quantity*sd.inout_flag<0 and sd.sub_amount=0 and coalesce(trade_type,'X') in ('X','XD','HC','DH','J')   then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric";
            string cl_quantity = "sum(case when sd.sub_amount=0 and (trade_type='CL' or sd.remark like '%陈列%')  then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric";


            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Sortable=true,

                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"item_id",    new DataItem(){Title="商品ID",  Width="150",SqlFld="sd.item_id",Hidden=true,HideOnLoad=true,IsIDColumn=true}},
                       {"item_name",    new DataItem(){Title="商品名称",  Width="200",SqlFld="ip.item_name",Linkable=true,Sortable=true,IsChinese=true,Pinned=true}},
					   {"other_class",    new DataItem(){Title="other_class",Hidden=true,  Width="200"}},
                     
                       //  {"produce_date",    new DataItem(){Title="生产日期",  Width="150"}},
                      //  {"batch_no",    new DataItem(){Title="批次",  Width="100"}},
                       {"item_spec", new DataItem(){Title="规格", Width="150",SqlFld="ip.item_spec", Hidden=true, Sortable=true,IsChinese=true}},
                       {"brand_name",    new DataItem(){Title="品牌",  Width="10", Hidden=true, Sortable=true,IsChinese=true}},
                       {"s_barcode",    new DataItem(){Title="条码(小)",  Width="100",Hidden=true, Sortable=false}},
                       {"b_barcode",    new DataItem(){Title="条码(大)",  Width="100",Hidden=true, Sortable=false}},
                       {"m_barcode",    new DataItem(){Title="条码(中)",  Width="100",Hidden=true, Sortable=false}},
                       {"classes_name",    new DataItem(){Title="类别", SqlFld=@"ic1.class_name || '/' || ic.class_name  ", Width="100",Hidden=true, Sortable=false}},

                       {"b_unit_no",   new DataItem(){Title="大单位名称", Width="200",SqlFld="b_unit_no",Hidden=true,HideOnLoad = true}},
                       {"s_unit_no",   new DataItem(){Title="小单位", Width="200",SqlFld="s_unit_no",Hidden=true,HideOnLoad = true,CellsAlign="right"}},
                       {"unit_conv",new DataItem(){Title="单位换算",ButtonUsage="list", CellsAlign="center",Width="200",Hidden=true,
                           SqlFld = @"yj_get_unit_relation(b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},
                         /*{"x_quantity",   new DataItem(){Title="总销量", CellsAlign="center", Sortable=true,   Width="8%",
                                      SqlFld="unit_from_s_to_bms ((sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                                      SortFld="sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric/b_unit_factor", 
                                      FuncDealMe=(value)=>{return value=="0"?"":value; },
                                       FuncGetSumValue = (sumColumnValues) =>
                                      {
                                          string sQty = "";
                                          if(sumColumnValues["x_quantity_b"]!="")  sQty+= sumColumnValues["x_quantity_b"]+"大";
                                          if(sumColumnValues["x_quantity_m"]!="")  sQty+= sumColumnValues["x_quantity_m"]+"中";
                                          if(sumColumnValues["x_quantity_s"]!="")  sQty+= sumColumnValues["x_quantity_s"]+"小";
                                          return sQty;
                                      }
                                  }},
                                  {"x_quantity_b",   new DataItem(){Title="总销量(大)", CellsAlign="center",   Width="8%",ShowSum=true,Hidden=true,
                                       SqlFld="yj_get_unit_qty('b',sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                                       FuncDealMe=(value)=>{return value=="0"?"":value; },
                                  }},
                                  {"x_quantity_m",   new DataItem(){Title="总销量(中)", CellsAlign="center",   Width="8%",ShowSum=true,Hidden=true,
                                       SqlFld="yj_get_unit_qty('m',sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                                       FuncDealMe=(value)=>{return value=="0"?"":value; },
                                  }},
                                  {"x_quantity_s",   new DataItem(){Title="总销量(小)", CellsAlign="center",   Width="8%",ShowSum=true,Hidden=true,
                                       SqlFld="yj_get_unit_qty('s',sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                                       FuncDealMe=(value)=>{return value=="0"?"":value; },
                                  }},*/
                       { "rebate_quantity",   new DataItem(){Title="补差数量",Sortable=true,  CellsAlign="right",  Width="100",SqlFld="round(sum(case when coalesce(rebate_price, 0)<>0 then quantity*sd.unit_factor else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       { "rebate_amount", new DataItem() {Title = "补差额", SqlFld="SUM(coalesce(rebate_price,0) * quantity*sd.unit_factor)", CellsAlign = "right", Width = "100", Hidden = true, Sortable = true,  ShowSum = true, FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},
                       {"rebate_quantity_rbs",   new DataItem(){Title="补差数量（补差单）",Sortable=true,  CellsAlign="right",  Width="100",SqlFld="round(sum(rebate_qty_rbs*sd.unit_factor)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"rebate_amount_rbs", new DataItem() {SqlFld="sum(rebate_price_rbs*rebate_qty_rbs)", Title = "补差额（补差单）", CellsAlign = "right", Width = "100", Hidden = true, Sortable = true,  ShowSum = true, FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},
                
#region 销量
                       {"x_quantity_num",   new DataItem(){Title="销售量(小)", Width="120",Hidden=true,Sortable=true, ShowSum = true,SqlFld=$"round({x_quantity},2)", Tooltip="价格>0的销售数量（小单位数)"}},
					   {"x_quantity_big",   new DataItem(){Title="销售量(大)", Width="120",Hidden=true,Sortable=true, ShowSum = true,SqlFld=$"round(({x_quantity}/b_unit_factor)::numeric,2)", Tooltip="价格>0的销售数量（大单位数)"}},
					   {"x_quantity",   new DataItem(){Title="销量", CellsAlign="center", Sortable=true,   Width="50",
                           Tooltip="价格>0的销售数量",
                           SqlFld = $"unit_from_s_to_bms ({x_quantity_rounded},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           SortFld="x_quantity_num",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           RelyColumns="x_quantity_b,x_quantity_m,x_quantity_s",
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               return CPubVars.GetBMSQty(sumColumnValues["x_quantity_b"],sumColumnValues["x_quantity_m"],sumColumnValues["x_quantity_s"]);

                           }
                       }},
                       {"x_quantity_b",   new DataItem(){Title="销量(b)",  CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('b',{x_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_quantity_m",   new DataItem(){Title="销量(m)",  CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('m',{x_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_quantity_s",   new DataItem(){Title="销量(s)",  CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('s',{x_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},

#endregion
#region 退货量 

                       {"t_quantity_num",   new DataItem(){Title="退货量(小)", Hidden=true,  ShowSum = true, Width="120",SqlFld = t_quantity_rounded,Tooltip="价格>0的退货数量(小单位数)",Sortable=true}},
					   {"t_quantity_big",   new DataItem(){Title="退货量(大)", Width="120",Hidden=true,ShowSum = true,SqlFld=$"round(({t_quantity}/b_unit_factor)::numeric,2)", Tooltip="价格>0的退货数量（大单位数)",Sortable=true}},
					   {"t_quantity",   new DataItem(){Title="退货量", CellsAlign="center",Hidden=true,   Width="80", Sortable=true,
						  Tooltip="价格>0的退货数量",
						   SortFld="t_quantity_num",
                           SqlFld=$"unit_from_s_to_bms ({t_quantity_rounded},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) ",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                            RelyColumns="t_quantity_b,t_quantity_m,t_quantity_s",
                            FuncGetSumValue = (sumColumnValues) =>
                            {
                                 return CPubVars.GetBMSQty(sumColumnValues["t_quantity_b"],sumColumnValues["t_quantity_m"],sumColumnValues["t_quantity_s"]);
                            }
                       }},
                        {"t_quantity_b",   new DataItem(){Title="退货量(b)",  CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('b',{t_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                        {"t_quantity_m",   new DataItem(){Title="退货量(m)", CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('m',{t_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                        {"t_quantity_s",   new DataItem(){Title="退货量(s)",  CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('s',{t_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
#endregion

#region 净销量
                       {"net_quantity_s_unit", new DataItem(){Title="净销量(小)", Hidden=true, CellsAlign="center", Sortable=true,  Width="65",ShowSum=true,
                           SqlFld=$"{net_quantity_rounded}",
                        
							Tooltip="销售量(小) - 退货量(小)",
							FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},

                       {"net_quantity_b_unit", new DataItem(){Title="净销量(大)", Hidden=true, CellsAlign="center", Sortable=true,  Width="65",ShowSum=true, SqlFld=$"{net_quantity_rounded}/b_unit_factor",
						  Tooltip="销售量(大) - 退货量(大)",
							FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"net_quantity", new DataItem(){Title="净销量",  CellsAlign="center", Sortable=true,  Width="80",SqlFld=$"unit_from_s_to_bms({net_quantity_rounded},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           Tooltip="销售量 - 退货量", 
							SortFld=$"net_quantity_s_unit",
						   FuncDealMe=(value)=>{return value=="0"?"":value; },
                           RelyColumns="net_quantity_b,net_quantity_m,net_quantity_s",
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                                 return CPubVars.GetBMSQty(sumColumnValues["net_quantity_b"],sumColumnValues["net_quantity_m"],sumColumnValues["net_quantity_s"]);

                           }
                       }},
                        {"net_quantity_b", new DataItem(){Title="净销量(b)", CellsAlign="center",  Width="65",
                            SqlFld=$"yj_get_unit_qty ('b',{net_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,

                       }},

                        {"net_quantity_m", new DataItem(){Title="净销量(m)",  CellsAlign="center",  Width="65",
                            SqlFld=$"yj_get_unit_qty ('m',{net_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,
                       }},
                        {"net_quantity_s", new DataItem(){Title="净销量(s)",  CellsAlign="center",  Width="65",
                            SqlFld=$"yj_get_unit_qty ('s',{net_quantity_rounded},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,
                       }},
#endregion                
              
                   {"return_rate", new DataItem(){Title="退货率(%)",  CellsAlign="center",ShowSum=true, Sortable=true,  Width="80", GetFromDb=true ,
                   SqlFld=$@"case when round({x_quantity}::numeric,2)<>0 then 
                            (
                                round({t_quantity}::numeric,2) 
                                /
                                round({x_quantity}::numeric,2)
                            ) end",
               
                            FuncDealMe=(value)=>{return (value=="0"||value=="")?"":CPubVars.FormatMoney(Convert.ToDouble(value)*100,1); },
                           /* RelyColumns="x_quantity_num,t_quantity_num",
                            FuncGetValueFromRowData=(colName,row)=>
                            {
                               string x_quantity_num = row["x_quantity_num"];
                               string t_quantity_num = row["t_quantity_num"];
                               string returnRate="";
                               if (x_quantity_num != "" && t_quantity_num!=""){
                                   decimal n=CPubVars.ToDecimal(x_quantity_num);
                                   if (n != 0)
                                   {
                                       decimal d=CPubVars.ToDecimal(t_quantity_num)/n*100;
                                       returnRate=CPubVars.FormatMoney(d,1)+"%";
                                   }
                               }
                               return returnRate;
                            },*/

                            FuncGetSumValue = (sumColumnValues) =>
                            {
                                 string s_x_quantity_num = sumColumnValues["x_quantity_num"];
                                 string s_t_quantity_num = sumColumnValues["t_quantity_num"];

                                 double x_quantity_num = s_x_quantity_num != "" ? Convert.ToDouble(s_x_quantity_num) : 0.0;
                                 double t_quantity_num = s_t_quantity_num != "" ? Convert.ToDouble(s_t_quantity_num) : 0.0;
                                 string rate = "";
                                 if (x_quantity_num != 0)
                                 {
                                    rate = CPubVars.FormatMoney(t_quantity_num / x_quantity_num * 100, 1)+"%";
                                 }
                                 return rate;
                            }
                        }},
#region 销量(含赠|陈列)
                        {"x_quantity_has_give_cl",   new DataItem(){Title="销量(含赠|陈列)",Hidden=true, CellsAlign="center", Sortable=true,   Width="180",
                            SortFld="x_quantity_has_give_cl_num", 
                          Tooltip="销量+赠品量+陈列量",
                           SqlFld=$"unit_from_s_to_bms({x_quantity_has_give_cl},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           RelyColumns="x_quantity_b_has_give_cl,x_quantity_m_has_give_cl,x_quantity_s_has_give_cl",
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               return CPubVars.GetBMSQty(sumColumnValues["x_quantity_b_has_give_cl"],sumColumnValues["x_quantity_m_has_give_cl"],sumColumnValues["x_quantity_s_has_give_cl"]);

                           }
                       }},
					   {"x_quantity_has_give_cl_num",   new DataItem(){Title="销量(含赠|陈列)(小)", Hidden=true,  ShowSum = true, Width="65",
							Tooltip="销量(小) + 赠品量(小) + 陈列量(小)",
						   SqlFld=$"round({x_quantity_has_give_cl},2)"}},
					   {"x_quantity_b_has_give_cl",   new DataItem(){Title="销量(b)(含赠|陈列)",  CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('b',{x_quantity_has_give_cl},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_quantity_m_has_give_cl",   new DataItem(){Title="销量(m)(含赠|陈列)",  CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('m',{x_quantity_has_give_cl},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_quantity_s_has_give_cl",   new DataItem(){Title="销量(s)(含赠|陈列)",  CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('s',{x_quantity_has_give_cl},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       
#endregion

#region 退货量(含赠|陈列)

                     
                       {"t_quantity_has_give_cl",   new DataItem(){Title="退货量(含赠|陈列)", Hidden=false,  CellsAlign="center",   Width="65", Sortable=true,
                           SortFld="t_quantity_has_give_cl_num",
						    Tooltip="退货量 + 退赠品量 + 退陈列量",
						   SqlFld=$"unit_from_s_to_bms ({t_quantity_has_give_cl},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) ",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                            RelyColumns="t_quantity_has_give_cl_b,t_quantity_has_give_cl_m,t_quantity_has_give_cl_s",
                            FuncGetSumValue = (sumColumnValues) => {
                                 return CPubVars.GetBMSQty(
                                     sumColumnValues["t_quantity_has_give_cl_b"],
                                     sumColumnValues["t_quantity_has_give_cl_m"],
                                     sumColumnValues["t_quantity_has_give_cl_s"]
                                 );
                            }
                       }},
						{"t_quantity_has_give_cl_num",   new DataItem(){Title="退货量(含赠|陈列)(小)", Hidden=true,  ShowSum = true, Width="65",
							Tooltip="退货量(小) + 退赠品量(小) + 退陈列量(小)",
						   SqlFld=$"round({t_quantity_has_give_cl},2)"}},

						{"t_quantity_has_give_cl_b",   new DataItem(){Title="退货量(b)(含赠|陈列)",   CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('b',{t_quantity_has_give_cl},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                        {"t_quantity_has_give_cl_m",   new DataItem(){Title="退货量(m)(含赠|陈列)",  CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('m',{t_quantity_has_give_cl},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                        {"t_quantity_has_give_cl_s",   new DataItem(){Title="退货量(s)(含赠|陈列)",   CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('s',{t_quantity_has_give_cl},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
#endregion

                   #region 净销量（含赠|陈列)
                    {"net_quantity_hasfree_cl", new DataItem(){Title="净销量（含赠|陈列)", Hidden=true, CellsAlign="center",  Width="210",
					      Tooltip="销量(含赠|陈列) - 退货量(含赠|陈列)",
						  SqlFld=$"unit_from_s_to_bms({net_quantity_has_give_cl},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                            RelyColumns="net_quantity_b_hasfree,net_quantity_m_hasfree,net_quantity_s_hasfree",
                            FuncGetSumValue = (sumColumnValues) =>
                            {
                                return CPubVars.GetBMSQty(sumColumnValues["net_quantity_b_hasfree"],sumColumnValues["net_quantity_m_hasfree"],sumColumnValues["net_quantity_s_hasfree"]);

                            }
                   }},
                   {"net_quantity_hasfree_cl_b_unit", new DataItem(){Title="净销量（含赠|陈列)(大)",  CellsAlign="center",  Width="70",
					Tooltip="销量(含赠|陈列)(大) - 退货量(含赠|陈列)(大)",
					   SqlFld=$"({net_quantity_has_give_cl} / b_unit_factor)::numeric",
                                FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum = true, Hidden=true
                   }},
                   {"net_quantity_hasfree_cl_s_unit", new DataItem(){Title="净销量（含赠|陈列)(小)",  CellsAlign="center",  Width="70",
				   Tooltip="销量(含赠|陈列)(小) - 退货量(含赠|陈列)(小)",
					   SqlFld=$"{net_quantity_has_give_cl}",
                       FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum = true
                   }},
                   /*{"net_quantity_hasfree_s_unit", new DataItem(){Title="净销量（含赠）(小)",  CellsAlign="center",  Width="70",
                       SqlFld=$"{net_quantity_has_give_cl}",
                       FuncDealMe=(value)=>{return value=="0"?"":value; },
                   }},*/
                   {"net_quantity_b_hasfree", new DataItem(){Title="净销量（含赠|陈列）(b)", CellsAlign="center",  Width="70",
                       SqlFld=$"yj_get_unit_qty('b',{net_quantity_has_give_cl},b_unit_factor,m_unit_factor,false)",
                       FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,

                   }},
                   {"net_quantity_m_hasfree", new DataItem(){Title="净销量（含赠|陈列）(m)", CellsAlign="center",  Width="70",
                       SqlFld=$"yj_get_unit_qty('m',{net_quantity_has_give_cl},b_unit_factor,m_unit_factor,false)",
                       FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,
                   }},
                   {"net_quantity_s_hasfree", new DataItem(){Title="净销量（含赠|陈列）(s)", CellsAlign="center",  Width="70",
                       SqlFld=$"yj_get_unit_qty('s',{net_quantity_has_give_cl},b_unit_factor,m_unit_factor,false)",
                       FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,
                   }},
#endregion
               
                   
#region 销量(含赠|还)
                        {"x_quantity_has_give_h",   new DataItem(){Title="销量(含赠|还)",Hidden=true, CellsAlign="center", Sortable=true,   Width="120",
                            SortFld="x_quantity_has_give_h_num",
						   Tooltip="销量+赠品量+还货量",
						   SqlFld=$"unit_from_s_to_bms ({x_quantity_has_give_h},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           RelyColumns="x_quantity_has_give_h_b,x_quantity_has_give_h_m,x_quantity_has_give_h_s",
                            FuncGetSumValue = (sumColumnValues) =>
                           {
                               return CPubVars.GetBMSQty(sumColumnValues["x_quantity_has_give_h_b"],sumColumnValues["x_quantity_has_give_h_m"],sumColumnValues["x_quantity_has_give_h_s"]);
  
                           }
                       }},
						 {"x_quantity_has_give_h_num",   new DataItem(){Title="销量(含赠|还)(小)", Hidden=true,  ShowSum = true, Width="65",
							Tooltip="销量(小)+赠品量(小)+还货量(小)",
						   SqlFld=$"round({x_quantity_has_give_h},2)"}},


					   {"x_quantity_has_give_h_b",   new DataItem(){Title="销量(b)(含赠|还)", CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('b',{x_quantity_has_give_h},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_quantity_has_give_h_m",   new DataItem(){Title="销量(m)(含赠|还)", CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('m',{x_quantity_has_give_h},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_quantity_has_give_h_s",   new DataItem(){Title="销量(s)(含赠|还)", CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('s',{x_quantity_has_give_h},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       
#endregion
     
#region 退货量 (含赠|借)

                   
                    {"t_quantity_has_give_j",   new DataItem(){Title="退货量(含赠|借)",Hidden=true, CellsAlign="center",   Width="70", Sortable=true, 
                        SortFld="t_quantity_has_give_j_num",
						Tooltip="退货量+退赠品量+借货量",

						   SqlFld=$"unit_from_s_to_bms ({t_quantity_has_give_j},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) ",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                            RelyColumns="t_quantity_has_give_j_b,t_quantity_has_give_j_m,t_quantity_has_give_j_s",
                            FuncGetSumValue = (sumColumnValues) =>
                           {
                               return CPubVars.GetBMSQty(sumColumnValues["t_quantity_has_give_j_b"],sumColumnValues["t_quantity_has_give_j_m"],sumColumnValues["t_quantity_has_give_j_s"]);

                           }
                     }},

					 {"t_quantity_has_give_j_num", new DataItem(){Title="退货量(含赠|借)(小)", Hidden=true,  ShowSum = true, Width="70",
						Tooltip="退货量(小)+退赠品量(小)+借货量(小)",
						SqlFld=$"round({t_quantity_has_give_j},2)"}},

					 {"t_quantity_has_give_j_b",   new DataItem(){Title="退货量(b)(含赠|借)", CellsAlign="center",   Width="70",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('b',{t_quantity_has_give_j},b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                     }},
                     {"t_quantity_has_give_j_m",   new DataItem(){Title="退货量(m)(含赠|借)", CellsAlign="center",   Width="70",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('m',{t_quantity_has_give_j},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                     }},
                     {"t_quantity_has_give_j_s",   new DataItem(){Title="退货量(s)(含赠|借)",  CellsAlign="center",   Width="70",ShowSum=true,Hidden=true,HideOnLoad = true,
                          SqlFld=$"yj_get_unit_qty('s',{t_quantity_has_give_j},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                     }},
#endregion

#region 净销量（含赠|借还)
                     {"net_quantity_has_give_jh", new DataItem(){Title="净销量（含赠|借还）",  Hidden=true,  CellsAlign="center",  Width="210",
					     Tooltip="销量(含赠|还) - 退货量(含赠|借)", 
						 SqlFld=$"unit_from_s_to_bms({net_quantity_has_give_jh},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                            RelyColumns="net_quantity_has_give_jh_b,net_quantity_has_give_jh_m,net_quantity_has_give_jh_s",
                            FuncGetSumValue = (sumColumnValues) =>
                            {
                                return CPubVars.GetBMSQty(sumColumnValues["net_quantity_has_give_jh_b"], sumColumnValues["net_quantity_has_give_jh_m"], sumColumnValues["net_quantity_has_give_jh_s"]);

                            }
                       }},
                       {"net_quantity_has_give_jh_num", new DataItem(){Title="净销量（含赠|借还）(小)", Hidden=true,  CellsAlign="center",  Width="70",
						   Tooltip="销量(含赠|还)(小) - 退货量(含赠|借)(小)",
						   SqlFld=$"{net_quantity_has_give_jh}",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                       {"net_quantity_has_give_jh_b", new DataItem(){Title="净销量（含赠|借还）(b)",  CellsAlign="center",  Width="70",
                           SqlFld=$"yj_get_unit_qty ('b',{net_quantity_has_give_jh},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,

                       }},
                        {"net_quantity_has_give_jh_m", new DataItem(){Title="净销量（含赠|借还）(m)",  CellsAlign="center",  Width="70",
                            SqlFld=$"yj_get_unit_qty ('m',{net_quantity_has_give_jh},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,
                       }},
                        {"net_quantity_has_give_jh_s", new DataItem(){Title="净销量（含赠|借还）(s)",  CellsAlign="center",  Width="70",
                             SqlFld=$"yj_get_unit_qty ('s',{net_quantity_has_give_jh},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,
                       }},
                       
#endregion


 #region 销量(含赠|陈列|还)
                        {"x_quantity_has_give_cl_h",   new DataItem(){Title="销量(含赠|陈列|还)",Hidden=true, CellsAlign="center", Sortable=true,   Width="210",
                            SortFld="x_quantity_has_give_cl_j_num",
						Tooltip="销量 + 赠品量 + 陈列量 + 还货量",
						   SqlFld=$"unit_from_s_to_bms ({x_quantity_has_give_cl_h},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           RelyColumns="x_quantity_has_give_cl_h_b,x_quantity_has_give_cl_h_m,x_quantity_has_give_h_s",
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               return CPubVars.GetBMSQty(sumColumnValues["x_quantity_has_give_cl_h_b"],sumColumnValues["x_quantity_has_give_cl_h_m"],sumColumnValues["x_quantity_has_give_h_s"]);
                           }
                       }},

					   {"x_quantity_has_give_cl_j_num", new DataItem(){Title="销量(含赠|陈列|还)(小)", Hidden=true,  ShowSum = true, Width="70",
						SqlFld=$"round({x_quantity_has_give_cl_h},2)"}},

					   {"x_quantity_has_give_cl_h_b",   new DataItem(){Title="销量(b)(含赠|陈列|还)", CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('b',{x_quantity_has_give_cl_h},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_quantity_has_give_cl_h_m",   new DataItem(){Title="销量(m)(含赠|陈列|还)", CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('m',{x_quantity_has_give_cl_h},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_quantity_has_give_cl_h_s",   new DataItem(){Title="销量(s)(含赠|陈列|还)", CellsAlign="center",   Width="65",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('s',{x_quantity_has_give_cl_h},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       
#endregion
     
#region 退货量 (含赠|陈列|借)

                  
                    {"t_quantity_has_give_cl_j",   new DataItem(){Title="退货量(含赠|陈列|借)",Hidden=true, CellsAlign="center",   Width="180", Sortable=true, 
                        SortFld="t_quantity_has_give_cl_j_num",
						Tooltip="退货量 + 退赠品量 + 退陈列量 + 借货量",
						   SqlFld=$"unit_from_s_to_bms ({t_quantity_has_give_cl_j},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) ",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                            RelyColumns="t_quantity_has_give_cl_j_b,t_quantity_has_give_cl_j_m,t_quantity_has_give_cl_j_s",
                            FuncGetSumValue = (sumColumnValues) =>
                           {
                               return CPubVars.GetBMSQty(sumColumnValues["t_quantity_has_give_cl_j_b"],sumColumnValues["t_quantity_has_give_cl_j_m"],sumColumnValues["t_quantity_has_give_cl_j_s"]);

                           }
                     }},

					  {"t_quantity_has_give_cl_j_num", new DataItem(){Title="退货量(含赠|借)(小)", Hidden=true,  ShowSum = true, Width="70",
						SqlFld=$"round({t_quantity_has_give_cl_j},2)"}},


					 {"t_quantity_has_give_cl_j_b",   new DataItem(){Title="退货量(b)(含赠|借)", CellsAlign="center",   Width="70",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('b',{t_quantity_has_give_cl_j},b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                     }},
                     {"t_quantity_has_give_cl_j_m",   new DataItem(){Title="退货量(m)(含赠|借)", CellsAlign="center",   Width="70",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('m',{t_quantity_has_give_cl_j},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                     }},
                     {"t_quantity_has_give_cl_j_s",   new DataItem(){Title="退货量(s)(含赠|借)",  CellsAlign="center",   Width="70",ShowSum=true,Hidden=true,HideOnLoad = true,
                          SqlFld=$"yj_get_unit_qty('s',{t_quantity_has_give_cl_j},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                     }},
#endregion

#region 净销量（含赠|陈列|借还)
                     {"net_quantity_has_give_cl_jh", new DataItem(){Title="净销量（含赠|陈列|借还）", Hidden=true,   CellsAlign="center",  Width="210",
					 Tooltip="销量(含赠|陈列|还)  -  退货量(含赠|陈列|借)",
						 SqlFld=$"unit_from_s_to_bms({net_quantity_has_give_cl_jh},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                            RelyColumns="net_quantity_has_give_cl_jh_b,net_quantity_has_give_cl_jh_m,net_quantity_has_give_cl_jh_s",
                            FuncGetSumValue = (sumColumnValues) =>
                            {
                                return CPubVars.GetBMSQty(sumColumnValues["net_quantity_has_give_cl_jh_b"], sumColumnValues["net_quantity_has_give_cl_jh_m"], sumColumnValues["net_quantity_has_give_cl_jh_s"]);
                            }
                       }},
                       {"net_quantity_has_give_cl_jh_num", new DataItem(){Title="净销量（含赠|陈列|借还）(小)", Hidden=true,  CellsAlign="center",  Width="70",
						Tooltip="销量(含赠|陈列|还)(小)  -  退货量(含赠|陈列|借)(小)",
						   SqlFld=$"{net_quantity_has_give_cl_jh}",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"net_quantity_has_give_cl_jh_b", new DataItem(){Title="净销量（含赠|陈列|借还）(b)",  CellsAlign="center",  Width="70",
                           SqlFld=$"yj_get_unit_qty ('b',{net_quantity_has_give_cl_jh},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,

                       }},
                        {"net_quantity_has_give_cl_jh_m", new DataItem(){Title="净销量（含赠|陈列|借还）(m)",  CellsAlign="center",  Width="70",
                            SqlFld=$"yj_get_unit_qty ('m',{net_quantity_has_give_cl_jh},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,
                       }},
                        {"net_quantity_has_give_cl_jh_s", new DataItem(){Title="净销量（含赠|陈列|借还）(s)",  CellsAlign="center",  Width="70",
                             SqlFld=$"yj_get_unit_qty ('s',{net_quantity_has_give_cl_jh},b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,
                       }},
                       
#endregion
 

                       #region 赠品量
                       {"z_quantity_s_unit", new DataItem(){Title="赠品量(小)", Width="70",Hidden=true,Sortable=true,
						   Tooltip = "赠品销量(小) - 退赠退量(小) ,不含陈列",
						   SqlFld=$"round({z_quantity},2)",
                         FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},

                       {"z_quantity",   new DataItem(){Title="赠品量", CellsAlign="center",Width="70",Sortable=true,
					       Tooltip="赠品销量 - 退赠退量, 不含陈列",
						   SqlFld=$"unit_from_s_to_bms({z_quantity},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           SortFld="z_quantity_s_unit",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           RelyColumns="z_quantity_b,z_quantity_m,z_quantity_s",
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                                 return CPubVars.GetBMSQty(sumColumnValues["z_quantity_b"],sumColumnValues["z_quantity_m"],sumColumnValues["z_quantity_s"]);
  
                           }
                       }},
                        {"z_quantity_b",   new DataItem(){Title="赠品量(b)",  CellsAlign="center",   Width="70",ShowSum=true, Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('b',{z_quantity},b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; } 
                       }},
                        {"z_quantity_m",   new DataItem(){Title="赠品量(m)",  CellsAlign="center",   Width="70",ShowSum=true, Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('m',{z_quantity},b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                         {"z_quantity_s",   new DataItem(){Title="赠品量(s)",   CellsAlign="center",   Width="70",ShowSum=true, Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('s',{z_quantity},b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},

                      
                         #endregion
                       #region 赠品量(含陈列)
                         {"z_quantity_has_cl_s_unit", new DataItem(){Title="赠品量(含陈列)(小)", Width="70",Hidden=true,
							Tooltip="赠品量(小) + 陈列量(小)",
							 SqlFld=$"round({z_quantity_has_cl},2)",
                         FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},

                       {"z_quantity_has_cl",   new DataItem(){Title="赠品量(含陈列)", Hidden=true,  CellsAlign="center",Width="180",Sortable=true,
					       Tooltip="赠品量 + 陈列量",
                           SortFld="z_quantity_has_cl_s_unit",
						   SqlFld=$"unit_from_s_to_bms ({z_quantity_has_cl},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           RelyColumns="z_quantity_has_cl_b,z_quantity_has_cl_m,z_quantity_has_cl_s",
                           FuncGetSumValue = (sumColumnValues) =>
                           { 
                               return CPubVars.GetBMSQty(sumColumnValues["z_quantity_has_cl_b"],sumColumnValues["z_quantity_has_cl_m"],sumColumnValues["z_quantity_has_cl_s"]);
  
                           }
                       }},
                        {"z_quantity_has_cl_b",   new DataItem(){Title="赠品量(含陈列)(b)",  CellsAlign="center",   Width="70",ShowSum=true, Hidden=true,HideOnLoad = true,
                           SqlFld = $"yj_get_unit_qty('b',{z_quantity_has_cl},b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                        {"z_quantity_has_cl_m",   new DataItem(){Title="赠品量(含陈列)(m)",  CellsAlign="center",   Width="70",ShowSum=true, Hidden=true,HideOnLoad = true,
                           SqlFld = $"yj_get_unit_qty('m',{z_quantity_has_cl},b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                         {"z_quantity_has_cl_s",   new DataItem(){Title="赠品量(含陈列)(s)",   CellsAlign="center",   Width="70",ShowSum=true, Hidden=true,HideOnLoad = true,
                           SqlFld = $"yj_get_unit_qty('s',{z_quantity_has_cl},b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},

                         #endregion

                         {"z_quantity_has_j",   new DataItem(){Title="赠品量(含还)",Hidden=true, CellsAlign="center",Width="150",Sortable=false,
						 Tooltip="赠品量 + 还货量",
						  
                           SqlFld=$"unit_from_s_to_bms ({z_quantity_has_j},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           RelyColumns="z_quantity_has_j_b,z_quantity_has_j_m,z_quantity_has_j_s",
                           FuncGetSumValue = (sumColumnValues) =>
                           { 
                                return CPubVars.GetBMSQty(sumColumnValues["z_quantity_has_j_b"],sumColumnValues["z_quantity_has_j_m"],sumColumnValues["z_quantity_has_j_s"]);
  
                           }
                       }},

						  
						{"z_quantity_has_j_b",   new DataItem(){Title="赠品量(b)(含还)",  CellsAlign="center",   Width="70",ShowSum=true, Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('b',{z_quantity_has_j},b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                        {"z_quantity_has_j_m",   new DataItem(){Title="赠品量(m)(含还)",  CellsAlign="center",   Width="70",ShowSum=true, Hidden=true,HideOnLoad = true,
                            SqlFld=$"yj_get_unit_qty('m',{z_quantity_has_j},b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                         {"z_quantity_has_j_s",   new DataItem(){Title="赠品量(s)(含还)",   CellsAlign="center",   Width="70",ShowSum=true, Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('s',{z_quantity_has_j},b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},

                       {"cl_quantity_b_unit",   new DataItem(){Title="陈列量(大)", Width="70",Hidden=true,SqlFld=$"round(({cl_quantity}/b_unit_factor)::numeric,2)", Tooltip="陈列数量(大单位数),包含走陈列协议还货的+备注为陈列的",}},

                       {"cl_quantity_s_unit",   new DataItem(){Title="陈列量(小)", Width="70",Hidden=true,SqlFld=$"round(({cl_quantity})::numeric,2)",Tooltip="陈列数量(小单位数),包含走陈列协议还货的+备注为陈列的"}},

                       {"cl_quantity",   new DataItem(){Title="陈列量", CellsAlign="center",Width="70",Sortable=true,
					       Tooltip="陈列数量,包含走陈列协议还货的+备注为陈列的",

						   SortFld="cl_quantity_s_unit",
                           SqlFld=$"unit_from_s_to_bms ({cl_quantity},b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           RelyColumns="cl_quantity_b,cl_quantity_m,cl_quantity_s",
                           FuncGetSumValue = (sumColumnValues) =>
                           { 
                               return CPubVars.GetBMSQty(sumColumnValues["cl_quantity_b"],sumColumnValues["cl_quantity_m"],sumColumnValues["cl_quantity_s"]);
  
                           }
                       }},

                        {"cl_quantity_b",   new DataItem(){Title="陈列量(m)", CellsAlign="center",   Width="70",ShowSum=true, Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('b',{cl_quantity},b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                        {"cl_quantity_m",   new DataItem(){Title="陈列量(b)", CellsAlign="center",   Width="70",ShowSum=true, Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('m',{cl_quantity},b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                         {"cl_quantity_s",   new DataItem(){Title="陈列量(s)", CellsAlign="center",   Width="70",ShowSum=true, Hidden=true,HideOnLoad = true,
                           SqlFld=$"yj_get_unit_qty('s',{cl_quantity},b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                            
                      
                       {"net_quantity_per_sender_b", new DataItem(){Title="净销量送货员人均(大单位数)",  CellsAlign="center",  Width="250",SqlFld="round(sum(sd.quantity*sd.unit_factor*sd.inout_flag*(-1)/array_length(string_to_array(sm.senders_id,','),1))::numeric/b_unit_factor::numeric,2)",
						  Tooltip="此处净销量包含赠品|陈列|借还货",
							FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       }},
                       {"x_amount",     new DataItem(){Title="销售金额", CellsAlign="right", Width="100", Sortable=true,SqlFld="sum(case when sd.quantity*sd.inout_flag<0 then sd.inout_flag*(-1)*sd.sub_amount else 0 end)",
                       ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); },}},
                       {"disc_amount",     new DataItem(){Title="价格优惠", CellsAlign="right", Width="100", Sortable=true,SqlFld="round(sum((orig_price-real_price)*sd.quantity*sd.inout_flag*(-1))::numeric,2)",ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; },}},

                       {"t_amount",     new DataItem(){Title="退货金额", CellsAlign="right", Width="100", Sortable=true,SqlFld="sum(case when sd.quantity*sd.inout_flag>0 then sd.sub_amount*sd.inout_flag else 0 end)",ShowSum=true,FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); },}},
                       {"net_amount",   new DataItem(){Title="销售净额", CellsAlign="right", Width="100", Sortable=true,SqlFld="sum(inout_flag*(-1)*sub_amount)",ShowSum=true,ShowRowPercent=true, FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},//先取原值再四舍五入，为了和经营利润表一致
                       {"net_rebate_amount",   new DataItem(){Title="销售净额(补差后)", CellsAlign="right", Width="150", Sortable=true,Hidden = true,SqlFld="sum(inout_flag*(-1)*sub_amount - coalesce(rebate_price, 0)*quantity*sd.unit_factor )",ShowSum=true,ShowRowPercent=true, FuncDealMe=(value)=>{return string.IsNullOrEmpty(value)?"":Math.Round(CPubVars.ToDecimal(value),2).ToString(); } }},
                         //{"disc_amount",  new DataItem(){Title="优惠", CellsAlign="right",    Width="10%",SqlFld="sum(money_inout_flag*disc_amount)",ShowSum=true}},
                      
                       {"weight", new DataItem(){Title="出货重量(kg)",  CellsAlign="center",  Width="100",SqlFld="round(sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.inout_flag*(-1)*mu2.weight else 0 end)::numeric,3)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       }},

                       {"ton", new DataItem(){Title="出货吨位",SqlFld="round(sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.inout_flag*(-1)*mu2.weight/1000 else 0 end)::numeric,3)",ShowSum=true, Hidden=true,CellsAlign="right", Width="200" } },

                       {"return_weight", new DataItem(){Title="退货重量(kg)",  CellsAlign="center",  Width="100",SqlFld="round(sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.inout_flag*mu2.weight else 0 end)::numeric,3)",
					        Tooltip="此处退货量包含退赠品|退陈列|借货",
							FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       }},
                        {"net_weight", new DataItem(){Title="净销重量(kg)",  CellsAlign="center",  Width="100",SqlFld="round(sum(sd.quantity*sd.inout_flag*(-1)*mu2.weight)::numeric,3)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       }},
                        {"volume", new DataItem(){Title="出货体积(m³)",  CellsAlign="center",  Width="100",SqlFld="round(sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.inout_flag*(-1)*mu2.volume else 0 end)::numeric,3)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       }},
                       
                       {"average_price",  new DataItem(){Title="均价", CellsAlign="right", Width="60", Sortable=true,SqlFld=" (case when  ( SUM ( sd.quantity * sd.unit_factor * sd.inout_flag * ( - 1 ) ) :: NUMERIC  <> 0  ) then  round((SUM ( inout_flag * ( - 1 ) * sub_amount ):: NUMERIC / SUM ( sd.quantity * sd.unit_factor * sd.inout_flag * ( - 1 ) ):: NUMERIC) , 2 )else 0 end  )",}},
 
                       {"cost_price",  new DataItem() { Title = "成本单价(不含赠品)", CellsAlign = "right", Width = "160",Hidden=true,  Sortable = true, SqlFld = "Will be changed by condition", ShowSum = false,BuyPriceRelated=true }},

                       {"valid_days",  new DataItem(){Title="保质期", Width="150",SqlFld="ip.valid_days", Hidden=true, Sortable=true,IsChinese=true}},
                       //  {"orig_price",     new DataItem(){Title="原价(小)", CellsAlign="right", Width="8%", Sortable=true,SqlFld="round(coalesce(recent_orig_price,s_wholesale_price::numeric)::numeric,2)",
                       //ShowSum=false,FuncDealMe=(value)=>{return value=="0"?"":value; },}},
                       //  {"recent_orig_price",     new DataItem(){Title="原价", CellsAlign="right", Width="8%", Sortable=true,SqlFld="round(coalesce(recent_orig_price,mu.wholesale_price)::numeric,2)",
                       //ShowSum=false,FuncDealMe=(value)=>{return value=="0"?"":value; },}},
                       //{"orig_amount",     new DataItem(){Title="原价金额", CellsAlign="right", Width="8%", Sortable=true,SqlFld="round(sum(case when sd.quantity*sd.inout_flag<0 then sd.inout_flag*(-1)*sd.sub_amount else 0 end)::numeric,2)",
                       //ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; },}},
                       //{"sys_price",     new DataItem(){Title="系统售价", CellsAlign="right", Width="8%", Sortable=true,SqlFld="round(coalesce(recent_orig_price,mu.wholesale_price)::numeric,2)",
                       //ShowSum=false,FuncDealMe=(value)=>{return value=="0"?"":value; },}},
                       // {"recent_price",     new DataItem(){Title="上次售价(小)", CellsAlign="right", Width="8%", Sortable=true,SqlFld="round(recent_price::numeric,2)",
                       //ShowSum=false,FuncDealMe=(value)=>{return value=="0"?"":value; },}},
                        {"s_retail_price",     new DataItem(){Title="小单位零售价", CellsAlign="right", Width="150", Sortable=true,SqlFld="round(s_retail_price::numeric,2)",
                       ShowSum=false,FuncDealMe=(value)=>{return value=="0"?"":value; },}},
                       // {"cost_price",     new DataItem(){Title="成本价", CellsAlign="right", Width="8%", Sortable=true,SqlFld="round(s_retail_price::numeric,2)",
                       //ShowSum=false,FuncDealMe=(value)=>{return value=="0"?"":value; },}},
                     




                     },
                     QueryFromSQL=$@"
FROM ~detailTable sd
LEFT JOIN ~mainTable sm on sd.sheet_id=sm.sheet_id and sd.company_id=sm.company_id ~OTHER_DETAIL_MAIN_JOIN_CONDI
LEFT JOIN 
(
    SELECT us.item_id,us.unit_no s_unit_no, us.barcode s_barcode,1 s_unit_factor,us.retail_price s_retail_price, us.wholesale_price s_wholesale_price,
                                 ub.unit_no b_unit_no, ub.barcode b_barcode,  ub.unit_factor b_unit_factor,
                                 um.unit_no m_unit_no, um.barcode m_barcode,  um.unit_factor m_unit_factor
               FROM      info_item_multi_unit us
               LEFT JOIN info_item_multi_unit ub on us.item_id=ub.item_id and ub.unit_type='b' and ub.company_id=~COMPANY_ID  
               LEFT JOIN info_item_multi_unit um on us.item_id=um.item_id and um.unit_type='m' and um.company_id=~COMPANY_ID  
               WHERE us.company_id=~COMPANY_ID  and us.unit_type='s'
) t ON sd.item_id=t.item_id  
LEFT JOIN info_item_prop ip on sd.item_id = ip.item_id and ip.company_id = ~COMPANY_ID
LEFT JOIN info_item_brand brand on ip.item_brand = brand.brand_id and brand.company_id = ~COMPANY_ID
LEFT JOIN info_operator io on sm.seller_id = io.oper_id and io.company_id = ~COMPANY_ID
LEFT JOIN info_branch ib on ib.branch_id = sm.branch_id and ib.company_id = ~COMPANY_ID
LEFT JOIN info_supcust s on s.supcust_id = sm.supcust_id and s.company_id = ~COMPANY_ID
LEFT JOIN info_item_multi_unit mu2 on  sd.item_id = mu2.item_id and sd.unit_factor = mu2.unit_factor  and sd.unit_no = mu2.unit_no  and mu2.company_id = ~COMPANY_ID 
LEFT JOIN info_item_class ic on ip.item_class = ic.class_id and ic.company_id = ~COMPANY_ID
LEFT JOIN info_item_class ic1 on ic.mother_id = ic1.class_id and ic1.company_id = ~COMPANY_ID
LEFT JOIN info_item_batch itb on itb.batch_id = COALESCE(sd.batch_id,0) and itb.company_id = ~COMPANY_ID
WHERE sd.company_id= ~COMPANY_ID and sd.inout_flag <>0  ~VAR_IS_DEL",
                     QueryGroupBySQL = " group by sd.item_id,ip.item_name,ip.item_spec,brand_name,ip.other_class, ic.class_name,ic1.class_name, b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no,s_barcode,m_barcode,b_barcode,valid_days,s_retail_price",
                     QueryOrderSQL=" order by other_class,item_name"
                  }
                }
            };
			/*
            left join 
(
    select prm.company_id company_id,prd.quantity rebate_quantity_from_rbs,prd.sheet_rebate_price sheet_rebate_price,prd.item_id,prm.happen_time,prm.seller_id seller_id
    from sheet_price_rebate_main prm
    left join sheet_price_rebate_detail prd on prm.company_id = prd.company_id and prm.sheet_id = prd.sheet_id
) rbs
on rbs.company_id = ~COMPANY_ID and rbs.item_id=sd.item_id and rbs.happen_time >='~VAR_startDay' and rbs.happen_time<'~VAR_endDay' ~VAR_SELLER_CONDI

             */
			var origCols = Grids.First().Value.Columns;
			var cols = GetProfitColumns(origCols,false);
           
            foreach (var k in cols)
            {
                origCols.Add(k.Key, k.Value);
            }

        }
        public static void SetProfitColumns(PageQueryModel page )
        {
            var costPrice = "sd.cost_price_buy";//当前进价
            var cost_price_type = page.DataItems["cost_price_type"].Value;
            switch (cost_price_type)
            {
                case "3"://预设进价
                    costPrice = "sd.cost_price_buy";
                    break;
                case "2"://加权价
                    costPrice = "sd.cost_price_avg";
                    break;
                case "1"://预设成本
                    costPrice = "sd.cost_price_prop";
                    break;
                case "4"://最近平均进价
                    costPrice = "sd.cost_price_recent";
                    break;
            }
            //Console.WriteLine("Item页面的SetProfitColumn方法获取的costPrice:" + costPrice);
            var columns = page.Grids.GetValueOrDefault("gridItems").Columns;
            if (columns.ContainsKey("cost_price"))
            {
                columns["cost_price"].SqlFld = $@"
          round( 
            round( SUM ( CASE WHEN sub_amount <> 0  THEN - inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END ) :: NUMERIC, 2 )
             /
            NULLIF(round( SUM ( CASE WHEN sub_amount <> 0  THEN  - inout_flag *quantity * sd.unit_factor  ELSE 0 END ) :: NUMERIC, 2 ),0)

            ,2)

            ";
           // columns["rebate_amount"].SqlFld = $"SUM(coalesce(rebate_price,0) * quantity*sd.unit_factor)";
           // columns["rebate_amount_from_rbs"].SqlFld = $"SUM(coalesce(sheet_rebate_price,0) * rebate_quantity_from_rbs*sd.unit_factor)";
            }

            //补差额
            //if (columns.ContainsKey("rebate_amount")) columns["rebate_amount"].SqlFld = $"SUM(coalesce(rebate_price,0) * quantity*sd.unit_factor)";
            //if (columns.ContainsKey("rebate_amount_from_rbs")) columns["rebate_amount_from_rbs"].SqlFld = $"SUM(coalesce(sheet_rebate_price,0) * rebate_quantity_from_rbs*sd.unit_factor)"; 
 

            var showRebateProfit = "0";  
            if (page.DataItems.ContainsKey("showRebateProfit") && page.DataItems["showRebateProfit"].Value.ToLower() == "true")
            {
                showRebateProfit = "SUM(coalesce(rebate_price,0) * quantity*sd.unit_factor*inout_flag*(-1))";
            }

            //赠品+陈列+借还（所有赠品）
            columns["cost_amount_hasfree_cl_jh"].SqlFld = $"SUM ( quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} ) ";
           // columns["profit_hasfree_cl_jh"].SqlFld = $@"sum(inout_flag*(-1)*sub_amount)-sum(case when row_index = 1 then inout_flag*(-1)*coalesce(disc_amount,0) else 0 end)-SUM ( quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} ) ";
            columns["profit_hasfree_cl_jh"].SqlFld = $@"sum(inout_flag*(-1)*sub_amount)-SUM ( quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} ) - {showRebateProfit}";

            columns["profit_rate_hasfree_cl_jh"].SqlFld = @$"
round ( (
    (
		(round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2) - round({showRebateProfit}::numeric,2) )*100
	) /
	(
		case when round(sum(inout_flag*(-1)*sub_amount)::numeric,2) - round({showRebateProfit}::numeric,2) <>0 
		     then round(sum(inout_flag*(-1)*sub_amount)::numeric,2) - round({showRebateProfit}::numeric,2) 
					else null 
		end) :: NUMERIC
	):: NUMERIC, 2)

";

            //赠品+陈列
            columns["cost_amount_free_cl"].SqlFld = $"SUM( (case when coalesce(trade_type,'X')  not in ('J','H') then  quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end) ) ";
            //columns["profit_free_cl"].SqlFld = $@"sum(inout_flag*(-1)*sub_amount)- sum(case when row_index = 1 then inout_flag*(-1)*coalesce(disc_amount,0) else 0 end) - SUM ( case when coalesce(trade_type,'X')  not in ('J','H')  then  quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end )";
            columns["profit_free_cl"].SqlFld = $@"sum(inout_flag*(-1)*sub_amount)- SUM ( case when coalesce(trade_type,'X')  not in ('J','H')  then  quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end ) - {showRebateProfit}";


            columns["profit_rate_free_cl"].SqlFld = @$"
round ( (
	(
		(
			round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round(sum( case when coalesce(trade_type,'X')   not in ('J','H')  then quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end )::numeric,2) - round({showRebateProfit}::numeric,2) )*100
	) /
	(
		case when round(sum(inout_flag*(-1)*sub_amount)::numeric,2)- round({showRebateProfit}::numeric,2) <>0
		     then round(sum(inout_flag*(-1)*sub_amount)::numeric,2)- round({showRebateProfit}::numeric,2) 
		     else null
		end
		) :: NUMERIC
	):: NUMERIC, 2)
";



            //赠品
            columns["cost_amount_hasfree"].SqlFld = $" SUM( round( (case when coalesce(trade_type,'X') not in ('CL','J','H')  and not (coalesce(sd.remark,'') like '%陈列%' and sub_amount=0) then quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end)::numeric, 2) ) ";
          //  columns["profit_hasfree"].SqlFld = $@"sum(inout_flag*(-1)*sub_amount)-sum(case when row_index = 1 then inout_flag * (-1) * coalesce(disc_amount, 0) else 0 end)- SUM ( case when coalesce(trade_type,'X') not in ('CL','J','H')  and  not (coalesce(sd.remark,'') like '%陈列%' and sub_amount=0) then quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end ) ";
            columns["profit_hasfree"].SqlFld = $@"sum(inout_flag*(-1)*sub_amount)- SUM ( case when coalesce(trade_type,'X') not in ('CL','J','H')  and  not (coalesce(sd.remark,'') like '%陈列%' and sub_amount=0) then quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end ) - {showRebateProfit}";

            columns["profit_rate_hasfree"].SqlFld = @$"
round ( (
	(
		(
			round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round(sum(case when coalesce(trade_type,'X')  not in ('CL','J','H') and  not (coalesce(sd.remark,'') like '%陈列%' and sub_amount=0) then quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end)::numeric,2) - round({showRebateProfit}::numeric,2) )*100
	) /
	(
		case when round(sum(inout_flag*(-1)*sub_amount)::numeric,2) - round({showRebateProfit}::numeric,2) <>0
		     then round(sum(inout_flag*(-1)*sub_amount)::numeric,2) - round({showRebateProfit}::numeric,2)
		     else null
		end
		) :: NUMERIC
	):: NUMERIC, 2)
";

             
           
            columns["free_cost_amount"].SqlFld = $"sum(round((case when sub_amount=0 and not (COALESCE(sd.trade_type,'X') in ('CL','J','H') or coalesce(sd.remark,'') like '%陈列%') then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end)::numeric,2) ) ";

            columns["cl_cost_amount"].SqlFld = $"sum(round((case when sub_amount=0  and (COALESCE(sd.trade_type,'X')='CL' or coalesce(sd.remark,'') like '%陈列%') then -quantity*sd.unit_factor*inout_flag*{costPrice} else 0 end)::numeric,2))  ";
            
            
            columns["cost_amount"].SqlFld = $"SUM ( round( (CASE WHEN sub_amount <> 0  THEN - inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END)::numeric, 2) )";
            if (columns.ContainsKey("return_cost_amount"))
            {
				columns["return_cost_amount"].SqlFld = $"SUM ( round( (CASE WHEN inout_flag * quantity>0 THEN  inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END)::numeric, 2) )";
            }

		//	columns["profit"].SqlFld = $@"sum(inout_flag*(-1)*sub_amount) - sum(case when row_index = 1 then inout_flag * (-1) * coalesce(disc_amount, 0) else 0 end)
//-SUM ( CASE WHEN sub_amount <> 0   THEN - inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END ) ";

            columns["profit"].SqlFld = $@"sum(inout_flag*(-1)*sub_amount)
-SUM ( CASE WHEN sub_amount <> 0   THEN - inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END ) - {showRebateProfit} ";

            columns["profit_rate"].SqlFld = @$"
round ( (
	(
        100*(round(sum(inout_flag*(-1)*sub_amount)::numeric,2)-round( SUM ( CASE WHEN sub_amount <> 0  THEN - inout_flag * quantity * sd.unit_factor * {costPrice} ELSE 0 END ) :: NUMERIC, 2 ) - round({showRebateProfit}::numeric,2) )
            	) /
            	(
            		case when round(sum(inout_flag*(-1)*sub_amount)::numeric,2)- round({showRebateProfit}::numeric,2) <>0 
            		     then round(sum(inout_flag*(-1)*sub_amount)::numeric,2)- round({showRebateProfit}::numeric,2)
            					else null 
            		end
		) :: NUMERIC
	):: NUMERIC, 2)
";
            if (columns.ContainsKey("sheet_disc"))
            {
                columns["sheet_disc"].SqlFld = @$"sum(case when row_index = 1 then money_inout_flag*coalesce(disc_amount,0) else 0 end)";

            }

            if (columns.ContainsKey("profit_has_free_cl_cut_disc"))
            {
                columns["profit_has_free_cl_cut_disc"].SqlFld = @$"
round(
(
sum(inout_flag*(-1)*sub_amount)
- SUM (case when coalesce(trade_type,'X')  not in ('J','H')  then  quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end ) 
- {showRebateProfit}
-sum(case when row_index = 1 then money_inout_flag*coalesce(disc_amount,0) else 0 end)
)::numeric
,2)
";

            }

            if (columns.ContainsKey("profit_rate_has_free_cl_cut_disc"))
            {
                columns["profit_has_free_cl_cut_disc"].SqlFld = @$"
round(
(
sum(inout_flag*(-1)*sub_amount)
- SUM (case when coalesce(trade_type,'X')  not in ('J','H')  then  quantity*sd.unit_factor*inout_flag*(-1)*{costPrice} else 0 end ) 
- {showRebateProfit}
-sum(case when row_index = 1 then money_inout_flag*coalesce(disc_amount,0) else 0 end)
)::numeric
,2)/
";

            }

            if (columns.ContainsKey("profit_cut_disc"))
			{
                columns["profit_cut_disc"].SqlFld = @$"
ROUND
(
    (
          SUM(inout_flag * (-1) * sub_amount)
        - SUM(case when row_index = 1 then money_inout_flag*coalesce(disc_amount,0) else 0 end)
        - SUM(quantity * sd.unit_factor*inout_flag * (-1) * {costPrice})
        + SUM(case when sub_amount=0 and trade_type !='J' then -quantity*sd.unit_factor * inout_flag * {costPrice} else 0 end)
        - {showRebateProfit}
    )::numeric
  ,2
)";

            }
            if (columns.ContainsKey("profit_rate_cut_disc"))
			{
                columns["profit_rate_cut_disc"].SqlFld = @$"
round
( 
      (
           SUM(inout_flag * ( -1 ) * sub_amount )
         - SUM (CASE WHEN row_index = 1 THEN inout_flag * ( -1 ) * COALESCE ( disc_amount, 0 ) ELSE 0 END )
         - SUM ( quantity * sd.unit_factor * inout_flag * ( -1 ) * {costPrice} )
         + SUM ( CASE WHEN sub_amount = 0 AND trade_type != 'J' THEN - quantity * sd.unit_factor * inout_flag *{costPrice} ELSE 0 END )
         - {showRebateProfit}
      )::numeric * 100
      /
      (
        CASE WHEN round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) - round({showRebateProfit}::numeric,2) <> 0 THEN
            round( SUM ( inout_flag * ( - 1 ) * sub_amount ) :: NUMERIC, 2 ) - round({showRebateProfit}::numeric,2) ELSE NULL 
        END 
      ) :: NUMERIC 
    , 2
)

"  ;
            }
               
        }
        public static void SetCostInfo(PageQueryModel page)
        {
            var costPriceType = "3";
            var costPriceTypeName = "预设进价";
            if (page.JsonCompanySetting.IsValid())
            {
                dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(page.JsonCompanySetting);
                if (setting != null && setting.costPriceType != null) costPriceType = setting.costPriceType;
            }
            /*
            var columns = page.Grids["gridItems"].Columns;
          
            bool seeProfit = false;
            if (page.JsonOperRights.IsValid())
            {
                bool seeInPrice = false;
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(page.JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";

                seeProfit = ((string)operRights?.delicacy?.seeProfit?.value)?.ToLower() != "false";
                if (!seeInPrice) seeProfit = false;

            }



            if (!seeProfit)
            {
                if (columns.ContainsKey("free_cost_amount")) columns["free_cost_amount"].HideOnLoad = columns["free_cost_amount"].Hidden = true;
                if (columns.ContainsKey("cl_cost_amount")) columns["cl_cost_amount"].HideOnLoad = columns["cl_cost_amount"].Hidden = true;

                if (columns.ContainsKey("cost_amount")) columns["cost_amount"].HideOnLoad = columns["cost_amount"].Hidden = true;
				if(columns.ContainsKey("return_cost_amount"))columns["return_cost_amount"].HideOnLoad = columns["return_cost_amount"].Hidden = true;

                if (columns.ContainsKey("profit")) columns["profit"].HideOnLoad = columns["profit"].Hidden = true;
                if (columns.ContainsKey("profit_rate")) columns["profit_rate"].HideOnLoad = columns["profit_rate"].Hidden = true;
                if (columns.ContainsKey("cost_amount_hasfree")) columns["cost_amount_hasfree"].HideOnLoad = columns["cost_amount_hasfree"].Hidden = true;
                if (columns.ContainsKey("profit_hasfree")) columns["profit_hasfree"].HideOnLoad = columns["profit_hasfree"].Hidden = true;
                if (columns.ContainsKey("profit_rate_hasfree")) columns["profit_rate_hasfree"].HideOnLoad = columns["profit_rate_hasfree"].Hidden = true;
                if (columns.ContainsKey("cost_amount_hasfree_cl_jh")) columns["cost_amount_hasfree_cl_jh"].HideOnLoad = columns["cost_amount_hasfree_cl_jh"].Hidden = true;
                if (columns.ContainsKey("profit_hasfree_cl_jh")) columns["profit_hasfree_cl_jh"].HideOnLoad = columns["profit_hasfree_cl_jh"].Hidden = true;
                if (columns.ContainsKey("profit_rate_hasfree_cl_jh")) columns["profit_rate_hasfree_cl_jh"].HideOnLoad = columns["profit_rate_hasfree_cl_jh"].Hidden = true;
                if (columns.ContainsKey("cost_amount_free_cl")) columns["cost_amount_free_cl"].HideOnLoad = columns["cost_amount_free_cl"].Hidden = true;
                if (columns.ContainsKey("profit_free_cl")) columns["profit_free_cl"].HideOnLoad = columns["profit_free_cl"].Hidden = true;
                if (columns.ContainsKey("profit_rate_free_cl")) columns["profit_rate_free_cl"].HideOnLoad = columns["profit_rate_free_cl"].Hidden = true;
                if(columns.ContainsKey("cost_price")) columns["cost_price"].HideOnLoad = columns["cost_price"].Hidden = true;

                if (columns.ContainsKey("profit_cut_disc")) columns["profit_cut_disc"].HideOnLoad = columns["profit_cut_disc"].Hidden = true;
                if (columns.ContainsKey("profit_rate_cut_disc")) columns["profit_rate_cut_disc"].HideOnLoad = columns["profit_rate_cut_disc"].Hidden = true;

                if (columns.ContainsKey("profit_has_free_cl_cut_disc")) columns["profit_has_free_cl_cut_disc"].HideOnLoad = columns["profit_has_free_cl_cut_disc"].Hidden = true;
             
            }
            */
            
            if (costPriceType == "4") costPriceTypeName = "最近平均进价";
            else if (costPriceType == "1") costPriceTypeName = "预设成本";
            else if (costPriceType == "2") costPriceTypeName = "加权平均成本";

            page.DataItems["cost_price_type"].Value = costPriceType;
            page.DataItems["cost_price_type"].Label = costPriceTypeName;
            //Console.WriteLine("Item页面的SetCostInfo方法获取的costPriceType:" + costPriceType);

        }
        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            SetProfitColumns(this);
            var sheetType = DataItems["sheetType"].Value;
            var startDay = DataItems["startDay"].Value;
            var endDay = DataItems["endDay"].Value;
            var seller_id = DataItems["seller_id"].Value;
            this.SQLVariables["startDay"] = startDay;
            this.SQLVariables["endDay"] = endDay;
            this.SQLVariables["IS_DEL"] = "";
            this.SQLVariables["SELLER_CONDI"] = "";
            if (sheetType.ToLower() == "xd")
            {
                this.SQLVariables["IS_DEL"] = "and coalesce(sm.is_del, false) = false";
            }
            if(seller_id!="")
            {
                this.SQLVariables["SELLER_CONDI"] = "and rbs.seller_id in ("+ seller_id+")";
            }

             
            var sql = this.Grids["gridItems"].QueryFromSQL;

            var grid = this.Grids.First().Value;
         
            var main_table = "sheet_sale_main";
            var detail_table = "sheet_sale_detail";

 
            string OTHER_DETAIL_MAIN_JOIN_CONDI = "";
            // if (!grid.Columns["rebate_amount_rbs"].Hidden || !grid.Columns["rebate_quantity_rbs"].Hidden)
            if ((grid.Columns.ContainsKey("rebate_amount_rbs") && !grid.Columns["rebate_amount_rbs"].Hidden) ||  (grid.Columns.ContainsKey("rebate_quantity_rbs") && !grid.Columns["rebate_quantity_rbs"].Hidden))
            {                
                string earlyTime = DataItems["startDay"].Value;
               
                detail_table = @$"
(
SELECT company_id,sheet_id,0 b_sheet_class,happen_time,   trade_type,   branch_id,   branch_position,   batch_id,item_id,  quantity,  orig_price,  real_price,  sub_amount,  rebate_price,        0 rebate_qty_rbs,            0 rebate_price_rbs, unit_no,unit_factor,  inout_flag,remark,  cost_price_avg,  cost_price_buy,  cost_price_prop,  cost_price_recent FROM         sheet_sale_detail where company_id={this.company_id} and happen_time>'{earlyTime}'
UNION
SELECT company_id,sheet_id,1 b_sheet_class,happen_time,'' trade_type,-1 branch_id,-1 branch_position,-1 batch_id,item_id,0 quantity,0 orig_price,0 real_price,0 sub_amount,0 rebate_price, quantity rebate_qty_rbs, rebate_price rebate_price_rbs, unit_no,unit_factor,1 inout_flag,remark,0 cost_price_avg,0 cost_price_buy,0 cost_price_prop,0 cost_price_recent FROM sheet_price_rebate_detail where company_id={this.company_id} and happen_time>'{earlyTime}'
)
";
                main_table = @$"
(
SELECT company_id,{(sheetType.ToLower() == "xd" ? "is_del," : ""         )}sheet_id,0 b_sheet_class,red_flag,money_inout_flag,happen_time,make_time,approve_time,   branch_id,supcust_id,seller_id,   senders_id,'' senders_name,total_amount,prepay_amount,payway1_id,payway1_amount,payway2_id,payway2_amount,payway3_id,payway3_amount, now_pay_amount,now_disc_amount,paid_amount,disc_amount,make_brief,sheet_attribute,department_id FROM sheet_sale_main         WHERE company_id={this.company_id} and happen_time>'{earlyTime}'
UNION
SELECT company_id,{(sheetType.ToLower() == "xd" ? "false as is_del," : "")}sheet_id,1 b_sheet_class,red_flag,money_inout_flag,happen_time,make_time,approve_time,-1 branch_id,supcust_id,seller_id,'' senders_id,'' senders_name,total_amount,prepay_amount,payway1_id,payway1_amount,payway2_id,payway2_amount,payway3_id,payway3_amount, now_pay_amount,now_disc_amount,paid_amount,disc_amount,make_brief,sheet_attribute,department_id FROM sheet_price_rebate_main WHERE company_id={this.company_id} and happen_time>'{earlyTime}'
)
";
                OTHER_DETAIL_MAIN_JOIN_CONDI = " and sd.b_sheet_class=sm.b_sheet_class";
               
            }
            else
            {
                if (grid.Columns.ContainsKey("rebate_amount_rbs"))
                {
                    grid.Columns.Remove("rebate_amount_rbs");
                }

                if (grid.Columns.ContainsKey("rebate_quantity_rbs"))
                {
                    grid.Columns.Remove("rebate_quantity_rbs");
                }
            }

            sql = sql.Replace("~OTHER_DETAIL_MAIN_JOIN_CONDI", OTHER_DETAIL_MAIN_JOIN_CONDI); 
            if (sheetType == "xd")
            {
               
                main_table = main_table.Replace("sheet_sale_main", "sheet_sale_order_main");
                detail_table = detail_table.Replace("sheet_sale_detail", "sheet_sale_order_detail");                
                sql = sql.Replace("~JOIN_STATUS_TABLE", "left join sheet_status_order sso on ");
            }


            sql = sql.Replace("~mainTable", main_table);
            sql = sql.Replace("~detailTable", detail_table);

             
            this.Grids["gridItems"].QueryFromSQL = sql;

        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            SetCostInfo(this);
            var sheetType = DataItems["sheetType"].Value;
            if (sheetType.IsValid() && sheetType.ToLower() =="xd"){
                this.DataItems.Remove("sale_way");
              
            }
    
        }

        public async Task OnGet()
        {
            await InitGet(cmd);
        }

    }



    [Route("api/[controller]/[action]")]
    public class SalesSummaryByItemController : QueryController
    { 
        public SalesSummaryByItemController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SalesSummaryByItemModel model = new SalesSummaryByItemModel(cmd);

            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }

        [HttpPost]
        public async Task<object> GetQueryRecords([FromBody] dynamic data)
        {
            // string cost_price_type_name = data.cost_price_type_name;
            SalesSummaryByItemModel model = new SalesSummaryByItemModel(cmd);
    
            object records = await model.GetRecordFromQuerySQL(Request, cmd, data);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            string sParams = Request.Form["params"];
            sParams = System.Web.HttpUtility.UrlDecode(sParams);
            dynamic queryParams = JsonConvert.DeserializeObject(sParams);
              
            SalesSummaryByItemModel model = new SalesSummaryByItemModel(cmd);
            //var sql = model.Grids["gridItems"].QueryFromSQL;
            //sql = sql.Replace("~mainTable", main_table);
            //sql = sql.Replace("~detailTable", detail_table);
            //model.Grids["gridItems"].QueryFromSQL = sql;
            return await model.ExportExcel(Request, cmd, queryParams);
        }

    }
}
