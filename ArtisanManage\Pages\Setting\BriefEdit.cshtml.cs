using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json.Linq;
using ArtisanManage.Models;
using System.Runtime.CompilerServices;
using ArtisanManage.Services;

namespace ArtisanManage.Pages.BaseInfo 
{
    public class BriefEditModel : PageFormModel
    {
        public BriefEditModel(CMySbCommand cmd,string company_id="",string oper_id="") : base(Services.MenuId.briefsView)
        {
            if (company_id != "") this.company_id = company_id;
            if (oper_id != "") this.OperID = oper_id;
            this.cmd = cmd;
            DataItems = new Dictionary<string, DataItem>()
            {
                {"brief_id",new DataItem(){Title="编号",CtrlType="hidden",FldArea="divHead"}},
                {"brief_text",new DataItem(){Title="备注内容",FldArea="divHead"}},
                //{"sheet_type",new DataItem(){Title="适用单据",FldArea="divHead",LabelFld = "sheet_type_name",LabelInDB = false, Value = "X", Label = "销售单",ButtonUsage = "list",Source = "[{v:'X',l:'销售单'}]"}},//,{v:'T',l:'退货单'},{v:'XD',l:'销售订单'},{v:'XT',l:'退货订单'},{v:'CG',l:'采购单'},{v:'CT',l:'采购退货单'},{v:'DB',l:'调拨单'}
                {"sheet_type",new DataItem(){Title="适用单据",FldArea="divHead",LabelFld = "sheet_type_name",LabelInDB = false, Value = "X", Label = "销售单",ButtonUsage = "list",Source = "[{v:'X',l:'销售'},{v:'CG',l:'采购'},{v:'DB',l:'调拨单'}]"}},//
               
                {"is_price_remember",new DataItem(){Title="记忆价格",FldArea="divHead",LabelFld="is_price",LabelInDB=false,Value = "false",Label="否",ButtonUsage = "list",DropDownWidth="100", DropDownHeight="50",  Source = "[{v:true,l:'是'},{v:false,l:'否'}]" } },
                {"relate_supplier_fee", new DataItem(){Title = "关联供应商费用", FldArea = "divHead",LabelFld = "is_related",LabelInDB=false,Value = "false",Label="否", ButtonUsage = "list", DropDownWidth = "100",DropDownHeight = "50", Source = "[{v:true, l:'是'},{v:false, l:'否'}]",}},
				//{"sub_id",new DataItem(){Title="赠品科目", SqlFld="info_sheet_detail_brief.sub_id", LabelFld="sub_name", Width="250", DropDownWidth="250", DropDownHeight="150", CtrlType="jqxDropDownTree",MumSelectable=true,FldArea="divHead",TreePathFld="",
                //{"is_price_remember",new DataItem(){Title="记忆价格",FldArea="divHead",LabelFld="is_price",LabelInDB=false,Value = "false",Label="否",ButtonUsage = "list",DropDownWidth="100", DropDownHeight="100",  Source = "[{v:true,l:'是'},{v:false,l:'否'}]" } },
                {"default_for_give",new DataItem(){Title="默认赠品备注",FldArea="divHead",LabelFld="is_default",LabelInDB=false,Value = "false",Label="否",ButtonUsage = "list",DropDownWidth="100", DropDownHeight="100",  Source = "[{v:true,l:'是'},{v:false,l:'否'}]" } },
                {"sub_id",new DataItem(){Title="赠品科目", SqlFld="info_sheet_detail_brief.sub_id", LabelFld="sub_name", Width="250", DropDownWidth="250", DropDownHeight="150", CtrlType="jqxDropDownTree",MumSelectable=true,FldArea="divHead",TreePathFld="",
                   SqlForOptions=$@"select cw1.sub_id as v, cw1.mother_id as pv,
                   substring((select string_agg(sub_name,'-' order by sub_code::text) as name from cw_subject where company_id= ~COMPANY_ID and sub_id::text in ( select * from REGEXP_SPLIT_TO_TABLE((select substring(concat(other_sub,sub_id),2) from cw_subject where company_id= ~COMPANY_ID and level>=1 and sub_id=cw1.sub_id), '/')  ) ),8) as l 
                    from cw_subject cw1 where cw1.company_id= ~COMPANY_ID and (cw1.status is null or cw1.status='1')  and cw1.direction=1
                        and ((cw1.sub_id not in (select mother_id from cw_subject where company_id= ~COMPANY_ID )  and substr(cw1.sub_code::text,1,4)='5601') or cw1.sub_code=5401) order by cw1.sub_code::text", JSAfterCreate=@"
                                    $('#sub_id').on('open', function (event) { 
                                        ajaxGet('/api/CwVoucherView/GetAcPeriod', { operKey: window.parent.g_operKey }).then(data => {
                                            if (data.result === 'OK') {
                                                console.log('cvview-get ac period', data);
                                            }else{
                                                bw.toast('如需选用科目，'+data.msg);
                                            }
                                        }).catch(error => { console.log(error) }); 
                                    });  "}},
            };

            m_idFld = "brief_id"; m_nameFld = "brief_text";
            m_tableName = "info_sheet_detail_brief";
            m_selectFromSQL = "from info_sheet_detail_brief  left join cw_subject cs on info_sheet_detail_brief.company_id=cs.company_id and info_sheet_detail_brief.sub_id=cs.sub_id where info_sheet_detail_brief.company_id= ~COMPANY_ID and brief_id='~ID'";
        }

        public async Task OnGet()
        {       
            await InitGet(cmd);
        } 
    }

    [ApiController]
    [Route("api/[controller]/[action]")]
    public class BriefEditController : BaseController
    { 
        public BriefEditController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            BriefEditModel model = new BriefEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<string> GetColumnOptions(string operKey,string gridID,string colName, string flds, string value, string availValues)
        {
            BriefEditModel model = new BriefEditModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.Grids[gridID].Columns, colName, flds, value, availValues);
            return data;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] dynamic request)
        {
            BriefEditModel model = new BriefEditModel(cmd);
            CMySbTransaction tran = cmd.Connection.BeginTransaction();
            string briefText = request.brief_text;
            if(string.IsNullOrEmpty(briefText))
            {
                return new JsonResult(new { result = "Error", msg = "备注内容不能为空，保存失败" });
            }
            try
            {
                #region 限制财务凭证用过的备注信息不能改备注文字也不能改科目
                dynamic thisBrief = null;
                if (request.brief_id != "")
                {
                    thisBrief = await CDbDealer.Get1RecordFromSQLAsync($"select * from info_sheet_detail_brief where company_id={cmd.company_id} and brief_id={request.brief_id}", cmd);
                    if (thisBrief != null && thisBrief.sub_id.ToString() != "")
                    {
                        dynamic vo = await CDbDealer.Get1RecordFromSQLAsync($@"select cvm.sheet_id as vo_id, sd.sheet_id
                            from (
                                select sheet_id,(jsonb_array_elements_text((sheet_attribute->>'biz_info')::jsonb))::jsonb as row from cw_voucher_main where company_id={cmd.company_id} and sheet_attribute is not null and red_flag is null
                            ) cvm
                            left join (
                                select distinct sheet_id from cw_voucher_detail where company_id={cmd.company_id} and position('赠品' in remark)>0 and sub_id={thisBrief.sub_id}
                            ) cvd on cvd.sheet_id=cvm.sheet_id
                            left join (
                                select distinct sheet_id from sheet_sale_detail where company_id={cmd.company_id} and remark='{thisBrief.brief_text}' and coalesce(sub_amount,0)=0
                            ) sd on sd.sheet_id::text=row->>'biz_sheet_id'
                            where cvm.row->>'biz_sheet_type' in ('X','T') and sd.sheet_id is not null ", cmd);
                        if (vo != null && (thisBrief.brief_text.ToString() != request.brief_text.ToString() || thisBrief.sub_id.ToString() != request.sub_id.ToString()))
                        {
                            return new JsonResult(new { result = "Error", msg = "该备注及其科目已被会计凭证使用过，无法修改或删除" });
                        }
                    } 
                }
                #endregion

                string default_for_give = request.default_for_give;
                if ((default_for_give??"").ToLower()=="true")
                {
                    string sql = $"update info_sheet_detail_brief set default_for_give = false where company_id={cmd.company_id}";
                    cmd.CommandText = sql;
                    await cmd.ExecuteNonQueryAsync();
                }
                var result = await model.SaveTable(cmd, request,tran);
                tran.Commit();
                return result;
            }
            catch(Exception ex)
            {
                MyLogger.LogMsg(model.company_id, "保存备注失败:" + ex.Message + ex.StackTrace);
                tran.Rollback();
                return new JsonResult(new { result = "Error", msg = "保存备注失败" });
            }
          
            
        }
    }
}