﻿
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.Sheets
{
    public class BuySheetSuggestModel : PageQueryModel
    { 
        private string buildJsAggregatesComputer(string unit_no, string qty)
        {
            var JsAggregatesComputer = $@"[{{
                                     'qty':
                                          function (aggregatedValue, currentValue, column, record) {{
                                                  var unit_no = record.{unit_no};
                                                  var qty = record.{qty};
                                                  currentValue = parseFloat(currentValue);
                                                  if (!currentValue) return aggregatedValue;
                                                  if (qty && unit_no) {{
                                                    if (!aggregatedValue) aggregatedValue = '';
                                                         var n = aggregatedValue.indexOf(unit_no);
                                                         if (n > 0) {{
                                                            var unitQty = 0;
                                                            for (i = n - 1; i >= 0; i--) {{
                                                                 var tmp = parseFloat(aggregatedValue.substring(i, n));
                                                                 if (tmp.toString() != 'NaN') {{
                                                                      unitQty = tmp;
                                                                 }}
                                                                 else break;
                                                             }}
                                                            if (unitQty > 0) {{
                                                               aggregatedValue = aggregatedValue.replace(unitQty + unit_no, toDecimal(unitQty + currentValue) + unit_no)
                                                            }}
                                                        }}
                                                        else
                                                        {{
                                                            aggregatedValue = aggregatedValue + currentValue.toString() + unit_no;
                                                         }}
                                                   }}
                                                return aggregatedValue;
                                                }}
                                        }}]
                                    ";

            return JsAggregatesComputer;
        }
        private string buildJsAggregatesRender()
        {
            return @" function aggregatesrenderer_quantity (aggregates, column, element, summaryData) {
                                               var renderstring = `<div class='jqx-widget-content style='float: left; width: 100%; height: 100%; '>`;
                                               $.each(aggregates, function (key, value) {
                                                    renderstring += '<div style=`position: relative; margin: 6px; text-align: right; overflow: hidden;`>' + value + '</div>';
                                               });
                                               renderstring +=`</div>`;
                                          return renderstring;
                                         } 
                     ";
        }

        
        public BuySheetSuggestModel(CMySbCommand cmd) : base(Services.MenuId.salesSummaryByItem)
        {
            this.cmd = cmd;
            this.PageTitle = "销售情况";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
               {"showBuyedOnly",new DataItem(){FldArea="divHead",Title="只看采购过的",CtrlType="jqxCheckBox",ForQuery=false}},
                {"other_class",new DataItem(){Title="类别", Checkboxes=true, FldArea="divHead",LabelFld="class_name",CtrlType="jqxDropDownTree",TreePathFld="other_class",MumSelectable=true,CompareOperator="like",
                   SqlForOptions= CommonTool.selectClasses} },
               
                {"branch_id",new DataItem(){Title="仓库",FldArea="divHead",LabelFld="branch_name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.branch_id",
                    SqlForOptions ="select branch_id as v,branch_name as l from info_branch"}},
                /*{"supcust_id",new DataItem(){FldArea="divHead",Title="客    户",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld="sm.supcust_id",
                    SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag like '%C%' and company_id=~COMPANY_ID "}},
                */
                {"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'2',l:'加权平均成本'},{v:'3',l:'预设进价'},{v:'1',l:'预设成本'},{v:'4',l:'最近平均进价'}]", CompareOperator="="
                }},
                {"purchase_cycle",new DataItem(){FldArea="divHead",Title="采购周期(天)",ForQuery=false,CtrlType="jqxInput",Value="30",
                    PlaceHolder="请输入天数(1-365)"
                }},
                {"supcust_id",new DataItem(){Title="供应商",FldArea="divHead",ForQuery=false,Hidden=true}}

            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"item_id",    new DataItem(){Title="商品",  Width="150",Hidden=true,SqlFld="sale_sum.item_id"}},
                       {"item_name",    new DataItem(){Title="商品名称",  Width="200",Linkable=true,Sortable=true}},
                       {"buy_qty",    new DataItem(){Title="建议采购数",  Width="120",GetFromDb=false,editable=true}},
                       {"buy_unit_no",    new DataItem(){Title="单位",CellsAlign="center", Width="60",SqlFld="case when b_unit_no is not null then b_unit_no else s_unit_no end"}},
                       {"net_quantity1", new DataItem(){Title="净销量(无单位)",  Hidden=true}},
                       {"net_quantity", new DataItem(){Title="净销量",  CellsAlign="center",  Width="80",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                            FuncGetSumValue = (sumColumnValues) =>
                            {
                               string sQty = sumColumnValues["net_quantity_b"]+"大";
                               if(sumColumnValues["net_quantity_m"]!="")
                                 sQty+= sumColumnValues["net_quantity_m"]+"中";
                               if(sumColumnValues["net_quantity_s"]!="")
                                 sQty+=sumColumnValues["net_quantity_s"]+"小";
                               return sQty;
                            }
                       }},
                       {"net_quantity_b", new DataItem(){Title="净销量(大)",  CellsAlign="center", 
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,

                       }},
                        {"net_quantity_m", new DataItem(){Title="净销量(中)",  CellsAlign="center",  
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       }},
                        {"net_quantity_s", new DataItem(){Title="净销量(小)",  CellsAlign="center",  
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       }},
                          {"stock_qty",   new DataItem(){Title="库存", Width="80",SqlFld="unit_from_s_to_bms (stock_qty::numeric,b_unit_factor,m_unit_factor,1,b_unit_no,m_unit_no,s_unit_no)"}},
                       {"last_buy_qty",    new DataItem(){Title="上次采购",  Width="100",SqlFld="unit_from_s_to_bms (last_buy_qty::numeric,b_unit_factor,m_unit_factor,1,b_unit_no,m_unit_no,s_unit_no)",Sortable=true}},
                      
                         {"b_unit_no",   new DataItem(){Title="大单位名称", Hidden=true}},
                       {"x_quantity1",   new DataItem(){Title="销售量(无单位)", Hidden=true } },
    
                       {"x_quantity",   new DataItem(){Title="总销量", CellsAlign="center", Sortable=true,   Width="100",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                            FuncGetSumValue = (sumColumnValues) =>
                           {
                               string sQty = sumColumnValues["x_quantity_b"]+"大";
                               if(sumColumnValues["x_quantity_m"]!="")
                                 sQty+= sumColumnValues["x_quantity_m"]+"中";
                               if(sumColumnValues["x_quantity_s"]!="")
                                 sQty+=sumColumnValues["x_quantity_s"]+"小";
                               return sQty;
                           }
                       }},
                       {"x_quantity_b",   new DataItem(){Title="总销量(大)", CellsAlign="center",  ShowSum=true,Hidden=true,
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_quantity_m",   new DataItem(){Title="总销量(中)", CellsAlign="center",  ShowSum=true,Hidden=true,
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_quantity_s",   new DataItem(){Title="总销量(小)", CellsAlign="center",  ShowSum=true,Hidden=true,
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"daily_sales_qty", new DataItem(){Title="近期日销量", Width="100", CellsAlign="center", Sortable=false, GetFromDb=false}},
                       {"estimated_sales_days", new DataItem(){Title="预计可销售天数", Width="120", CellsAlign="center", Sortable=false, GetFromDb=false}},
                       {"t_quantity1",   new DataItem(){Title="退货量(无单位)", Hidden=true
                        }},
                       {"t_quantity",   new DataItem(){Title="退货量", CellsAlign="center",Width="80",
                         
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                            FuncGetSumValue = (sumColumnValues) =>
                           {
                               string sQty = sumColumnValues["t_quantity_b"]+"大";
                               if(sumColumnValues["t_quantity_m"]!="")
                                 sQty+= sumColumnValues["t_quantity_m"]+"中";
                               if(sumColumnValues["t_quantity_s"]!="")
                                 sQty+=sumColumnValues["t_quantity_s"]+"小";
                               return sQty;
                           }
                       }},
                        {"t_quantity_b",   new DataItem(){Title="退货量(大)", CellsAlign="center",   ShowSum=true,Hidden=true,
                         
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                        {"t_quantity_m",   new DataItem(){Title="退货量(中)", CellsAlign="center",  ShowSum=true,Hidden=true,
                                                     FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                        {"t_quantity_s",   new DataItem(){Title="退货量(小)", CellsAlign="center", ShowSum=true,Hidden=true,
                          
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                 
                       {"x_amount",     new DataItem(){Title="销售金额", CellsAlign="right", Width="100", Sortable=true,SqlFld="",
                       ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; },}},
                       {"t_amount",     new DataItem(){Title="退货金额", CellsAlign="right", Width="100", Sortable=true,ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; },}},
                       {"net_amount",   new DataItem(){Title="销售净额", CellsAlign="right", Width="100", Sortable=true,ShowSum=true}},
                         //{"disc_amount",  new DataItem(){Title="优惠", CellsAlign="right",    Width="10%",SqlFld="sum(money_inout_flag*disc_amount)",ShowSum=true}},
                       {"cost_price",  new DataItem(){Title="成本", CellsAlign="right",    Width="70", Sortable=true,SqlFld="",ShowSum=true}},
                       {"profit",       new DataItem(){Title="利润", CellsAlign="right",    Width="70", Sortable=true,SqlFld="",ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; },}},

                     },
                     QueryFromSQL=
$@"
from 
(
	select sd.item_id as item_id,ip.item_name as item_name,b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no,round((sum(case when sd.tquantity<0 and sd.sub_amount!=0 then sd.tquantity*(-1) else 0 end)::numeric/b_unit_factor::numeric),2) as x_quantity1,unit_from_s_to_bms ((sum(case when sd.tquantity<0 then sd.tquantity*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as x_quantity,yj_get_unit_qty('b',sum(case when sd.tquantity<0 then sd.tquantity*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false) as x_quantity_b,yj_get_unit_qty('m',sum(case when sd.tquantity<0 then sd.tquantity*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false) as x_quantity_m,yj_get_unit_qty('s',sum(case when sd.tquantity<0 then sd.tquantity*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false) as x_quantity_s,round((sum(case when sd.tquantity>0 and sd.sub_amount!=0 then sd.tquantity*(-1) else 0 end)::numeric/b_unit_factor::numeric),2) as t_quantity1,unit_from_s_to_bms ((sum(case when sd.tquantity>0 then sd.tquantity*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)  as t_quantity,yj_get_unit_qty('b',sum(case when sd.tquantity>0 then sd.tquantity*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false) as t_quantity_b,yj_get_unit_qty('m',sum(case when sd.tquantity>0 then sd.tquantity*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false) as t_quantity_m,yj_get_unit_qty('s',sum(case when sd.tquantity>0 then sd.tquantity*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false) as t_quantity_s,round((sum(case when sd.tquantity!=0 and sd.sub_amount=0 then sd.tquantity*(-1) else 0 end)::numeric/b_unit_factor::numeric),2) as z_quantity1,unit_from_s_to_bms ((sum(case when sd.tquantity!=0 and sd.sub_amount=0 then sd.tquantity*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as z_quantity,yj_get_unit_qty('b',(sum(case when sd.tquantity!=0 and sd.sub_amount=0 then sd.tquantity*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,false) as z_quantity_b,yj_get_unit_qty('m',(sum(case when sd.tquantity!=0 and sd.sub_amount=0 then sd.tquantity*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,false) as z_quantity_m,yj_get_unit_qty('s',(sum(case when sd.tquantity!=0 and sd.sub_amount=0 then sd.tquantity*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,false) as z_quantity_s,round((sum(sd.tquantity*(-1))::numeric/b_unit_factor::numeric),2) as net_quantity1,unit_from_s_to_bms ((sum(case when sd.sub_amount<>0 then sd.tquantity*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) as net_quantity,yj_get_unit_qty ('b',sum(case when sd.sub_amount<>0 then sd.tquantity*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false) as net_quantity_b,yj_get_unit_qty ('m',sum(case when sd.sub_amount<>0 then sd.tquantity*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false) as net_quantity_m,yj_get_unit_qty ('s',sum(case when sd.sub_amount<>0 then sd.tquantity*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false) as net_quantity_s,round(sum(case when sd.tquantity<0 then inout_flag*(-1)*sub_amount else 0 end)::numeric,2) as x_amount,round(sum(disc_amount)::numeric,2) as disc_amount,round(sum(case when sd.tquantity>0 then inout_flag*(-1)*sub_amount else 0 end)::numeric,2) as t_amount,round(sum(inout_flag*(-1)*sub_amount)::numeric,2) as net_amount,round(sum((case when sd.tquantity!=0 and sd.sub_amount=0 then sd.tquantity*(-1) else 0 end)*sd.cost_price_buy)::numeric,2)  as free_cost_price,round(sum(quantity*sd.unit_factor*inout_flag*(-1)*sd.cost_price_buy)::numeric,2)  as cost_price,round((sum(inout_flag*(-1)*sub_amount)-sum(quantity*sd.unit_factor*inout_flag*(-1)*sd.cost_price_buy))::numeric,2) as profit 
    from  ~mainTable sm 
	left join 
	(
		select d.*,(quantity*d.unit_factor*d.inout_flag) tquantity,b_unit_factor::REAL,m_unit_factor::REAL,s_unit_factor::REAL,s_unit_no,m_unit_no,b_unit_no,b1->>'f1' b1_unit_factor,b1->>'f2' b1_unit_no,s1->>'f1' s1_unit_factor,s1->>'f2' s1_unit_no 
        from ~detailTable d
		LEFT JOIN
		(
            select item_id,(case when b is null then s end) as b1,(case when b is not null then s end) s1,b->>'f1' as b_unit_factor,m->>'f1' as m_unit_factor,s->>'f1' as s_unit_factor,b->>'f2' as b_unit_no,m->>'f2' as m_unit_no,s->>'f2' as s_unit_no from crosstab('select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json from info_item_multi_unit where company_id= ~COMPANY_ID order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s jsonb,m jsonb,b jsonb)
        ) t 
        on d.item_id=t.item_id where company_id= ~COMPANY_ID  
	) sd on sd.sheet_id = sm.sheet_id			
	left join info_item_prop ip on sd.item_id = ip.item_id and ip.company_id = ~COMPANY_ID
	left join info_operator io on sm.seller_id = io.oper_id and io.company_id = ~COMPANY_ID
	left join info_branch ib on ib.branch_id = sm.branch_id and ib.company_id = ~COMPANY_ID
	left join info_supcust s on s.supcust_id = sm.supcust_id and s.company_id = ~COMPANY_ID
	left join info_item_multi_unit mu on  sd.item_id = mu.item_id  and mu.unit_type = 's' and mu.company_id = ~COMPANY_ID 		 
	where sd.company_id= ~COMPANY_ID and sm.approve_time is not null and sm.red_flag is null and ~QUERY_CONDITION 		 
	group by sd.item_id,ip.item_name,mu.barcode,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no order by item_name
)  sale_sum 
left join 
(
   select item_id as stock_item_id,sum(stock_qty) as stock_qty from stock group by item_id
)  stock on sale_sum.item_id=stock.stock_item_id 
left join 
(
    select distinct d_buy.item_id ,d_buy.quantity as last_buy_qty from
    (
      select d.item_id, max(d.happen_time) last_time from sheet_buy_detail d left join sheet_buy_main m on d.sheet_id=m.sheet_id where d.company_id=~COMPANY_ID and m.supcust_id= ~VAR_SUP_ID GROUP BY d.item_id
    )  item_time left join 
    (
    select d.item_id,d.happen_time,sum(d.quantity * d.unit_factor) quantity from sheet_buy_detail d left join sheet_buy_main m on d.sheet_id=m.sheet_id where d.company_id=~COMPANY_ID and m.supcust_id= ~VAR_SUP_ID group by  d.happen_time,item_id
    )  d_buy on item_time.item_id=d_buy.item_id and item_time.last_time=d_buy.happen_time
) last_buy on sale_sum.item_id=last_buy.item_id ~VAR_LAST_BUY_CONDI
",
                     QueryGroupBySQL = "",
                     QueryOrderSQL=""
                  }
                }
            };
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            var cost_price_type = DataItems["cost_price_type"].Value;

            var columns = Grids.GetValueOrDefault("gridItems").Columns;
            var cost_price = columns.GetValueOrDefault("cost_price");
            var profit = columns.GetValueOrDefault("profit");
            var free_cost_price = columns.GetValueOrDefault("free_cost_price");
            var costPrice = "sd.cost_price_buy";//当前进价
            switch (cost_price_type)
            {
                case "3"://当前进价
                    costPrice = "sd.cost_price_buy";
                    break;
                case "2"://加权价
                    costPrice = "sd.cost_price_avg";
                    break;
                case "1"://预设成本
                    costPrice = "sd.cost_price_prop";
                    break;
            }


            this.SQLVariables["SUP_ID"]= DataItems["supcust_id"].Value;
            string showBuyedOnly = DataItems["showBuyedOnly"].Value;
            this.SQLVariables["LAST_BUY_CONDI"] = "";
            if (showBuyedOnly == "true")
            {
                this.SQLVariables["LAST_BUY_CONDI"] = " where last_buy.last_buy_qty is not null";
            }

            
          //  free_cost_price.SqlFld = $"round(sum((case when sd.tquantity!=0 and sd.sub_amount=0 then sd.tquantity*(-1) else 0 end)*{costPrice})::numeric,2) ";
          //   cost_price.SqlFld = $"round(sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice})::numeric,2) ";
          //  profit.SqlFld = $"round((sum(inout_flag*(-1)*sub_amount)-sum(quantity*sd.unit_factor*inout_flag*(-1)*{costPrice}))::numeric,2)";


        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            var costPriceType = "3";
            var costPriceTypeName = "预设进价";
            if (JsonCompanySetting.IsValid())
            {
                dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonCompanySetting);
                if (setting != null && setting.costPriceType != null) costPriceType = setting.costPriceType;
            }
            if (costPriceType == "1") costPriceTypeName = "预设成本";
            else if (costPriceType == "2") costPriceTypeName = "加权平均成本";
            else if (costPriceType == "4") costPriceTypeName = "最近平均进价";

            DataItems["cost_price_type"].Value = costPriceType;
            DataItems["cost_price_type"].Label = costPriceTypeName;
            var columns = Grids["gridItems"].Columns;

             string sup_id = CPubVars.RequestV(Request, "supcust_id");
            if (sup_id.IsValid())
            {
                cmd.CommandText = $"select happen_time from sheet_buy_main where supcust_id={sup_id} order by happen_time desc limit 1";
                object ov=await cmd.ExecuteScalarAsync();
                if(ov!=null && ov != DBNull.Value)
                {
                    DataItems["startDay"].Value = CPubVars.GetDateText(ov);
                }
            }




        }

        public async Task OnGet()
        {
            await InitGet(cmd);
        }

    }



    [Route("api/[controller]/[action]")]
    public class BuySheetSuggestController : QueryController
    { 
        public BuySheetSuggestController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            BuySheetSuggestModel model = new BuySheetSuggestModel(cmd);
            
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords(string cost_price_type_name, string sheetType)
        {
            var main_table = "sheet_sale_main";
            var detail_table = "sheet_sale_detail";
            if (sheetType == "xd")
            {
                main_table = "sheet_sale_order_main";
                detail_table = "sheet_sale_order_detail";
            }
            BuySheetSuggestModel model = new BuySheetSuggestModel(cmd);
            var sql = model.Grids["gridItems"].QueryFromSQL;
            sql = sql.Replace("~mainTable", main_table);
            sql = sql.Replace("~detailTable", detail_table);
            model.Grids["gridItems"].QueryFromSQL = sql;
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel(string sheetType)
        {
            var main_table = "sheet_sale_main";
            var detail_table = "sheet_sale_detail";
            if (sheetType == "xd")
            {
                main_table = "sheet_sale_order_main";
                detail_table = "sheet_sale_order_detail";
            }
            BuySheetSuggestModel model = new BuySheetSuggestModel(cmd);
            var sql = model.Grids["gridItems"].QueryFromSQL;
            sql = sql.Replace("~mainTable", main_table);
            sql = sql.Replace("~detailTable", detail_table);
            model.Grids["gridItems"].QueryFromSQL = sql;
            return await model.ExportExcel(Request, cmd);
        }

    }
}
