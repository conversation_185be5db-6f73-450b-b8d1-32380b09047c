﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace ArtisanManage.Pages.BaseInfo
{
    public class ClientValueModel : PageQueryModel
    { 
        public ClientValueModel(CMySbCommand cmd) : base(Services.MenuId.clientValue)
        {
            this.cmd = cmd;
            this.PageTitle = "客户价值分析";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, CompareOperator="=", QueryOnChange=false,Value=CPubVars.GetDateText(DateTime.Now.Date.AddDays(-90))+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput",ForQuery=false, CompareOperator="=", QueryOnChange=false,Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }"
                }},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客户",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld="sc.supcust_id",
                            SqlForOptions=CommonTool.selectSupcust } },
               
				{"brand_id", CommonTool.GetDataItem("brand_id", new DataItemChange(){ForQuery=false})},

				{"seller_id",new DataItem(){Title="业务员",FldArea="divHead",LabelFld="seller_name",ButtonUsage="list",ForQuery=false,SqlForOptions=CommonTool.selectSellers } },
                {"other_class",new DataItem(){Title="类别",MumSelectable=true,Checkboxes=true, FldArea="divHead",LabelFld="class_name",MaxRecords="1000",CtrlType="jqxDropDownTree",DropDownHeight="200", TreePathFld="class_path",CompareOperator="like",
                    SqlForOptions="select class_id as v,class_name as l,mother_id as pv from info_item_class order by order_index , class_id",
                    DealQueryItem = (value) =>
                    {
                        return "/"+value+"/";
                    }
                }},                {"other_region",new DataItem(){FldArea="divHead",Title="片区",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index "
                }},
                {"group_id",new DataItem(){Title="渠道",FldArea="divHead", LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_group",
                    SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},
                {"rank_id",new DataItem(){Title="等级", FldArea="divHead",LabelFld="rank_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_rank",
                    SqlForOptions ="select rank_id as v,rank_name as l from info_supcust_rank"}},
                //{"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="sd.item_id",DropDownWidth="300",
                //    SqlForOptions ="select item_id as v,item_name as l from info_item_prop" }},
                //{"other_class",new DataItem(){Title="类别",FldArea="divHead",LabelFld="class_name",CtrlType="jqxDropDownTree",TreePathFld="other_class",MumSelectable=true,CompareOperator="like",
                //   SqlForOptions="select class_id as v,class_name as l,py_str as z,mother_id as pv from info_item_class"} },
                //{"brand_id",new DataItem(){Title="品牌",FldArea="divHead",LabelFld="brand_name",ButtonUsage="list",CompareOperator="=",SqlFld="item_brand",
                //    SqlForOptions ="select brand_id as v,brand_name as l from info_item_brand"}},
                
                //{"seller_id",new DataItem(){Title="业务员",FldArea="divHead", LabelFld="seller_name",ButtonUsage="list",CompareOperator="=",SqlFld="seller_id",SqlForOptions=CommonTool.selectSellers } },
                   // SqlForOptions ="select oper_id as v,oper_name as l from info_operator"}},
                
                
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true, Sortable=true,
                     ZeroAsEmpty = "true",


                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"supcust_id",     new DataItem(){Title="客户", Sortable=true,SqlFld="sc.supcust_id", Width="150",Hidden=true}},
                       {"sup_name",     new DataItem(){Title="客户名称", Sortable=true,SqlFld="",  Width="150" ,Linkable=true }},
                       {"create_time",     new DataItem(){Title="建档日期", Sortable=true,SqlFld="TO_CHAR(create_time ,'yyyy-MM-DD') ",  Width="150" }},
                       {"first_time",     new DataItem(){Title="首次销售日期", Sortable=true,SqlFld="(case when min(sm.happen_time) is null then '未销售' else TO_CHAR(min(sm.happen_time), 'yyyy-MM-DD') end) ",  Width="150" }},
                       {"last_time",     new DataItem(){Title="上次销售日期", Sortable=true,SqlFld="CASE WHEN TO_CHAR( MIN ( sm.happen_time ), 'yyyy-MM-DD' )=TO_CHAR( MAX ( sm.happen_time ), 'yyyy-MM-DD' ) THEN '未复购' else TO_CHAR( MAX ( sm.happen_time ), 'yyyy-MM-DD' ) END",  Width="150" }},
                       {"avg_sale_period",     new DataItem(){Title="平均销售周期（天）", Sortable=true,SqlFld="COALESCE(avg_sale_period,0)",  Width="150" }},
                       {"avg_sheet_price",     new DataItem(){Title="平均客单价", Sortable=true,SqlFld="COALESCE(avg_sheet_price,0)",  Width="150" }},                     
                       {"sale_day",     new DataItem(){Title="销售天数", Sortable=true,SqlFld="T.sale_day",  Width="150" }},
                        {"sale_amount",     new DataItem(){Title="销售金额", Sortable=true,SqlFld="T.total_sale_amount",  Width="150" }},
                        {"arrears_balance",     new DataItem(){Title="尚欠金额", Sortable=true,SqlFld="COALESCE(ab.balance,0)",  Width="150" }},
  //                      {"item_num",     new DataItem(){Title="在售商品数", Sortable=true,SqlFld="COALESCE(item_num,0)",  Width="150" ,Linkable=true}},
                       {"sheet_num",     new DataItem(){Title="历史单据", Sortable=true,SqlFld="COALESCE(sheet_num,0)",  Width="150" ,Linkable=true}},
                       {"not_sale_day",     new DataItem(){Title="超期未售天数", Sortable=true,SqlFld="case when (ROUND(date_part('day',current_date-cast(TO_CHAR(max(sm.happen_time), 'yyyy-MM-DD') as timestamp) )::NUMERIC ,1)-avg_sale_period)>0  then ROUND(date_part('day',current_date-cast(TO_CHAR(max(sm.happen_time), 'yyyy-MM-DD') as timestamp) )::NUMERIC ,1) - avg_sale_period else null end",  Width="150" }},

                     },
                     QueryFromSQL=@"
from info_supcust sc
left join arrears_balance ab on sc.company_id = ab.company_id and sc.supcust_id =ab.supcust_id
left join sheet_sale_main sm on sc.company_id = sm.company_id and sc.supcust_id =sm.supcust_id
LEFT JOIN
(
    select sm.supcust_id,
		sheet_count.total_sale_amount,
        sheet_count.sale_day,
        round((CASE WHEN COUNT (DISTINCT sm.sheet_id ) > 0 THEN date_part( 'day', CAST ( '~VAR_endDay' AS TIMESTAMP ) - CAST ( '~VAR_startDay' AS TIMESTAMP ) ) / sheet_count.sheet_num ELSE 0 END ) :: NUMERIC,0 ) AS avg_sale_period,
        round((CASE WHEN COUNT (distinct sm.sheet_id ) > 0 and ( money_inout_flag > 0) THEN sheet_count.total_sale_amount / sheet_count.sheet_num ELSE 0 END ) :: NUMERIC,1 ) AS avg_sheet_price,
       
        sheet_count.sheet_num as sheet_num,
	    ip.other_class 
    from  sheet_sale_main sm
    left join sheet_sale_detail sd on sm.sheet_id =sd.sheet_id and sd.company_id= sm.company_id
    left join  info_item_prop ip on sd.item_id = ip.item_id and sd.company_id= ip.company_id
    LEFT JOIN (
			select supcust_id, count(1) sheet_num,sum(total_amount*money_inout_flag) total_sale_amount,
            	COUNT (DISTINCT TO_CHAR( happen_time, 'yyyy-MM-DD' )) sale_day
            from sheet_sale_main 
		  where happen_time >='~VAR_startDay'
            and happen_time <'~VAR_endDay' and approve_time is not null and company_id=~COMPANY_ID and company_id=~COMPANY_ID
            and sheet_type='X' and red_flag is null 
              ~VAR_seller_id
            and sheet_sale_main.supcust_id in (~VAR_supcust_id_values)
			GROUP BY supcust_id
							
	) sheet_count on sheet_count.supcust_id = sm.supcust_id
    where sm.happen_time >='~VAR_startDay'
            and sm.happen_time <'~VAR_endDay' and sm.approve_time is not null and sm.company_id=~COMPANY_ID and sd.company_id=~COMPANY_ID
            and sm.sheet_type='X' and sm.red_flag is null 
             ~VAR_brand_id  ~VAR_seller_id
            and sm.supcust_id in (~VAR_supcust_id_values)
    GROUP BY sm.supcust_id ,money_inout_flag,sheet_count.sale_day,sheet_count.sheet_num,sheet_count.total_sale_amount,	ip.other_class
) t on sc.supcust_id =t.supcust_id
where  sm.company_id=~COMPANY_ID and
sc.supcust_id in (~VAR_supcust_id_values) ~VAR_seller_id
                      
                        ",
                     QueryGroupBySQL =" GROUP BY sc.supcust_id, sup_name,create_time,avg_sale_period,avg_sheet_price,sheet_num,sale_day,ab.balance,T.total_sale_amount",
                     QueryOrderSQL=""

                  }
                } 
            };             
        }


        public async Task OnGet()
        {
            await InitGet(cmd);
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            SQLVariables["startDay"] = DataItems["startDay"].Value;
            SQLVariables["endDay"] = DataItems["endDay"].Value;

            var brand  = DataItems["brand_id"].Value;  
            if (brand != "")
            {
                SQLVariables["brand_id"] = $"AND ip.item_brand = '{brand}'";
            }
            else
            {
                SQLVariables["brand_id"] = "";
            }
            var seller_id = DataItems["seller_id"].Value;
            if (seller_id != "")
            {
                SQLVariables["seller_id"] = $"AND seller_id = {seller_id}";
            }
            else
            {
                SQLVariables["seller_id"] = "";
            }
            string supcustIDValueSQL = @$"select  string_agg(supcust_id::text,',') as supcust_ids from info_supcust where  company_id={company_id}  and supcust_flag ='C'  and (status is null or status = 1)";
            dynamic data = await CDbDealer.Get1RecordFromSQLAsync(supcustIDValueSQL,cmd);
            SQLVariables["supcust_id_values"] = data.supcust_ids;
        }



    }



    [Route("api/[controller]/[action]")]
    public class ClientValueController : QueryController
    { 
        public ClientValueController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            ClientValueModel model = new ClientValueModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        

        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
           
          
            ClientValueModel model = new ClientValueModel(cmd);
            
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

    
        [HttpPost]
        public async Task<ActionResult> ExportExcel(string sheetType)
        {
            
            ClientValueModel model = new ClientValueModel(cmd);
            
            return await model.ExportExcel(Request, cmd);
        }
    }
}
