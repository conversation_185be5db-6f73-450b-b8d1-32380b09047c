﻿using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Threading.Tasks;
using ArtisanManage.Models;
using ArtisanManage.Services;
using MathNet.Numerics.Distributions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using static ArtisanManage.Models.DataItem;

namespace ArtisanManage.Pages.BaseInfo
{
    public class SalesSummaryBySellerAndItemModel : PageQueryModel
    { 
        public SalesSummaryBySellerAndItemModel(CMySbCommand cmd) : base(Services.MenuId.salesSummaryBySellerAndItem)
        {
            this.UsePostMethod = true;
            this.cmd = cmd;
            this.PageTitle = "销售汇总(业务员/商品)";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time+sd.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期",FldArea="divHead", CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time+sd.happen_time", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"status",new DataItem(){FldArea="divHead",   Title="单据状态",Checkboxes=true, ButtonUsage = "list",CompareOperator="=",Value="approved",Label="已审核",
                        Source = @"[{v:'normal',l:'所有',condition:""sm.red_flag is null""},
                                   {v:'unapproved',l:'未审核',condition:""sm.approve_time is null""},
                                   {v:'approved',l:'已审核',condition:""sm.approve_time is not null and sm.red_flag is null""}]"

                }},
                {"trade_type",new DataItem(){FldArea="divHead",Checkboxes=true, Title="交易类型",ButtonUsage = "list",CompareOperator="=",Value="",Label="",
                        Source = @"[{v:'ALL',l:'所有',condition:""true""},
                                   {v:'XT',l:'销退',condition:""coalesce(trade_type,'X') in ('X','T','XD','TD')""},
                                   {v:'DH',l:'定货还货',condition:""trade_type='DH'""},
                                   {v:'JH',l:'借还货',condition:""trade_type in ('J','H')""},
                                   {v:'CL',l:'陈列兑付',condition:""trade_type ='CL'""},
                                   {v:'HH',l:'换货',condition:""trade_type in ('HR','HC')""}]"
                }},
               
				{"brand_id", CommonTool.GetDataItem("brand_id", new DataItemChange(){SqlFld="ip.item_brand"})},
				{"other_class",new DataItem(){Title="类别",FldArea="divHead",Checkboxes=true,MaxRecords = "1000", LabelFld="class_name",CtrlType="jqxDropDownTree",TreePathFld="other_class",MumSelectable=true,CompareOperator="like",
                   SqlForOptions=CommonTool.selectClasses} },
                /*{"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",SqlFld="sd.item_id",DropDownWidth="300",
                   QueryByLabelLikeIfIdEmpty=true, SqlForOptions ="select item_id as v,item_name as l,py_str as z from info_item_prop" }},*/
                
                {"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="sd.item_id",DropDownWidth="300",
                    SqlForOptions =@"select item_id as v,item_name as l,py_str as z,s_barcode,m_barcode,b_barcode from info_item_prop ip
                                       left join (select item_id mu_item_id,s_barcode,m_barcode,b_barcode from crosstab('select item_id,unit_type,barcode 
                                                    from info_item_multi_unit where company_id=~COMPANY_ID order by item_id', $$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_barcode text,m_barcode text, b_barcode text)) t
                                     on ip.item_id=t.mu_item_id where company_id=~COMPANY_ID  and ~QUERY_CONDITION",SearchFields="['l','z','s_barcode','m_barcode','b_barcode']" }},
                {"seller_id",new DataItem(){Title="业务员",Checkboxes=true,FldArea="divHead",LabelFld="seller_name",ButtonUsage="list",CompareOperator="=",SqlFld="seller_id",SqlForOptions=CommonTool.selectSellers } },
                    //SqlForOptions ="select oper_id as v,oper_name as l from info_operator"}},
                {"branch_id",new DataItem(){Title="仓库",Checkboxes=true,FldArea="divHead",LabelFld="branch_name",ButtonUsage="list",CompareOperator="=",SqlFld="sm.branch_id",
                    SqlForOptions ="select branch_id as v,branch_name as l from info_branch"}},
                {"supcust_id",new DataItem(){FldArea="divHead",Title="客    户",LabelFld="sup_name", ButtonUsage="event",CompareOperator="=",SqlFld="sm.supcust_id",
                    SqlForOptions="select supcust_id as v,sup_name as l,py_str as z from info_supcust where supcust_flag like '%C%' and company_id=~COMPANY_ID "}},
                {"group_id",new DataItem(){Title="渠道",Checkboxes=true,FldArea="divHead", LabelFld="group_name",ButtonUsage="list",CompareOperator="=",SqlFld="sup_group",
                    SqlForOptions ="select group_id as v,group_name as l from info_supcust_group"}},
                {"other_region",new DataItem(){FldArea="divHead",Title="片区",LabelFld="region_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MaxRecords="500",MumSelectable=true,DropDownWidth="150", TreePathFld="other_region",CompareOperator="like",
                    SqlForOptions="select region_id as v,region_name as l,mother_id as pv from info_region  order by  mother_id,order_index "
                }},
                //{"sup_rank",new DataItem(){Title="等级",FldArea="divHead",LabelFld="rank_name",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",CompareOperator="=",
                {"sup_rank",new DataItem(){Title="等级",Checkboxes=true,FldArea="divHead",LabelFld="rank_name",ButtonUsage="list",DropDownHeight="200",DropDownWidth="150",CompareOperator="=",
                    SqlForOptions="select rank_id as v,rank_name as l from info_supcust_rank"
                }},
                {"depart_path",new DataItem(){Title="部门",FldArea="divHead",LabelFld="depart_path_name",CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=true,DropDownWidth="150", TreePathFld="depart_path",CompareOperator="like",LikeWrapper="/",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                 {"department_id",new DataItem(){Title="所属部门",TreePathFld="department_path",Hidden=true, FldArea="divHead",LabelFld="department_id_label", CtrlType="jqxDropDownTree",DropDownHeight="200",MumSelectable=false,DropDownWidth="150", CompareOperator="=",
                    SqlForOptions="select depart_id as v,depart_name as l,mother_id as pv from info_department"
                }},
                {"cost_price_type",new DataItem(){FldArea="divHead",Title="成本核算",ForQuery=false,LabelFld="cost_price_type_name",ButtonUsage="list",Source = "[{v:'3',l:'预设进价'},{v:'2',l:'加权平均价'},{v:'1',l:'预设成本'},{v:'4',l:'最近平均进价'}]", CompareOperator="="}},
                {"saleWay",new DataItem(){FldArea="divHead",Title="销售方式",ForQuery=true,LabelFld="sale_ways",ButtonUsage="list",Value="all",Label="所有",AfterGroup=true,
                    Source = "[{v:'sale',l:'销售',condition:\"sum(case when sd.sub_amount<>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)<>'0'\"},{v:'return',l:'退货',condition:\"sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)<>'0'\"},{v:'free',l:'赠品',condition:\"sum(case when sd.sub_amount=0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)<>'0'\"},{v:'all',l:'所有',condition:'1=1'}]", 
                    CompareOperator="=",Checkboxes=true } },
                {"senders_id",
                    new DataItem()
                    {
                        FldArea = "divHead", Title = "送货员",Checkboxes=true,SqlFld = "','||senders_id||','", LabelFld = "senders_name", ButtonUsage = "list",
                        DealQueryItem = status => ","+status+",",
                        SqlForOptions=CommonTool.selectSenders,  //SqlForOptions = "select oper_id as v,oper_name as l,py_str as z from info_operator",
                        CompareOperator = "like"
                    }
                },
                {"make_brief",new DataItem(){Title="整单备注",FldArea="divHead",CompareOperator="ilike" } },
                {"remark",new DataItem(){Title="商品备注",FldArea="divHead",CompareOperator="ilike" } },
                 {"arrears_status",new DataItem(){FldArea="divHead",Title="欠款情况", Hidden = true, Checkboxes=true,LabelFld = "status_name",ButtonUsage = "list",CompareOperator="=",
                    Source = @"[{v:'cleared',l:'已结清',condition:""abs(total_amount-paid_amount-disc_amount)<0.1""},
                                 {v:'uncleared',l:'未结清',condition:""abs(total_amount-paid_amount-disc_amount)>0.1""},
                                 {v:'all',l:'所有',condition:""true""}]"
                }},
                {"showRebateProfit",new DataItem(){FldArea="divHead",Title="显示补差后利润",CtrlType="jqxCheckBox",Hidden=true,ForQuery=false,Value="false"}},
                {"sheetType",new DataItem(){Title="",FldArea="divHead",Hidden=true,ForQuery=false,HideOnLoad = true} }
            };
             
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates=true,
                     Sortable=true,
                     PageByOverAgg = false,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"seller_id",    new DataItem(){Title="",  Width="150",SqlFld="sm.seller_id",Hidden=true,HideOnLoad = true}},
                       {"oper_name", new DataItem(){Title="业务员名称", Sortable=true,Pinned=true, Linkable=true, Width="150", SqlFld="io.oper_name" }},
                        {"item_id",    new DataItem(){Title="", Width="150", Hidden=true,HideOnLoad = true,SqlFld="sd.item_id"}},
                       {"item_name",    new DataItem(){Title="商品名称",  Width="150",Pinned=true, SqlFld="ip.item_name",Linkable=true,Sortable=true}},
                       {"b_unit_no",   new DataItem(){Title="大单位名称", Width="",SqlFld="b_unit_no",Hidden=true,HideOnLoad = true}},
                       {"unit_conv",new DataItem(){Title="单位换算",ButtonUsage="list", CellsAlign="center",Width="200",Hidden=true,
                           SqlFld = @"yj_get_unit_relation(b_unit_no,b_unit_factor,m_unit_no,m_unit_factor,s_unit_no)"}},
                       {"x_quantity1",   new DataItem(){Title="销售量(无单位)", Width="150",Hidden=true,HideOnLoad = true,SqlFld="round((sum(case when sd.quantity>0 and sd.sub_amount!=0 then sd.quantity*sd.unit_factor*(-1) else 0 end)::numeric/b_unit_factor::numeric),2)"}},

                       {"x_quantity2",   new DataItem(){Title="总销量(大)", Width="100",Hidden=true,HideOnLoad = false,ShowSum=true,SqlFld="round((sum(case when sd.quantity*sd.inout_flag<0 and sd.sub_amount<>0 then sd.quantity*sd.unit_factor else 0 end)::numeric/b_unit_factor::numeric),2)"}},
                       {"x_quantity3",   new DataItem(){Title="总销量(小)", Width="100",Hidden=true,HideOnLoad = false,ShowSum=true, SqlFld="sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric"}},

                       {"x_quantity",   new DataItem(){Title="总销量", CellsAlign="center", Sortable=true,   Width="100",
                            SqlFld="unit_from_s_to_bms ((sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                            SortFld="sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                            FuncGetSumValue = (sumColumnValues) =>
                           {
                               string sQty = "";
                               if(sumColumnValues["x_quantity_b"]!="")  sQty+= sumColumnValues["x_quantity_b"]+"大";
                               if(sumColumnValues["x_quantity_m"]!="")  sQty+= sumColumnValues["x_quantity_m"]+"中";
                               if(sumColumnValues["x_quantity_s"]!="")  sQty+= sumColumnValues["x_quantity_s"]+"小";
                               return sQty;
                           }
                       }},
                       {"x_quantity_b",   new DataItem(){Title="总销量(大)", CellsAlign="center",   Width="100",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld="yj_get_unit_qty('b',sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_quantity_m",   new DataItem(){Title="总销量(中)", CellsAlign="center",   Width="100",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld="yj_get_unit_qty('m',sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"x_quantity_s",   new DataItem(){Title="总销量(小)", CellsAlign="center",   Width="100",ShowSum=true,Hidden=true,HideOnLoad = true,
                            SqlFld="yj_get_unit_qty('s',sum(case when sd.quantity*sd.inout_flag<0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                       {"rebate_quantity",   new DataItem(){Title="补差数量", Sortable=true,  CellsAlign="right",  Width="100",SqlFld="round(sum(case when coalesce(rebate_price, 0) <> 0 then quantity*sd.unit_factor else 0 end)::numeric,2)",ShowSum=true,Hidden=true}},
                       {"t_quantity1",   new DataItem(){Title="退货量(无单位)", Hidden=true, HideOnLoad = true, Width="100",SqlFld="round((sum(case when sd.quantity<0 then sd.quantity*sd.unit_factor else 0 end)::numeric/b_unit_factor::numeric),2)"}},

                       {"t_quantity2",   new DataItem(){Title="退货量(大)", Hidden=true, HideOnLoad = false, Width="70",ShowSum=true, SqlFld="round((sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric/b_unit_factor::numeric),2)"}},
                       {"t_quantity3",   new DataItem(){Title="退货量(小)", Hidden=true, HideOnLoad = false, Width="100",ShowSum=true, SqlFld="sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric"}},

                       {"t_quantity",   new DataItem(){Title="退货量", CellsAlign="center",   Width="100", 
                           SqlFld="unit_from_s_to_bms (sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no) ",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },
                            FuncGetSumValue = (sumColumnValues) =>
                           {
                               string sQty = "";
                               if(sumColumnValues["t_quantity_b"]!="") sQty+= sumColumnValues["t_quantity_b"]+"大";
                               if(sumColumnValues["t_quantity_m"]!="") sQty+= sumColumnValues["t_quantity_m"]+"中";
                               if(sumColumnValues["t_quantity_s"]!="") sQty+= sumColumnValues["t_quantity_s"]+"小";
                               return sQty;
                           }
                       }},
                        {"t_quantity_b",   new DataItem(){Title="退货量(大)", CellsAlign="center",   Width="100",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty('b',sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                        {"t_quantity_m",   new DataItem(){Title="退货量(中)", CellsAlign="center",   Width="100",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty('m',sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                        {"t_quantity_s",   new DataItem(){Title="退货量(小)", CellsAlign="center",   Width="100",ShowSum=true,Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty('s',sum(case when sd.quantity*sd.inout_flag>0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                       {"z_quantity1",   new DataItem(){Title="赠品量(无单位)", Width="120",Hidden=true,HideOnLoad = true,SqlFld="round((sum(case when sd.quantity>0 and sd.sub_amount=0 then sd.quantity*sd.unit_factor*sd.inout_flag else 0 end)::numeric/b_unit_factor::numeric),2)"}},

                       {"z_quantity2",   new DataItem(){Title="赠品量(大)", Width="100",Hidden=true,HideOnLoad = false,ShowSum=true,SqlFld="round((sum(case when sd.quantity>0 and sd.sub_amount=0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric/b_unit_factor::numeric),2)"}},
                       {"z_quantity3",   new DataItem(){Title="赠品量(小)", Width="100",Hidden=true,HideOnLoad = false,ShowSum=true,SqlFld="sum(case when sd.quantity>0 and sd.sub_amount=0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric"}},

                       {"z_quantity",   new DataItem(){Title="赠品量", CellsAlign="center",Width="100",
                           SqlFld="unit_from_s_to_bms ((sum(case when sd.quantity>0 and sd.sub_amount=0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               string sQty = "";
                                if(sumColumnValues["z_quantity_b"]!="") sQty+= sumColumnValues["z_quantity_b"]+"大";
                               if(sumColumnValues["z_quantity_m"]!="") sQty+= sumColumnValues["z_quantity_m"]+"中";
                               if(sumColumnValues["z_quantity_s"]!="") sQty+=sumColumnValues["z_quantity_s"]+"小";
                               return sQty;
                           }
                       }},
                        {"z_quantity_b",   new DataItem(){Title="赠品量(大)", CellsAlign="center",   Width="100",ShowSum=true, Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty('b',(sum(case when sd.quantity>0 and sd.sub_amount=0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; } 
                       }},
                        {"z_quantity_m",   new DataItem(){Title="赠品量(中)", CellsAlign="center",   Width="100",ShowSum=true, Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty('m',(sum(case when sd.quantity>0 and sd.sub_amount=0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},
                        {"z_quantity_s",   new DataItem(){Title="赠品量(小)", CellsAlign="center",   Width="100",ShowSum=true, Hidden=true,HideOnLoad = true,
                           SqlFld="yj_get_unit_qty('s',(sum(case when sd.quantity>0 and sd.sub_amount=0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric),b_unit_factor,m_unit_factor,false)",
                           FuncDealMe=(value)=>{return value=="0"?"":value; }
                       }},

                        {"net_quantity1", new DataItem(){Title="净销量(大)",  Hidden=true,HideOnLoad = false,  Width="100",ShowSum=true, SqlFld = @"round(CAST(sum(case when sd.sub_amount <> 0 then sd.quantity * sd.unit_factor * sd.inout_flag * (-1) else 0 end) AS numeric) / b_unit_factor, 2)"}},
                        {"net_quantity2",  new DataItem(){Title = "净销量(小)", Hidden=true,HideOnLoad = false,  Width="100",ShowSum=true, SqlFld="sum(case when sd.sub_amount<> 0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)"}},

                       {"net_quantity", new DataItem(){Title="净销量",  CellsAlign="center",  Width="100",SqlFld="unit_from_s_to_bms(sum(case when sd.sub_amount<>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no)",
                           SortFld="sum(case when sd.sub_amount<>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric",
                           Sortable=true,
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                            FuncGetSumValue = (sumColumnValues) =>
                            {
                               string sQty = "";
                                if(sumColumnValues["net_quantity_b"]!="") sQty+= sumColumnValues["net_quantity_b"]+"大";
                               if(sumColumnValues["net_quantity_m"]!="") sQty+= sumColumnValues["net_quantity_m"]+"中";
                               if(sumColumnValues["net_quantity_s"]!="") sQty+=sumColumnValues["net_quantity_s"]+"小";
                               return sQty;
                            }
                       }},
                       {"net_quantity_b", new DataItem(){Title="净销量(大)",  CellsAlign="center",  Width="100",SqlFld="yj_get_unit_qty ('b',sum(case when sd.sub_amount<>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,

                       }},
                        {"net_quantity_m", new DataItem(){Title="净销量(中)",  CellsAlign="center",  Width="100",SqlFld="yj_get_unit_qty ('m',sum(case when sd.sub_amount<>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,
                       }},
                        {"net_quantity_s", new DataItem(){Title="净销量(小)",  CellsAlign="center",  Width="100",SqlFld="yj_get_unit_qty ('s',sum(case when sd.sub_amount<>0 then sd.quantity*sd.unit_factor*sd.inout_flag*(-1) else 0 end)::numeric,b_unit_factor,m_unit_factor,false)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,HideOnLoad = true,
                       }},
                       {"net_quantity_per_sender_b", new DataItem(){Title="净销量送货员人均(大单位数)",  CellsAlign="center",  Width="100",SqlFld="round(sum(sd.quantity*sd.unit_factor*sd.inout_flag*(-1)/array_length(string_to_array(sm.senders_id,','),1))::numeric/b_unit_factor,2)",
                            FuncDealMe=(value)=>{return value=="0"?"":value; },ShowSum=true, Hidden=true,
                       }},
                       {"x_amount",     new DataItem(){Title="销售金额", CellsAlign="right", Width="100", Sortable=true,SqlFld="round(sum(case when sd.quantity*sd.inout_flag<0 then sd.inout_flag*(-1)*sd.sub_amount else 0 end)::numeric,2)",
                       ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; },}},
                       {"disc_amount",     new DataItem(){Title="价格优惠", CellsAlign="right", Width="100", Sortable=true,SqlFld="round(sum((orig_price-real_price)*sd.quantity*sd.inout_flag*(-1))::numeric,2)",ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; },}},

                       {"t_amount",     new DataItem(){Title="退货金额", CellsAlign="right", Width="100", Sortable=true,SqlFld="round(sum(case when sd.quantity*sd.inout_flag>0 then sd.sub_amount*sd.inout_flag else 0 end)::numeric,2)",ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; },}},
                       {"net_amount",   new DataItem(){Title="销售净额", CellsAlign="right", Width="100", Sortable=true,SqlFld="round(sum(inout_flag*(-1)*sub_amount)::numeric,2)",ShowSum=true,ShowRowPercent=true}},
                       {"net_rebate_amount",   new DataItem(){Title="销售净额(补差后)", CellsAlign="right", Width="150", Sortable=true,Hidden= true, SqlFld="round(sum(inout_flag*(-1)*sub_amount - coalesce(rebate_price, 0)*quantity*sd.unit_factor)::numeric,2)",ShowSum=true,ShowRowPercent=true}},
                       //{"disc_amount",  new DataItem(){Title="优惠", CellsAlign="right",    Width="10%",SqlFld="sum(money_inout_flag*disc_amount)",ShowSum=true}},
                       /*{"cost_amount_hasfree",  new DataItem(){Title="成本(含赠)", CellsAlign="right",    Width="8%", Sortable=true,SqlFld="Will be changed by condition",ShowSum=true}},
                       {"profit_hasfree",       new DataItem(){Title="利润(含赠)", CellsAlign="right",    Width="8%", Sortable=true,ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; }}},
                       {"profit_rate_hasfree",new DataItem(){ Title = "利润率(%)(含赠)", Sortable=true,CellsAlign = "right",Width = "7%",ShowAvg = true,
                        FuncDealMe=(value)=>{return value=="0"?"":value; },
                            FuncGetSumValue = (sumColumnValues) =>
                           {
                               string s_profit_hasfree =sumColumnValues["profit_hasfree"];
                               string s_net_amount =sumColumnValues["net_amount"];
                               
                               double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
                               double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                               string rate="";
                               if (net_amount != 0)
                               {
                                   rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
                               }
                               return rate;
                           }


                       }},

                       {"free_cost_amount",new DataItem(){Title="赠品成本",CellsAlign="right",Width="8%",SqlFld="",ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; }} },
                       {"cost_amount",  new DataItem(){Title="成本", CellsAlign="right",    Width="8%", Sortable=true,SqlFld="Will be changed by condition",ShowSum=true}},
                       {"profit",       new DataItem(){Title="利润", CellsAlign="right",    Width="8%", Sortable=true,ShowSum=true,FuncDealMe=(value)=>{return value=="0"?"":value; }}},
                       {"profit_rate",new DataItem(){ Title = "利润率(%)", Sortable=true,CellsAlign = "right",Width = "7%",ShowAvg = true,
                        FuncGetSumValue = (sumColumnValues) =>
                           {
                               string s_profit_hasfree =sumColumnValues["profit"];
                               string s_net_amount =sumColumnValues["net_amount"];

                               double profit_hasfree=s_profit_hasfree!=""?Convert.ToDouble(s_profit_hasfree) : 0.0;
                               double net_amount=s_net_amount!=""?Convert.ToDouble(s_net_amount) : 0.0;
                               string rate="";
                               if (net_amount != 0)
                               {
                                   rate=CPubVars.FormatMoney(profit_hasfree/net_amount*100,1);
                               }
                               return rate;
                           }
                       }},*/

                     },
                    QueryFromSQL=$@"
from ~mainTable sm 
left join ~detailTable sd on sd.sheet_id=sm.sheet_id and sd.company_id=sm.company_id
left join
(SELECT s.item_id,
        b.unit_factor::numeric AS b_unit_factor,
        m.unit_factor::numeric AS m_unit_factor,
        s.unit_factor::numeric AS s_unit_factor,
        b.unit_no              AS b_unit_no,
        m.unit_no              AS m_unit_no,
        s.unit_no              AS s_unit_no
 FROM info_item_multi_unit s
          LEFT JOIN info_item_multi_unit b
                    ON s.item_id = b.item_id AND b.unit_type = 'b' AND b.company_id = ~COMPANY_ID
          LEFT JOIN info_item_multi_unit m
                    ON s.item_id = m.item_id AND m.unit_type = 'm' AND m.company_id = ~COMPANY_ID
 WHERE s.unit_type = 's'
   AND s.company_id = ~COMPANY_ID) t
on sd.item_id = t.item_id
left join info_item_prop ip on sd.item_id = ip.item_id and ip.company_id = ~COMPANY_ID
LEFT JOIN info_supcust sc ON sm.supcust_id = sc.supcust_id AND sc.company_id = ~COMPANY_ID
left join info_operator io on sm.seller_id = io.oper_id and io.company_id = ~COMPANY_ID
left join info_branch ib on ib.branch_id = sm.branch_id and ib.company_id = ~COMPANY_ID
left join info_item_multi_unit mu on  sd.item_id = mu.item_id  and mu.unit_type = 's' and mu.company_id = ~COMPANY_ID 
where sm.company_id= ~COMPANY_ID and sd.company_id= ~COMPANY_ID and sd.inout_flag <>0 ~VAR_IS_DEL and ~VAR_detail_happen_time
",
                     /*AND sm.supcust_id IN (SELECT sc.supcust_id AS supcust_id FROM ~mainTable sm
			LEFT JOIN ~detailTable sd ON sm.sheet_id = sd.sheet_id AND sd.company_id = ~COMPANY_ID
			LEFT JOIN info_item_prop ip ON sd.item_id = ip.item_id AND ip.company_id = ~COMPANY_ID
			LEFT JOIN info_supcust sc ON sm.supcust_id = sc.supcust_id AND sc.company_id = ~COMPANY_ID
			LEFT JOIN info_operator io ON sm.seller_id = io.oper_id AND io.company_id = ~COMPANY_ID
			LEFT JOIN info_item_multi_unit mu ON mu.item_id = sd.item_id AND unit_type = 's' AND mu.company_id = ~COMPANY_ID
			LEFT JOIN info_item_brand b ON b.brand_id = ip.item_brand AND b.company_id = ~COMPANY_ID
			WHERE sm.company_id = ~COMPANY_ID AND sm.approve_time IS NOT NULL AND sm.red_flag IS NULL 
			GROUP BY sc.supcust_id,sup_name ORDER BY sup_name LIMIT 200 OFFSET 0)*/
                     
                      QueryGroupBySQL = " GROUP BY sm.seller_id,io.oper_name,sd.item_id,ip.item_name,mu.barcode,b_unit_factor,m_unit_factor,s_unit_factor,b_unit_no,m_unit_no,s_unit_no",
                     
                      QueryOrderSQL=" ORDER BY  sm.seller_id,sd.item_id"
                  }
                }
            };
			var origCols = Grids["gridItems"].Columns;
			var cols = SalesSummaryByItemModel.GetProfitColumns(origCols,false);
            
            foreach (var k in cols)
            {
                origCols.Add(k.Key, k.Value);
            }
        }

        public override async Task OnQueryConditionGot(CMySbCommand cmd)
        {
            //Item页面中的SetProfitColumns方法
            SalesSummaryByItemModel.SetProfitColumns(this);
            var startDay = DataItems["startDay"].Value;
            var endDay = DataItems["endDay"].Value;
            SQLVariables["detail_happen_time"] = @$" (sd.happen_time >= '{startDay}') and (sd.happen_time < '{endDay}' )";
            var sheetType = DataItems["sheetType"].Value;
            this.SQLVariables["IS_DEL"] = "";
            if(sheetType.ToLower() == "xd")
            {
                this.SQLVariables["IS_DEL"] = "and coalesce(sm.is_del, false) = false";
            }
        }

        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            SalesSummaryByItemModel.SetCostInfo(this);
            /*
            var costPriceType = "3";
            var costPriceTypeName = "预设进价";
            if (JsonCompanySetting.IsValid())
            {
                dynamic setting = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonCompanySetting);
                if (setting != null && setting.costPriceType != null) costPriceType = setting.costPriceType;
            }
            var columns = Grids["gridItems"].Columns;
            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights != null && operRights.delicacy != null) seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower()=="true";
            }
            if (!seeInPrice)
            { 
                columns["free_cost_amount"].HideOnLoad = columns["free_cost_amount"].Hidden = true;
                columns["cost_amount"].HideOnLoad = columns["cost_amount"].Hidden = true;
                columns["profit"].HideOnLoad = columns["profit"].Hidden = true;
                columns["profit_rate"].HideOnLoad = columns["profit_rate"].Hidden = true;
                columns["cost_amount_hasfree"].HideOnLoad = columns["cost_amount_hasfree"].Hidden = true;
                columns["profit_hasfree"].HideOnLoad = columns["profit_hasfree"].Hidden = true;
                columns["profit_rate_hasfree"].HideOnLoad = columns["profit_rate_hasfree"].Hidden = true;
            }

            if (costPriceType == "4") costPriceTypeName = "最近平均进价";
            else if (costPriceType == "1") costPriceTypeName = "预设成本";
            else if (costPriceType == "2") costPriceTypeName = "加权平均成本";
            DataItems["cost_price_type"].Value = costPriceType;
            DataItems["cost_price_type"].Label = costPriceTypeName;
          
            var sheetType = DataItems["sheetType"].Value;
            */
        }

        public async Task OnGet()
        {
            await InitGet(cmd);
        }

    }



    [Route("api/[controller]/[action]")]
    public class SalesSummaryBySellerAndItemController : QueryController
    { 
        public SalesSummaryBySellerAndItemController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }

        [HttpGet]
        public async Task<JsonResult> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            SalesSummaryBySellerAndItemModel model = new SalesSummaryBySellerAndItemModel(cmd);
            return await PageBaseModel.GetDataItemOptionsJson(cmd, operKey, model.DataItems, dataItemName, flds, value, availValues);

        }
        [HttpPost]
        public async Task<object> GetQueryRecords([FromBody] dynamic data)
        {
            //string cost_price_type_name = data.cost_price_type_name;
            string sheetType = data.sheetType;
            var main_table = "sheet_sale_main";
            var detail_table = "sheet_sale_detail";
            if (sheetType == "xd")
            {
                main_table = "sheet_sale_order_main";
                detail_table = "sheet_sale_order_detail";
            }
            SalesSummaryBySellerAndItemModel model = new SalesSummaryBySellerAndItemModel(cmd);
            var sql = model.Grids["gridItems"].QueryFromSQL;
            sql = sql.Replace("~mainTable", main_table);
            sql = sql.Replace("~detailTable", detail_table);
            model.Grids["gridItems"].QueryFromSQL = sql;
            object records = await model.GetRecordFromQuerySQL(Request, cmd, data);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            string sParams = Request.Form["params"];
            sParams = System.Web.HttpUtility.UrlDecode(sParams);
            dynamic queryParams = JsonConvert.DeserializeObject(sParams);
            string sheetType = queryParams.sheetType;
            var main_table = "sheet_sale_main";
            var detail_table = "sheet_sale_detail";
            if (sheetType == "xd")
            {
                main_table = "sheet_sale_order_main";
                detail_table = "sheet_sale_order_detail";
            }
            SalesSummaryBySellerAndItemModel model = new SalesSummaryBySellerAndItemModel(cmd);
            var sql = model.Grids["gridItems"].QueryFromSQL;
            sql = sql.Replace("~mainTable", main_table);
            sql = sql.Replace("~detailTable", detail_table);
            model.Grids["gridItems"].QueryFromSQL = sql;
            return await model.ExportExcel(Request, cmd);
        }

    }
}
