﻿using ArtisanManage.Models;
using ArtisanManage.Pages;
using ArtisanManage.Services;
using MathNet.Numerics;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Mvc;
using Microsoft.CodeAnalysis.Elfie.Diagnostics;
using Microsoft.CodeAnalysis.Operations;
using myJXC;
using Newtonsoft.Json;
using NPOI.HSSF.Record;
using Org.BouncyCastle.Utilities.Collections;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Dynamic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using ArtisanManage.MyCW;
using NPOI.SS.Formula.PTG;
using ArtisanManage.YingjiangMessage.Services;
using ArtisanManage.YingjiangMessage.Pojo;
using Microsoft.IdentityModel.Tokens;
using NPOI.SS.Formula;
using System.Text.RegularExpressions;

namespace ArtisanManage.MyJXC
{
    public class SheetRowMove : SheetRowItem
    {
        public SheetRowMove()
        {

        }
        public new string branch_position { get; set; } = "";
        public new string branch_position_name { get; set; } = "";
        public new string branch_id { get; set; } = "";
        public new string branch_name { get; set; } = "";

        [SaveToDB][FromFld] public string from_branch_position { get; set; } = "0";
        [FromFld(LOAD_PURPOSE.SHOW)] public string from_branch_position_name { get; set; } = "";
        [SaveToDB][FromFld] public string to_branch_position { get; set; } = "0";
        [FromFld(LOAD_PURPOSE.SHOW)] public string to_branch_position_name { get; set; } = "";
        [SaveToDB] [FromFld] public string wholesale_price { get; set; }
        // 上次批发价
        [SaveToDB] [FromFld] public string last_time_price { get; set; }
		 
		[FromFld("(case when cost_price_type=1 then round((t.cost_price_prop*t.unit_factor  )::numeric,2) when cost_price_type=2 then round((t.cost_price_avg*t.unit_factor)::numeric,2) else round((t.buy_price*t.unit_factor)::numeric,2) end ) as cost_price", LOAD_PURPOSE.SHOW)] public string cost_price { get; set; }
        [FromFld("(case when cost_price_type=1 then round((t.cost_price_prop * t.quantity*t.unit_factor)::numeric,2) when cost_price_type=2 then round((t.cost_price_avg*t.quantity*t.unit_factor)::numeric,2) else round((t.buy_price*t.quantity*t.unit_factor)::numeric,2) end ) as cost_amount", LOAD_PURPOSE.SHOW)] public string cost_amount { get; set; }
        [SaveToDB] [FromFld]  public string buy_price { get; set; }
        [SaveToDB] [FromFld] public string cost_price_avg { get; set; }
        [SaveToDB] [FromFld] public string cost_price_prop { get; set; }
        [FromFld("mu.s_retail_price", LOAD_PURPOSE.SHOW)] public string s_retail_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string from_stock_qty { get; set; }

        [FromFld(LOAD_PURPOSE.SHOW)] public string to_stock_qty { get; set; }

        [FromFld(LOAD_PURPOSE.SHOW)] public string item_spec { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string s_weight { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string m_weight { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public virtual string b_weight { get; set; }
        //public decimal sheet_order_quantity { get; set; }
        [SaveToDB] [FromFld] public string contract_price { get; set; }
        internal bool attrRememberPrice = false;
        public string contract_sub_amount
        {
            get
            {
                if (!string.IsNullOrEmpty(contract_price))
                {
                    return CPubVars.FormatMoney(CPubVars.ToDecimal(contract_price) * quantity, 2);
                }
                return "";
            }
        }

        public decimal unit_weight
        {
            get
            {
                decimal unitWeight = 0m;
                decimal nb_weight = b_weight.IsValid() ? CPubVars.ToDecimal(b_weight) : 0m;
                decimal nm_weight = m_weight.IsValid() ? CPubVars.ToDecimal(m_weight) : 0m;
                decimal ns_weight = s_weight.IsValid() ? CPubVars.ToDecimal(s_weight) : 0m;

                if (unit_no == b_unit_no)
                {
                    unitWeight = nb_weight;
                    if (unitWeight == 0)
                    {
                        if (m_unit_factor != "" && nm_weight > 0) unitWeight = nm_weight / CPubVars.ToDecimal(m_unit_factor) * CPubVars.ToDecimal(b_unit_factor);
                        else if (ns_weight > 0) unitWeight = ns_weight * CPubVars.ToDecimal(b_unit_factor);
                    }
                }
                else if (unit_no == m_unit_no)
                {
                    unitWeight = nm_weight;
                    if (unitWeight == 0)
                    {
                        if (b_unit_factor != "" && nb_weight > 0) unitWeight = nb_weight / CPubVars.ToDecimal(b_unit_factor) * CPubVars.ToDecimal(m_unit_factor);
                        else if (ns_weight > 0) unitWeight = ns_weight * CPubVars.ToDecimal(m_unit_factor);
                    }
                }
                else if (unit_no == s_unit_no)
                {
                    unitWeight = ns_weight;
                    if (unitWeight == 0)
                    {
                        if (b_unit_factor != "" && nb_weight > 0) unitWeight = nb_weight / CPubVars.ToDecimal(b_unit_factor);
                        else if (m_unit_factor != "" && nm_weight > 0) unitWeight = nm_weight / CPubVars.ToDecimal(m_unit_factor);
                    }
                }
                return Math.Round(unitWeight, 3);
            }
        }
        public string wholesale_sub_amount {
            get
            {
                if (wholesale_price.IsValid())
                {
                    return CPubVars.FormatMoney(CPubVars.ToDecimal(wholesale_price) * quantity, 2);
                }
                else
                    return "";
                 
            }
        }

        public string wholesale_sub_cent
        {
            get
            {
                if (wholesale_sub_amount.IsValid())
                {
                    return CPubVars.FormatMoney(CPubVars.ToDecimal(wholesale_sub_amount) * 100, 0);
                }
                return "";               
            }
        }
        public string buy_sub_amount
        {
            get
            {
                if (buy_price.IsValid())
                {
                    return CPubVars.FormatMoney(CPubVars.ToDecimal(buy_price) * quantity, 2);
                }
                else
                    return "";
                
            }
        }


        [FromFld("s_wholesale_price", LOAD_PURPOSE.SHOW)] public string s_wholesale_price { get; set; }
 
        [FromFld("b_wholesale_price", LOAD_PURPOSE.SHOW)] public string b_wholesale_price { get; set; }
   
        [FromFld("m_wholesale_price", LOAD_PURPOSE.SHOW)] public string m_wholesale_price { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string son_mum_item { get; set; }

		[FromFld("setting->>'validDayType'", LOAD_PURPOSE.SHOW)] public string cs_valid_day_type { get; set; }
		[FromFld("valid_day_type", LOAD_PURPOSE.SHOW)] public string valid_day_type { get; set; }
		[FromFld("valid_days", LOAD_PURPOSE.SHOW)] public string _valid_days { get; set; }
		public string valid_days
		{
			get
			{
				if (_valid_days.IsValid())
				{
					string vt = "d";
					if (cs_valid_day_type.IsValid()) vt = cs_valid_day_type;
					if (valid_day_type.IsValid()) vt = valid_day_type;
					if (vt == "m") vt = "个月"; else if (vt == "d") vt = "天"; else if (vt == "y") vt = "年";
					return _valid_days + vt;
				}
				else return "";
			}
		}
		internal bool HasFromStockQty = false, HasToStockQty = false;
        internal decimal FromStockQty = 0, ToStockQty=0, FromSellPendQty = 0, ToSellPendQty=0;
 
        public override void SetInfoForPrint(bool smallUnitBarcode)
        {
            base.SetInfoForPrint(smallUnitBarcode);
            var row = this;
             
        }
        [FromFld("m.sheet_no")] public string sheet_no { get; set; }
    }

    public enum Assign_Van
    {
        EMPTY,
        ASSIGN_VAN,
        BACK_VAN
    }
    public class SheetMove : SheetBase<SheetRowMove>
    {
        
       
        [SaveToDB][FromFld] public string order_sheet_id { get; set; } = "";
        [SaveToDB][FromFld] public string from_branch_id { get; set; } = "";
    
        [FromFld(LOAD_PURPOSE.SHOW)] public string from_branch_name { get; set; } = "";
        [SaveToDB][FromFld] public string to_branch_id { get; set; } = "";
        [SaveToDB][FromFld] public string receiver_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string receiver_name { get; set; } = "";
        [SaveToDB][FromFld] public string sender_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string sender_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string to_branch_name { get; set; } = "";
        [SaveToDB][FromFld] public string seller_id { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_name { get; set; } = "";
        [FromFld(LOAD_PURPOSE.SHOW)] public string seller_mobile { get; set; } = "";
        [SaveToDB][FromFld] public decimal wholesale_amount { get; set; } = 0;
        [SaveToDB][FromFld] public decimal contract_amount { get; set; } = 0;
        [SaveToDB][FromFld] public decimal cost_amount_avg { get; set; } = 0;
        [SaveToDB][FromFld] public decimal cost_amount_prop { get; set; } = 0;
        [SaveToDB][FromFld] public decimal buy_amount { get; set; } = 0;

        public Dictionary<string, string> OtherSheetAttributes = null;
        [SaveToDB][FromFld] public override string is_imported { get; set; } = "";
        // 关联的客户
        public string relate_client { get; set; } = "";
        public override string print_count {
            get {
                string s = _print_count;
                if (s == "") s = "1";
                else s = (Convert.ToInt32(s) + 1).ToString();
                return s;
            }
            set
            {
                _print_count = value;
            }
        }
        [FromFld("sheet_print_count", LOAD_PURPOSE.SHOW)]
        public string _print_count { get; set; }

        [FromFld(LOAD_PURPOSE.SHOW)] public string from_branch_type { get; set; }
        [FromFld(LOAD_PURPOSE.SHOW)] public string to_branch_type { get; set; }
        public string wholesale_cent
        {
            get
            {
                return CPubVars.FormatMoney(wholesale_amount * 100, 0);
            }
        }
        public string wholesale_cn_wan
        {
            get
            {
                int wan = (int)(wholesale_amount / 10000);
                if (wan == 0) return "";
                string s = MoneyToUpper(wan.ToString()).Replace("圆", "").Replace("整", "");
                return s;
            }
        }
        public string cent_wholesale_cn_qian
        {
            get
            {
                decimal y = wholesale_amount % 10000;
                string s1 = CPubVars.FormatMoney(y, 2, true);
                string s = MoneyToUpper(s1, true);

                for (int i = s.Length; i < 6; i++)
                {
                    s = "零" + s;
                }
                return s;
            }
        }
        public string happen_year
        {
            get
            {
                if (this.happen_time == "") return "";
                return DateTime.Parse(this.happen_time).Year.ToString();
            }
        }


        public string happen_month
        {
            get
            {
                if (this.happen_time == "") return "";
                return DateTime.Parse(this.happen_time).Month.ToString();
            }
        }
        public string happen_day
        {
            get
            {
                if (this.happen_time == "") return "";
                return DateTime.Parse(this.happen_time).Day.ToString();
            }
        }

        public string move_type
        {
            get
            {
                if (from_branch_type == "store" && to_branch_type == "truck")
                {
                    return "装车单";
                }
                else if (from_branch_type == "truck" && to_branch_type == "store")
                {
                    return "回库单";
                }
                else return "调拨单";
            }
        }
        [SaveToDB][FromFld] public string sheet_attribute
        {
            get
            {
                Dictionary<string, string> sheetAttribute = new Dictionary<string, string>();


                IDictionary<string, object> d = new ExpandoObject();
                d["assignVan"] = "";
                if (assign_van != 0)
                {
                    if (assign_van == Assign_Van.ASSIGN_VAN) d["assignVan"] = "assign";
                    if (assign_van == Assign_Van.BACK_VAN) d["assignVan"] = "back";
                }
                if (SaleOrderSheetIDs != "") d["saleOrderSheetIDs"] = SaleOrderSheetIDs;
                if (SaleOrderSheetNos != "") d["saleOrderSheetNos"] = SaleOrderSheetNos;

                if (OtherSheetAttributes != null)
                {
                    foreach (var k in OtherSheetAttributes)
                    {
                        if (!d.ContainsKey(k.Key))
                            d.Add(k.Key, k.Value);
                    }
                }



                if (d.Count > 0)
                {
                    return JsonConvert.SerializeObject(d);
                }


                return "";
            }
            set
            {
                if (value.IsValid())
                {
                    dynamic d = JsonConvert.DeserializeObject(value);
                    if (d.saleOrderSheetIDs != null) SaleOrderSheetIDs = d.saleOrderSheetIDs;
                    if (d.saleOrderSheetNos != null) SaleOrderSheetNos = d.saleOrderSheetNos;
                    if (d.assignVan != null)
                    {
                        if (d.assignVan == "assign") assign_van = Assign_Van.ASSIGN_VAN;
                        else if (d.assignVan == "back") assign_van = Assign_Van.BACK_VAN;
                    }
                }
            }
        }
        public Assign_Van assign_van { get; set; } = Assign_Van.EMPTY;
        public string SaleOrderSheetIDs = "";
        public string SaleOrderSheetNos = "";

        public string sum_item_name = "";
        private void ConstructFun(LOAD_PURPOSE loadPurpose)
        {
            if (loadPurpose == LOAD_PURPOSE.SHOW)
            {
                MainLeftJoin = @" left join (select branch_id,branch_name as from_branch_name,branch_type as from_branch_type from info_branch where company_id=~company_id) fb on t.from_branch_id=fb.branch_id    
                                  left join (select branch_id,branch_name as  to_branch_name,branch_type as to_branch_type from info_branch where company_id=~company_id) tb on t.to_branch_id=tb.branch_id           
                                  left join (select oper_id,oper_name as seller_name,mobile as seller_mobile from info_operator where company_id=~company_id) seller on t.seller_id=seller.oper_id
                                  left join (select oper_id,oper_name as maker_name from info_operator where company_id=~company_id) maker on t.maker_id=maker.oper_id
                                  left join  (select oper_id,oper_name as approver_name from info_operator where company_id=~company_id) approver on t.approver_id=approver.oper_id
                                  left join  (select oper_id,oper_name as receiver_name from info_operator where company_id=~company_id) receiver on t.receiver_id=receiver.oper_id
                                  left join  (select oper_id,oper_name as sender_name from info_operator where company_id=~company_id) send on t.sender_id=send.oper_id
                                  left join sheet_status_move s on t.sheet_id=s.sheet_id and s.company_id = ~company_id
                            
              ";
                DetailLeftJoin = @"left join sheet_move_main m on t.sheet_id=m.sheet_id and m.company_id =~company_id
                                   left join (select brief_id as remark_id from info_sheet_detail_brief where company_id=~company_id) b on b.remark_id=t.remark_id
                                   left join (select company_id,cost_price_type,setting from company_setting where company_id=~company_id ) s on s.company_id = t.company_id
                                                                  
                                   left join (select item_id,company_id,branch_id,stock_qty as from_stock_qty, batch_id,branch_position from stock where company_id=~company_id) fs on t.item_id = fs.item_id and t.company_id = fs.company_id and m.from_branch_id = fs.branch_id and fs.batch_id = COALESCE(t.batch_id,0) and fs.branch_position = COALESCE(t.from_branch_position,0)
                                   left join (select item_id,company_id,branch_id,stock_qty as to_stock_qty ,batch_id,branch_position from stock where company_id=~company_id) ts on t.item_id = ts.item_id and t.company_id = ts.company_id and m.to_branch_id = ts.branch_id  and ts.batch_id =COALESCE(t.batch_id,0) and ts.branch_position = COALESCE(t.to_branch_position,0)
                                   left join info_item_prop ip on t.item_id=ip.item_id and ip.company_id=~company_id    
                                   left join info_item_brand ib on ip.item_brand=ib.brand_id and ib.company_id=~company_id
                                   left join (select class_id classId,class_name,order_index as class_order_index from info_item_class where company_id =~company_id) ic on ip.item_class=ic.classId 
                                   
		                           left join (select item_id,s_unit->>'f1' as s_unit_no,s_unit->>'f2' as s_unit_factor,s_unit->>'f3' as s_barcode,s_unit->>'f4' as s_wholesale_price,s_unit->>'f5' as s_retail_price,s_unit->>'f6' as s_buy_price,s_unit->>'f7' as s_cost_price_spec,s_unit ->> 'f8' as s_weight,
                                                            m_unit->>'f1' as m_unit_no,m_unit->>'f2' as m_unit_factor,m_unit->>'f3' as m_barcode,m_unit->>'f4' as m_wholesale_price,m_unit->>'f5' as m_retail_price,m_unit->>'f6' as m_buy_price,m_unit->>'f7' as m_cost_price_spec,m_unit ->> 'f8' as m_weight,
                                                            b_unit->>'f1' as b_unit_no,b_unit->>'f2' as b_unit_factor,b_unit->>'f3' as b_barcode,b_unit->>'f4' as b_wholesale_price,b_unit->>'f5' as b_retail_price,b_unit->>'f6' as b_buy_price,b_unit->>'f7' as b_cost_price_spec,b_unit ->> 'f8' as b_weight from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor,barcode,wholesale_price,retail_price,buy_price,cost_price_spec,weight)) as json 
                                        from info_item_multi_unit where company_id=~company_id order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)) mu on ip.item_id=mu.item_id
                                
                                   left join (select batch_id,COALESCE(batch_no,'') as batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) as produce_date from info_item_batch where company_id= ~company_id) itb on itb.batch_id = t.batch_id
                                   left join (select branch_id as from_branch_id,branch_position as from_branch_position ,branch_position_name as from_branch_position_name from info_branch_position where company_id = ~company_id) fibp on fibp.from_branch_id = m.from_branch_id and fibp.from_branch_position = COALESCE(t.from_branch_position,0)
                                   left join (select branch_id as to_branch_id,branch_position as to_branch_position ,branch_position_name as to_branch_position_name from info_branch_position where company_id = ~company_id) tibp on tibp.to_branch_id = m.to_branch_id and tibp.to_branch_position = COALESCE(t.to_branch_position,0)

                  ";
            }
            else if (loadPurpose == LOAD_PURPOSE.APPROVE)
                DetailLeftJoin = @$" left join sheet_move_main m on t.sheet_id=m.sheet_id and m.company_id=~company_id
                                     left join (select company_id,cost_price_type from company_setting where company_id=~company_id) s on s.company_id = t.company_id
                                      left join (select item_id,company_id,branch_id,stock_qty as from_stock_qty, batch_id,branch_position from stock where company_id=~company_id) fs on t.item_id = fs.item_id and t.company_id = fs.company_id and m.from_branch_id = fs.branch_id and fs.batch_id = COALESCE(t.batch_id,0) and fs.branch_position = COALESCE(t.from_branch_position,0)
                                   left join (select item_id,company_id,branch_id,stock_qty as to_stock_qty ,batch_id,branch_position from stock where company_id=~company_id) ts on t.item_id = ts.item_id and t.company_id = ts.company_id and m.to_branch_id = ts.branch_id  and ts.batch_id =COALESCE(t.batch_id,0) and ts.branch_position = COALESCE(t.to_branch_position,0)
 left join info_item_prop ip on t.item_id=ip.item_id and ip.company_id=~company_id
left join (select batch_id,COALESCE(batch_no,'') as batch_no,SUBSTRING(COALESCE(produce_date::text,''),1,10) as produce_date from info_item_batch where company_id= ~company_id) itb on itb.batch_id = t.batch_id
left join (select item_id,s_unit,m_unit,b_unit from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor,barcode,wholesale_price,buy_price)) as json 
                                        from info_item_multi_unit where company_id=~company_id order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)) mu on ip.item_id=mu.item_id
         ";
        }

        public SheetMove(LOAD_PURPOSE loadPurpose) : base("sheet_move_main", "sheet_move_detail", loadPurpose)
        {
            sheet_type = SHEET_TYPE.SHEET_MOVE_STORE;
            ConstructFun(loadPurpose);


        }
        public SheetMove() : base("sheet_move_main", "sheet_move_detail", LOAD_PURPOSE.SHOW)
        {
            ConstructFun(LOAD_PURPOSE.SHOW);
        }
        protected class CInfoForApprove : CInfoForApproveBase
        {
            public string ArrearBalance = "", PrepayBalance = "";
            public List<SheetRowMove> SheetRows = null;
            public bool RoleAllowNegativeStock = true;
            public bool FromBranchAllowNegativeStock = true;
            public string FromNegativeStockAccordance = "real";
            public bool ToBranchAllowNegativeStock = true;
            public string ToNegativeStockAccordance = "real";           
        }
	 
       
        protected override string GetSQLForTemplates(string companyID, string mainTable, string sheetIDs)
        {
            string sql =
            @$"select m.sheet_id,st.setting->>'companyName' as company_name,st.setting->>'contactTel' as company_tel,st.setting->>'companyAddress' as company_address, t.template_id,t.template_name, c.client_group_id,c.client_id from {mainTable} m 
                left join print_template t on t.sheet_type='DB' and t.company_id={companyID} 
                left join print_template_choose c on c.company_id={companyID} and t.template_id=c.template_id
                left join print_template_avail_elements pa on pa.sheet_type ='DB'
                left join company_setting st on m.company_id=st.company_id
                where m.company_id={companyID} and m.sheet_id in ({sheetIDs}) order by case when c.client_id is null then -1 else c.client_id end desc, case when c.client_group_id is null then -1 else c.client_group_id end desc, c.template_id, order_index;
                ";

            return sql;
        }

        public async override Task<string> RedAndChange<TSheet>(CMySbCommand cmd, bool bAutoCommit = true)
        {

            //调拨单红冲前需要做检查
            string sql = $"select sheet_attribute->>'saleOrderSheetIDs' order_sheet_ids , sheet_attribute->>'assignVan' assign_van_type from sheet_move_main where company_id = {company_id} and sheet_id = {old_sheet_id} and (sheet_attribute->>'saleOrderSheetIDs' is not null  or sheet_attribute->>'assignVan' is not null)  and red_flag is  null";

            dynamic records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            string msg = "";
            if (records.Count > 0)
            {
                foreach (dynamic record in records)
                {
                    if (record.assign_van_type == "assign") msg = "访单调拨需要在装车历史里进行撤销装车";
                    if (record.assign_van_type == "back") msg = "访单调拨需要在手机端里进行撤销回库";

                }
            }
            if (msg != "") return msg;
            return await base.RedAndChange<TSheet>(cmd);
        }
        protected override void InitForSave()
        {
            base.InitForSave();
            if (seller_id == "") seller_id = OperID;


        }
        protected override void GetInfoForSave_SetQQ(SQLQueue QQ)
        {
			if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
			string items_id = "";
			 
			foreach (SheetRowMove row in SheetRows)
			{
				if (items_id != "") items_id += ",";
				items_id += row.item_id;
			}
			if (items_id.IsValid() && this.red_flag != "2")
			{
				string sql = @$"
SELECT ip.item_name,ip.son_mum_item, attr.remember_price,units.* FROM info_item_prop ip 
LEFT JOIN 
(
      select item_id,s_unit->>'f1' as s_unit_no,s_unit->>'f2' as s_unit_factor,s_unit->>'f3' as s_barcode,s_unit->>'f4' as s_retail_price,s_unit->>'f5' as s_lowest_price,s_unit->>'f6' as s_weight,s_unit->>'f7' as s_volume,
                     m_unit->>'f1' as m_unit_no,m_unit->>'f2' as m_unit_factor,m_unit->>'f3' as m_barcode,m_unit->>'f4' as m_retail_price,m_unit->>'f5' as m_lowest_price,m_unit->>'f6' as m_weight,m_unit->>'f7' as m_volume,
                     b_unit->>'f1' as b_unit_no,b_unit->>'f2' as b_unit_factor,b_unit->>'f3' as b_barcode,b_unit->>'f4' as b_retail_price,b_unit->>'f5' as b_lowest_price,b_unit->>'f6' as b_weight,b_unit->>'f7' as b_volume
      from crosstab('select item_id,unit_type,row_to_json(row(unit_no,unit_factor,barcode,retail_price,lowest_price,weight,volume)) as json from info_item_multi_unit where company_id={company_id} order by item_id',$$values ('s'::text),('m'::text),('b'::text)$$) as errr(item_id int, s_unit jsonb,m_unit jsonb, b_unit jsonb)
)
units on ip.item_id=units.item_id 
LEFT JOIN info_item_prop mum on ip.son_mum_item = mum.item_id and mum.company_id = {company_id}
LEFT JOIN info_attribute attr on (mum.mum_attributes->0->>'attrID')::integer=attr.attr_id and attr.company_id = {company_id}
WHERE ip.company_id = {company_id} and ip.item_id in ({items_id})";
				QQ.Enqueue("checkItems", sql);
			}

		}

		protected override void GetInfoForApprove_SetQQ(SQLQueue QQ)
        {
            string sql;
            base.GetInfoForApprove_SetQQ(QQ);
            if (OperID != "")
            {
                sql = $"select rights->'delicacy'->'allowNegativeStock'->'value' role_allow_negative_stock from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={company_id} and oper_id={OperID}";
                QQ.Enqueue("role_allow_negative_stock", sql);

                // sql = $"select rights->'delicacy' delicacy from info_operator o left join info_role r on r.role_id= o.role_id where o.company_id={company_id} and oper_id={OperID}";
                // QQ.Enqueue("delicacy", sql);
            }

            if (from_branch_id != "")
            {
                sql = $"select branch_id,allow_negative_stock branch_allow_negative_stock,negative_stock_accordance from info_branch where company_id = {company_id} and branch_id in ({from_branch_id},{to_branch_id})";
                QQ.Enqueue("branch_allow_negative_stock", sql);

            }
            if (SaleOrderSheetIDs != "")
            {
                sql = $@"select item_id,branch_id,branch_position,batch_id,sum(qty) as qty from (
    select item_id,COALESCE(d.branch_id,m.branch_id) as branch_id,coalesce(branch_position,'0') as branch_position,COALESCE(batch_id,'0') as batch_id,sum(quantity*unit_factor) qty from sheet_sale_order_detail d 
    left join sheet_sale_order_main m on m.sheet_id = d.sheet_id and m.company_id = {company_id}
    left join sheet_placeholder_order_main pm on pm.sale_order_sheet_id = m.sheet_id and pm.company_id = {company_id}
    where d.company_id = {company_id} and pm.sale_order_sheet_id is null and COALESCE(d.branch_id,m.branch_id) = {from_branch_id} and m.sheet_id in ({SaleOrderSheetIDs}) and m.red_flag is null and m.approve_time is not null and quantity>0 group by item_id,m.branch_id,d.branch_id,branch_position,batch_id
    union all
    select item_id,COALESCE(d.branch_id,pm.branch_id) as branch_id,coalesce(branch_position,'0') as branch_position,COALESCE(batch_id,'0') as batch_id,sum(quantity*unit_factor) qty from sheet_placeholder_order_detail d 
    left join sheet_placeholder_order_main pm on pm.sheet_id = d.sheet_id and pm.company_id = {company_id}
    left join sheet_sale_order_main m on m.sheet_id = pm.sale_order_sheet_id and m.company_id = {company_id}
    where d.company_id = {company_id} and pm.sale_order_sheet_id is not null and COALESCE(d.branch_id,pm.branch_id) = {from_branch_id} and m.sheet_id in ({SaleOrderSheetIDs}) and m.red_flag is null and m.approve_time is not null and pm.red_flag is null and pm.approve_time is not null and quantity>0 group by item_id,d.branch_id,branch_position,batch_id,pm.branch_id
)t
group by item_id,branch_id,branch_position,batch_id
";
                QQ.Enqueue("sale_order_qty", sql);
            }
            if (SheetRows.Count > 0)
            {
                string items_id = "";
                string batchs_id = "";
                string from_branchs_position = "";
                string to_branchs_position = "";
                foreach (SheetRowMove row in SheetRows)
                {
                    if (items_id != "") items_id += ",";
                    items_id += row.item_id;
                    if (batchs_id != "") batchs_id += ",";
                    batchs_id += row.batch_id;
                    if (from_branchs_position != "") from_branchs_position += ",";
                    from_branchs_position += row.from_branch_position;
                    if (to_branchs_position != "") to_branchs_position += ",";
                    to_branchs_position += row.to_branch_position;
                }
                if (batchs_id == "")
                {
                    batchs_id = "0";
                }
                if (from_branchs_position == "")
                {
                    from_branchs_position = "0";
                }
                if (to_branchs_position == "")
                {
                    to_branchs_position = "0";
                }


                sql = $@"select branch_id,item_id,stock_qty,sell_pend_qty,batch_id,branch_position 
from stock 
where company_id={company_id} and item_id in ({items_id}) and batch_id in ({batchs_id}) 
and ((branch_id = {from_branch_id} and branch_position in ({from_branchs_position})) or (branch_id = {to_branch_id} and branch_position in ({to_branchs_position})))";
                QQ.Enqueue("stock", sql);
                sql = $@"select mu.item_id,mu.unit_no as item_unit_no,mu.unit_factor item_unit_factor,mu.buy_price,p.cost_price_avg,mu.cost_price_spec,mu.wholesale_price,mu.contract_price from info_item_multi_unit mu left join info_item_prop p on p.item_id=mu.item_id where mu.company_id =  {company_id} and mu.item_id in ({items_id}) and unit_type='s';";
                QQ.Enqueue("costPrice", sql);

                
            }
            if(to_branch_id.IsValid())
            {
                sql = $@"select relate_client from info_branch where company_id = {company_id} and branch_id = {to_branch_id};";
                QQ.Enqueue("relate_client", sql);
            }

            //if (to_branch_id.IsValid() && this.limitMaxVanStockOnMove == "true")
            //{
            //    sql = $@"select isa.item_id,item_name,stock_qty,threshold_overload,branch_type from info_stock_alert as isa
            //          left join( select item_id, sum(stock_qty) stock_qty from stock where company_id = {company_id} and branch_id={to_branch_id} group by item_id ) stock on isa.item_id = stock.item_id 
            //          left join (select item_id,item_name from info_item_prop where company_id = {company_id})ip on ip.item_id=isa.item_id 
            //          LEFT JOIN (select branch_id,branch_type from info_branch where company_id ={company_id})b on  b.branch_id = isa.branch_id
            //          where company_id = {company_id} and isa.branch_id={to_branch_id} and b.branch_type='truck'";
            //    QQ.Enqueue("limit_van_stock_on_move", sql);
            //}
        }

       
        protected override void GetInfoForSave_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            var info = InfoForApprove;

			base.GetInfoForSave_ReadData(dr, sqlName, bForRed);
             if (sqlName == "checkItems")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                if (records.Count == 0)
                {
                    if (this.SheetRows.Count > 0) info.ErrMsg = "单据商品行的商品不存在,请检查";
                }
                else
                {
                    foreach (var row in this.SheetRows)
                    {
                        var hasItem = false;
                        var itemNoExist = false;
                        var errorItemUnitFactor = false;
                        foreach (dynamic rec in records)
                        {

                            if (row.item_id == rec.item_id)
                            {
                                hasItem = true;
                                row.b_unit_no = rec.b_unit_no;
                                row.b_unit_factor = rec.b_unit_factor;
                                row.m_unit_no = rec.m_unit_no;
                                row.m_unit_factor = rec.m_unit_factor;
                                row.s_unit_no = rec.s_unit_no;
                                if (row.item_name == "") row.item_name = rec.item_name;
                                row.son_mum_item = rec.son_mum_item;
                                if (row.son_mum_item != "" && ((string)rec.remember_price).ToLower() == "true")
                                {
                                    row.attrRememberPrice = true;
                                }
                                if (rec.b_unit_no != "" && row.unit_no == rec.b_unit_no)
                                {
                                    itemNoExist = true;
                                    if (CPubVars.FormatMoney(row.unit_factor,2) != CPubVars.FormatMoney(rec.b_unit_factor,2))
                                    {
                                        errorItemUnitFactor = true;
                                    }

                                }
                                if (rec.m_unit_no != "" && row.unit_no == rec.m_unit_no)
                                {
                                    itemNoExist = true;
                                    if (CPubVars.FormatMoney(row.unit_factor, 2) != CPubVars.FormatMoney(rec.m_unit_factor,2))
                                    {
                                        errorItemUnitFactor = true;
                                    }

                                }
								if (rec.s_unit_no != "" && row.unit_no == rec.s_unit_no)
								{
									itemNoExist = true;

								}

							}
                        }
                        if (!hasItem)
                        {
                            info.ErrMsg = $"第{row.row_index}行商品{row.item_name},商品档案里不存在,请检查";
                        }
                        if (!itemNoExist)
                        {
                            info.ErrMsg = $"第{row.row_index}行商品{row.item_name}单位{row.unit_no},商品档案里不存在,请检查";
                        }
                        if (errorItemUnitFactor)
                        {
                            info.ErrMsg = $"第{row.row_index}行商品{row.item_name}单位{row.unit_no}的包装率与商品档案里不一致,请检查";
                        }
                    }
                }
            }
			//if (sqlName == "checkItems")
			//{
			//	List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
			//	if (records.Count == 0)
			//	{
			//		if (this.SheetRows.Count > 0) info.ErrMsg = "单据商品行的商品不存在,请检查";
			//	}
			//	else
			//	{
			//		foreach (var row in this.SheetRows)
			//		{
			//			var hasItem = false;
			//			var itemNoExist = false;
			//			var errorItemUnitFactor = false;
			//			foreach (dynamic rec in records)
			//			{
			//				if (row.item_id == rec.item_id)
			//				{
			//					hasItem = true;
			//					if (rec.b_unit_no != "" && row.unit_no == rec.b_unit_no)
			//					{
			//						itemNoExist = true;
			//						if (Convert.ToDecimal(row.unit_factor) != Convert.ToDecimal(rec.b_unit_factor))
			//						{
			//							errorItemUnitFactor = true;
			//						}
			//					}
			//					if (rec.m_unit_no != "" && row.unit_no == rec.m_unit_no)
			//					{
			//						itemNoExist = true;
			//						if (Convert.ToDecimal(row.unit_factor) != Convert.ToDecimal(rec.m_unit_factor))
			//						{
			//							errorItemUnitFactor = true;
			//						}
			//					}
			//					if (rec.s_unit_no != "" && row.unit_no == rec.s_unit_no)
			//					{
			//						itemNoExist = true;
			//						if (Convert.ToDecimal(row.unit_factor) != Convert.ToDecimal(rec.s_unit_factor))
			//						{
			//							errorItemUnitFactor = true;
			//						}
			//					}
			//				}
			//			}
			//			if (!hasItem)
			//			{
			//				info.ErrMsg = $"第{row.row_index}行商品{row.item_name},商品档案里不存在,请检查";
			//			}
			//			if (!itemNoExist)
			//			{
			//				info.ErrMsg = $"第{row.row_index}行商品{row.item_name}单位{row.unit_no},商品档案里不存在,请检查";
			//			}
			//			if (errorItemUnitFactor)
			//			{
			//				info.ErrMsg = $"第{row.row_index}行商品{row.item_name}单位{row.unit_no}的包装率与商品档案里不一致,请检查";
			//			}
			//		}
			//	}
			//}
		}

        protected override void GetInfoForApprove_ReadData(CMySbDataReader dr, string sqlName, bool bForRed = false)
        {
            if (InfoForApprove == null) InfoForApprove = new CInfoForApprove();
            CInfoForApprove info = (CInfoForApprove)InfoForApprove;

            base.GetInfoForApprove_ReadData(dr, sqlName, bForRed);
            //dynamic re=await limitVanStockOnMove();
            //if (re.result == false)
            //{
            //    info.ErrMsg += re.msg;      
            //}
            if (sqlName == "role_allow_negative_stock")
            {
                dynamic roleAllowNegativeStock = CDbDealer.Get1RecordFromDr(dr);
                if (roleAllowNegativeStock != null)
                {
                    string r = roleAllowNegativeStock.role_allow_negative_stock;
                    if (r.ToLower() == "false") info.RoleAllowNegativeStock = false;
                }
            }
           /* if (sqlName == "delicacy")
            {
                dynamic rec = CDbDealer.Get1RecordFromDr(dr);
                if (rec != null)
                {
                    string s = rec.delicacy;
                    if (s != "")
                    {
                        dynamic delicacy = JsonConvert.DeserializeObject(s);
                        if (delicacy.allowNegativeStock != null && delicacy.allowNegativeStock.value!=null)
                        {
                            if(delicacy.allowNegativeStock.value.ToString()=="false") info.RoleAllowNegativeStock = false;
                        }
                        if (delicacy.allowNegativeStock != null && delicacy.allowNegativeStock.value != null)
                        {
                            if (delicacy.allowNegativeStock.value.ToString() == "false") info.RoleAllowNegativeStock = false;
                        }
                    }
                    
                }
            }*/
 
            else if (sqlName == "branch_allow_negative_stock")
            {
                List<ExpandoObject> lstBranchInfo = CDbDealer.GetRecordsFromDr(dr);
                if (lstBranchInfo != null)
                {
                    foreach (dynamic branch in lstBranchInfo)
                    {
                        if (branch.branch_id == from_branch_id)
                        {
                            string b = branch.branch_allow_negative_stock;
                            if (b.ToLower() == "false") info.FromBranchAllowNegativeStock = false;
                            info.FromNegativeStockAccordance = branch.negative_stock_accordance;
                        }
                        else if (branch.branch_id == to_branch_id)
                        {
                            string b = branch.branch_allow_negative_stock;
                            if (b.ToLower() == "false") info.ToBranchAllowNegativeStock = false;
                            info.ToNegativeStockAccordance = branch.negative_stock_accordance;
                        }

                    }

                }
            }
            
            else if (sqlName == "sale_order_qty")  // 订单转单 的 销售单，找出原单据的数量，判断可用库存数量 
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowMove row in SheetRows)
                    {
                        if (rec.item_id != "" && row.item_id == rec.item_id && from_branch_id == rec.branch_id && row.branch_position == rec.branch_position && row.batch_id == rec.batch_id)
                        {
                            row.SaleOrderQty += CPubVars.ToDecimal(rec.qty == "" ? 0 : rec.qty);
                        }
                    }
                }
            }
            
            else if (sqlName == "stock")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                foreach (SheetRowMove row in SheetRows)
                {
                    row.HasFromStockQty = false;//前端会上传这两个属性
                    row.HasToStockQty = false;
                }

                foreach (dynamic rec in records)
                {
                    foreach (SheetRowMove row in SheetRows)
                    {
                        if (rec.item_id != "" && row.item_id == rec.item_id && row.batch_id == rec.batch_id)
                        {
                            if (rec.stock_qty != "")
                            {
                                if ((string)rec.branch_id == from_branch_id && (string)rec.branch_position == row.from_branch_position)
                                {
                                    row.HasFromStockQty = true;
                                    row.FromStockQty = CPubVars.ToDecimal(rec.stock_qty);
                                    row.FromSellPendQty = CPubVars.ToDecimal(rec.sell_pend_qty);
                                }
                                else if ((string)rec.branch_id == to_branch_id && (string)rec.branch_position == row.to_branch_position)
                                {
                                    row.HasToStockQty = true;
                                    row.ToStockQty = CPubVars.ToDecimal(rec.stock_qty);
                                    row.ToSellPendQty = CPubVars.ToDecimal(rec.sell_pend_qty);
                                }
                            }
                        }
                    }
                }
                info.SheetRows = SheetRows;

                //this.MergedSheetRows = MergeSheetRows(this.SheetRows);
                this.MergedSheetRowByBatchAndItem = MergeSheetRowsByBatchAndItem(this.SheetRows);


                int rowIndex = 1;
                foreach (var row in this.MergedSheetRowByBatchAndItem)
                {
                    #region 判断冲改时逻辑
                    // * 关于 bIgnoreCheckForRedChangeSameItem
                    // 为 true :
                    // 代表正在审查冲改单的被红冲单据，并且这个商品在新单据商品行中也存在
                    // 为 false :
                    // 代表其他情况，包括但不限于 正常审核、正常红冲、冲改单的新单据
                    // 以及正在审查冲改单的被红冲单据，并且这个商品在新单据商品行中并不存在
                    // * 另注：红冲时，没有必要判断出仓负库存
                    bool bIgnoreCheckForRedChangeSameItem = false;
                    if (bForRed && isRedAndChange&&this.RedChangeSheet!=null)
                    {
                        foreach (var newRow in this.RedChangeSheet.SheetRows)
                        {
                            if (
                                row.item_id == newRow.item_id
                                && row.batch_id == newRow.batch_id
                                && row.branch_id == newRow.branch_id
                                && row.branch_position == newRow.branch_position
                            ) // 说明新单据里面有这个旧单据商品，那么就不要校验这个商品的负库存
                            {
                                bIgnoreCheckForRedChangeSameItem = true;
                                break;
                            }
                        }
                    } 
                    #endregion

                    #region 判断出仓负库存
                    decimal newFromStockQty = 0;
                    string fromBranchAccordance = "实际";
                    if (info.FromNegativeStockAccordance == "real")
                    {
                        if(this.red_flag!="2")
                          newFromStockQty = row.FromStockQty - row.quantity;
                    }
                    else
                    {
                        fromBranchAccordance = "可用";
                        if (assign_van == Assign_Van.ASSIGN_VAN)
                        {

                            if (red_flag == "2")
                            {
                                if (row.SaleOrderQty > row.quantity)//当调拨数量少于订单数量时，可能会造成fromBranch的可用库存减少
                                {
                                    decimal newSellPendQty = row.FromSellPendQty + row.SaleOrderQty;
                                    newFromStockQty = row.FromStockQty + row.quantity - newSellPendQty;
                                }
                            }
                            else
                            {
                                decimal newSellPendQty = row.FromSellPendQty - row.SaleOrderQty;
                                newFromStockQty = row.FromStockQty - row.quantity - newSellPendQty;
                            }
                        }
                        else
                        {
							if (red_flag != "2")
                            {
								newFromStockQty = row.FromStockQty - row.FromSellPendQty - row.quantity;
							}
                        }
                    }

                    if ((!info.RoleAllowNegativeStock || !info.FromBranchAllowNegativeStock) && !(isRedAndChange && bForRed))
                    {
                        if (newFromStockQty < -0.01m)
                        {
                            info.ErrMsg = $"{row.item_name} 在 {from_branch_name} 出现负{fromBranchAccordance}库存";
                            if (red_flag == "2") info.ErrMsg += ",红冲失败";
                            else info.ErrMsg += ",审核失败";
                            break;
                        }
                    }

                    #endregion

                    #region 判断入仓负库存
                    if (red_flag == "2" || isRedAndChange)
                    {
                        decimal newToStockQty = 0;
                        string toBranchAccordance = "实际";
                        if (isRedAndChange && !bForRed)
                        {
                            if (info.ToNegativeStockAccordance == "real")
                            {
                                newToStockQty = row.ToStockQty + row.quantity;
                            }
                            else
                            {
                                toBranchAccordance = "可用";
                                newToStockQty = row.ToStockQty + row.quantity - row.ToSellPendQty;
                            }
                        }
                        else
                        {
                            if (info.ToNegativeStockAccordance == "real")
                            {
                                newToStockQty = row.ToStockQty - row.quantity;
                            }
                            else
                            {
                                toBranchAccordance = "可用";
                                newToStockQty = row.ToStockQty - row.quantity - row.ToSellPendQty;
                            }
                        }


                        if ((!info.RoleAllowNegativeStock || !info.ToBranchAllowNegativeStock) && !bIgnoreCheckForRedChangeSameItem)
                        {
                            if (newToStockQty < -0.01m)
                            {
                                info.ErrMsg = $"{row.item_name}在{to_branch_name} 出现负{toBranchAccordance}库存";
                                if (red_flag == "2") info.ErrMsg += ",红冲失败";
                                else info.ErrMsg += ",审核失败";
                                break;
                            }
                        }
                    }

                    #endregion

                    rowIndex++;
                }

            }
            else if(sqlName == "relate_client")
            {
                dynamic records = CDbDealer.GetRecordsFromDr(dr, false);
                
                if(records.Count > 0)
                {
                    relate_client = records[0].relate_client;
                }
                
            }

            else if (sqlName == "costPrice")
            {
                List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
                decimal wholesaleAmount = 0, avgAmount = 0 , propAmount=0, buyAmount=0; 
                foreach (dynamic rec in records)
                {
                    foreach (SheetRowMove row in SheetRows)
                    {
                        if (row.item_id == rec.item_id)
                        {
                            if (row.buy_price.IsInvalid() && rec.buy_price != "" && rec.buy_price != null)
                            {
                                row.buy_price = Convert.ToString(CPubVars.ToDecimal(rec.buy_price));
                            }
                            if (row.buy_price.IsValid())
                            {
                                buyAmount += CPubVars.ToDecimal(row.buy_price) * row.unit_factor * row.quantity;
                            }
                            if (row.cost_price_avg.IsInvalid() && rec.cost_price_avg != "" && rec.cost_price_avg != null)
                            {
                                row.cost_price_avg = Convert.ToString(Decimal.Parse(rec.cost_price_avg, System.Globalization.NumberStyles.Float));
                            }
                            if (row.cost_price_avg.IsValid())
                            {
                                avgAmount += CPubVars.ToDecimal(row.cost_price_avg) * row.unit_factor * row.quantity;
                            }

                            if (row.cost_price_prop.IsInvalid() && rec.cost_price_spec != "" && rec.cost_price_spec != null)
                            {
                                row.cost_price_prop = rec.cost_price_spec;
                            }
                            if (row.cost_price_prop.IsValid())
                            {
                                propAmount += CPubVars.ToDecimal(row.cost_price_prop) * row.unit_factor * row.quantity;
                            }
                            if (row.wholesale_price.IsInvalid() && rec.wholesale_price != "" && rec.wholesale_price != null)
                            {
                                row.wholesale_price = Convert.ToString(CPubVars.ToDecimal(rec.wholesale_price) * row.unit_factor);

                            }
                            if (row.wholesale_price.IsValid())
                            {
                                wholesaleAmount += CPubVars.ToDecimal(row.wholesale_price) * row.quantity;
                            }

                        }
                    }

                }
                wholesale_amount = Math.Round(wholesaleAmount, 2);
                cost_amount_avg = Math.Round(avgAmount,2);
                cost_amount_prop = Math.Round(propAmount,2);
                buy_amount = Math.Round(buyAmount,2);
            }
            //else if(sqlName == "limit_van_stock_on_move")
            //{
            //    List<ExpandoObject> records = CDbDealer.GetRecordsFromDr(dr, false);
            //    if (records.Count() > 0)
            //    {
            //        List<SheetRowMove> sheetRows = JsonConvert.DeserializeObject<List<SheetRowMove>>(JsonConvert.SerializeObject(this.SheetRows));
            //        Dictionary<string, decimal> itemQuantityDict = new Dictionary<string, decimal>();
            //        sheetRows = MergeSheetRows(sheetRows);
            //        foreach (SheetRowMove sheetRow in sheetRows)
            //        {
            //            itemQuantityDict[sheetRow.item_id] = sheetRow.quantity;
            //        }
            //        foreach (dynamic rec in records)
            //        {

            //            if (itemQuantityDict.ContainsKey(rec.item_id))
            //            {
            //                decimal quantity = itemQuantityDict[rec.item_id];
            //                decimal stockQty = string.IsNullOrEmpty(rec.stock_qty) ? 0 : CPubVars.ToDecimal(rec.stock_qty);
            //                if (stockQty + quantity > CPubVars.ToDecimal(rec.threshold_overload))
            //                {
            //                    info.ErrMsg = $"审核失败,{rec.item_name}在{to_branch_name}已达到车辆积压上限，请减少调拨数量";
            //                }
            //            }
            //        }
            //    }
            //}
        }

		public override string GetSheetCharactor()
		{
			string res = this.company_id + "_" + this.OperID + "_" + this.from_branch_id + "_" + this.to_branch_id + "_" + this.make_brief;
			foreach (var row in SheetRows)
			{
				res += row.item_id + "_" + row.item_name + "_" + row.quantity;
			}
			return res;
		}
		public override string GetDataLockKey()
		{
			return this.company_id + "_" + this.from_branch_id;
		}

		protected override async Task<string> CheckSaveSheetValid(CMySbCommand cmd)
        {
            var check = await base.CheckSaveSheetValid(cmd);
            if (check != "OK") return check;
            decimal wholeSaleAmount = 0, contractAmount = 0;
            foreach (var row in SheetRows)
            {
                wholeSaleAmount +=CPubVars.ToDecimal( row.wholesale_sub_amount==""?"0":row.wholesale_sub_amount);
                contractAmount += CPubVars.ToDecimal(row.contract_sub_amount == "" ? "0" : row.contract_sub_amount);

            }
            wholesale_amount = wholeSaleAmount;

           // if (Math.Abs(wholeSaleAmount - wholesale_amount) > 1m) return "总批发金额与明细行批发金额合计不相等";
            if (Math.Abs(contractAmount - contract_amount) > 1m) return $"总承包金额{contract_amount}与明细行承保金额合计{contractAmount}不相等";

            string msg = "";
            foreach (dynamic row in this.SheetRows)
            {
                string from_branch_position = row.from_branch_position;
                string to_branch_position = row.to_branch_position;
                if (row.from_branch_position != "0" && !from_branch_position.IsInvalid() && from_branch_position != "null")
                {
                    dynamic record = await CDbDealer.Get1RecordFromSQLAsync($"select branch_position from info_branch_position where company_id = {company_id} and branch_position = {from_branch_position} and branch_id = {from_branch_id};", cmd);
                    if (record == null)
                    {
                        msg = $"{from_branch_name} 不存在库位：{row.from_branch_position_name} id:{from_branch_position}";
                        break;
                    }
                }

                if (row.to_branch_position != "0" && !to_branch_position.IsInvalid() && to_branch_position != "null")
                {
                    dynamic record = await CDbDealer.Get1RecordFromSQLAsync($"select branch_position from info_branch_position where company_id = {company_id} and branch_position = {to_branch_position} and branch_id = {to_branch_id};", cmd);
                    if (record == null)
                    {
                        //msg = "ERROR";
                        msg = $"{to_branch_name}不存在库位：{row.to_branch_position_name}";
                        break;
                    }
                }
            }
            
            msg =await CheckBatch(cmd);
            if (msg != "") return msg;

            return "OK";
        }
        protected override async Task<string> CheckSheetValid(CMySbCommand cmd = null)
        {
            var check = await base.CheckSheetValid(cmd);
            if (check != "OK") return check;
 
            if (from_branch_id == "") return "必须指定出仓";
            if (to_branch_id == "") return "必须指定入仓";
            if (seller_id == "" && IsFromWeb) return "必须指定业务员";
            if (SheetRows.Count == 0) return "必须指定商品行";
            if (from_branch_id == to_branch_id) return "出仓与入仓不能相同";

            return "OK";
        }

    
        
        protected override string GetApproveSQL(CInfoForApproveBase info1)
        {
            if (IsImported) return "";
            CInfoForApprove info = (CInfoForApprove)info1;
            string sql = "";
            int inoutFlag = -1;
            if (red_flag == "2") inoutFlag *= -1;

            foreach (SheetRowMove row in info.SheetRows)
            {
                string s = "";
                string changeQty = "";

                decimal qty;
                //compute from branch stock qty
                qty = inoutFlag * row.quantity * row.unit_factor;
                changeQty = qty.ToString();
                if (changeQty == "-0") changeQty = "0";
                if (qty >= 0)
                {
                    changeQty = "+" + qty.ToString();
                }

                if (string.IsNullOrEmpty(row.batch_id)) row.batch_id = "0";

                if (row.HasFromStockQty)
                {
                    s = $"update stock set stock_qty=stock_qty{changeQty} where company_id={company_id} and branch_id={from_branch_id} and item_id={row.item_id} and batch_id = {row.batch_id} and branch_position = {row.from_branch_position};";
                }
                else
                {
                    s = $"insert into stock(company_id,branch_id,item_id,stock_qty,batch_id,branch_position) values ({company_id},{from_branch_id},{row.item_id},{qty},{row.batch_id},{row.from_branch_position}) on conflict (company_id,branch_id,item_id,batch_id,branch_position) do update set stock_qty=stock.stock_qty{changeQty};";
                }
                sql += s;
                //row.NewStockQty = row.FromStockQty + qty;
                //compute to branch stock qty
                qty = -1 * inoutFlag * row.quantity * row.unit_factor;
                changeQty = qty.ToString();
                if (changeQty == "-0") changeQty = "0";
                if (qty >= 0)
                {
                    changeQty = "+" + qty.ToString();
                }
                if (string.IsNullOrEmpty(row.batch_id)) row.batch_id = "0";

                if (row.HasToStockQty)
                {
                    s = $"update stock set stock_qty=stock_qty{changeQty} where company_id={company_id} and branch_id={to_branch_id} and item_id={row.item_id} and batch_id = {row.batch_id} and branch_position = {row.to_branch_position};";
                }
                else
                {
                    s = $"insert into stock(company_id,branch_id,item_id,stock_qty,batch_id,branch_position) values ({company_id},{to_branch_id},{row.item_id},{qty},{row.batch_id},{row.to_branch_position}) on conflict (company_id,branch_id,item_id,batch_id,branch_position) do update set stock_qty=stock.stock_qty{changeQty};";
                }
				sql += s;

				#region 更新上次批发价
				if (relate_client.IsValid())
                {
					decimal recent_price = 0;

					if (row.contract_price.IsValid() && CPubVars.ToDecimal(row.contract_price) > 0)
					{
						recent_price = CPubVars.ToDecimal(row.contract_price) / row.unit_factor;
					}
					else if (row.wholesale_price.IsValid() && CPubVars.ToDecimal(row.wholesale_price) > 0)
					{
						recent_price = CPubVars.ToDecimal(row.wholesale_price) / row.unit_factor;
					}

					string update_price = "insert into client_recent_prices (company_id,supcust_id,item_id,unit_no,happen_time,recent_price) values";
                
                    
                    update_price +=       $@"({company_id},{relate_client},{row.item_id},'{row.s_unit_no}','{happen_time}',{recent_price}) ";
                    if(row.b_unit_no.IsValid())
                    {
                        update_price += $@" ,({company_id},{relate_client},{row.item_id},'{row.b_unit_no}','{happen_time}',{CPubVars.FormatMoney(recent_price * decimal.Parse(row.b_unit_factor),2)}) ";
                    }
                    if(row.m_unit_no.IsValid())
                    {
                        update_price += $@" ,({company_id},{relate_client},{row.item_id},'{row.m_unit_no}','{happen_time}',{CPubVars.FormatMoney(recent_price * decimal.Parse(row.m_unit_factor),2)}) ";
                    }
                    update_price += $@"on conflict(company_id,supcust_id,item_id,unit_no) do update set recent_price = EXCLUDED.recent_price,happen_time = '{happen_time}' where client_recent_prices.happen_time <= '{happen_time}';";
                    sql += update_price;
                }
                
                
         
                #endregion

                
                
            }
            #region update recent price
            //if (red_flag != "2")
            //{
            //    if (info.UnitPriceRows.Count() > 0)
            //    {
            //        foreach (SheetRowMove row in info.UnitPriceRows)
            //        {
            //            string origPrice = row.orig_price.IsValid() ? row.orig_price : "null";

            //            string recent_price = row.real_price.ToString();
            //            string recent_orig_price = origPrice;
            //            string recent_retail_price = row.recent_retail_price.IsValid() ? row.recent_retail_price : "null";
            //            // string recent_s_retail_price = row.s_retail_price.IsValid() ? row.s_retail_price : "null"; 
            //            sql += $@"insert into client_recent_prices (company_id,supcust_id,item_id,unit_no,recent_wholesale_price, happen_time) values ({company_id},{supcust_id},{row.item_id},'{row.unit_no}',{recent_wholesale_price},'{happen_time}') 
            //        on conflict(company_id,supcust_id,item_id,unit_no) do update set recent_wholesale_price={recent_wholesale_price},happen_time = '{happen_time}' where (client_recent_prices.happen_time is null or client_recent_prices.happen_time <= '{happen_time}');";

            //        }
            //    }

            //}
            #endregion
            return sql;
        }

        public override async Task OnSheetIDGot(CMySbCommand cmd, string sheetID, CInfoForApproveBase info1)
        {
            if (IsImported) return;

            string sql = "";
            int inoutFlag = -1;
            if (red_flag == "2") inoutFlag *= -1;
            foreach (var row in MergedSheetRowByBatchAndItem)
            {
                decimal qty = inoutFlag * row.quantity;
                decimal newStock = row.FromStockQty + qty;
                string batchId = row.batch_id==null?"0": row.batch_id;
                string s = $@"insert into stock_change_log 
        (company_id, approve_time,    branch_id,       item_id,    sheet_id,     pre_stock_qty, new_stock_qty,batch_id,branch_position ) 
values ({company_id},'{approve_time}',{from_branch_id},{row.item_id},{sheet_id},{row.FromStockQty},{newStock},{batchId},{row.from_branch_position});";
                sql += s;

                qty *= -1;
                newStock = row.ToStockQty + qty; 
                s = $@"insert into stock_change_log 
        (company_id, approve_time,    branch_id,       item_id,    sheet_id,   pre_stock_qty, new_stock_qty,batch_id,branch_position ) 
values ({company_id},'{approve_time}',{to_branch_id},{row.item_id},{sheet_id},{row.ToStockQty},{newStock},{batchId},{row.to_branch_position});";

                sql += s;
            }
            cmd.CommandText = sql;
            await cmd.ExecuteNonQueryAsync();
        }
        //  public List<SheetRowMove> MergeSheetRows(List<SheetRowMove> rows)
        public override async Task<string> OnSheetSaved(CMySbCommand cmd, string sheetID)
        {
            string operKey = this.OperKey;
            string operID = this.OperID;
            string maker_name = this.maker_name;
            string from_branch_name = this.from_branch_name;
            string from_branch_id = this.from_branch_id;
            string to_branch_name = this.to_branch_name;
            string to_branch_id = this.to_branch_id;
            string sheetNo = this.sheet_no;
            //  更新旧消息
            await MessageUpdateServices.UpdateDealMessageService(new
            {
                operKey,
                msgId = "",
                sheetID = sheetID,
                msgClass = MessageType.ClassType.Todo,
                msgType = MessageType.MoveSheetMessageType.MessageType,
                msgSubType = MessageType.MoveSheetMessageType.MessageSubType.MoveSheetApproveSubType.SubTypeKey
            }, cmd);
            //创建新的消息
            await MessageCreateServices.CreateMessageService(new
            {
                operKey,
                createrId = operID,
                msgClass = MessageType.ClassType.Todo,
                msgType = MessageType.MoveSheetMessageType.MessageType,
                msgSubType = MessageType.MoveSheetMessageType.MessageSubType.MoveSheetApproveSubType.SubTypeKey,
                receiverId = "",
                sheetID = sheetID,
                from_branch_id,
                to_branch_id,
                msgTitle = @$"{maker_name} 申请 {from_branch_name} 到 {to_branch_name} 调拨。 单号: {sheetNo}， 请尽快处理",
            }, cmd);
            return "";
        }
        public override List<SheetRowMove> MergeSheetRows(List<SheetRowMove> rows, bool bIgnoreNativeQty = false)  
        {
            Dictionary<string, SheetRowMove> rowsDict = new Dictionary<string, SheetRowMove>();
            foreach (SheetRowMove sheetRow in rows)
            {
                
                string skey = sheetRow.item_id;// +"_" + sheetRow.unit_factor.ToString();
                SheetRowMove curRow = null;
                rowsDict.TryGetValue(skey, out curRow);
                if (curRow == null)
                {
                    curRow = new SheetRowMove();
                    curRow.item_id = sheetRow.item_id;
                    curRow.item_name = sheetRow.item_name;
                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
                    curRow.unit_no = sheetRow.unit_no;
                    curRow.unit_factor = sheetRow.unit_factor;
                    curRow.inout_flag = sheetRow.inout_flag;
                    curRow.FromStockQty = sheetRow.FromStockQty;
                    curRow.SaleOrderQty = sheetRow.SaleOrderQty;
                    curRow.FromSellPendQty = sheetRow.FromSellPendQty;
                    curRow.ToStockQty = sheetRow.ToStockQty;
                    rowsDict.Add(skey, curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
                }
            }
            List<SheetRowMove> newList = new List<SheetRowMove>();
            foreach (var k in rowsDict)
            {
                newList.Add(k.Value);
            }
            return newList;

        }
        public List<SheetRowMove> MergeSheetRowsByBatchAndItem(List<SheetRowMove> rows)
        {
            List<SheetRowMove> rowsDict = new List<SheetRowMove>();

            foreach (SheetRowMove sheetRow in rows)
            {
                SheetRowMove curRow = null;
                foreach (SheetRowMove rowDic in rowsDict)
                {
                    if (sheetRow.item_id == rowDic.item_id && sheetRow.batch_id == rowDic.batch_id && sheetRow.from_branch_position == rowDic.from_branch_position && sheetRow.to_branch_position == rowDic.to_branch_position)
                    {
                        curRow = rowDic;
                        break;
                    }
                }
                if (curRow == null)
                {
                    string s = JsonConvert.SerializeObject(sheetRow);
                    curRow = JsonConvert.DeserializeObject<SheetRowMove>(s);
                    curRow.item_id = sheetRow.item_id;
                    curRow.item_name = sheetRow.item_name;
                    curRow.quantity = sheetRow.quantity * sheetRow.unit_factor;
                    curRow.unit_no = sheetRow.unit_no;
                    curRow.unit_factor = sheetRow.quantity;
                    curRow.inout_flag = sheetRow.inout_flag;
                    curRow.FromStockQty = sheetRow.FromStockQty;
                    curRow.batch_id = sheetRow.batch_id;
                    curRow.from_branch_position = sheetRow.from_branch_position;
                    curRow.to_branch_position = sheetRow.to_branch_position;
                    curRow.SaleOrderQty = sheetRow.SaleOrderQty;
                    curRow.FromSellPendQty = sheetRow.FromSellPendQty;
                    curRow.ToStockQty = sheetRow.ToStockQty;
                    rowsDict.Add(curRow);
                }
                else
                {
                    curRow.quantity += sheetRow.quantity * sheetRow.unit_factor;
                }
            }
            return rowsDict;

        }

        public override async Task LoadInfoForPrint(CMySbCommand cmd, bool smallUnitBarcode, bool bLoadCompanySetting = true, dynamic printTemplate = null)
        {
            string sql = "";
            await base.LoadInfoForPrint(cmd, smallUnitBarcode, bLoadCompanySetting);
            decimal b_qty = 0, m_qty = 0, s_qty = 0;
            foreach (var row in SheetRows)
            {
                row.SetInfoForPrint(smallUnitBarcode);
                b_qty += row.b_quantity;
                m_qty += row.m_quantity;
                s_qty += row.s_quantity;
            }
            string sumQty = "";
            if (b_qty != 0) sumQty += CPubVars.FormatMoney(b_qty, 2) + "大";
            if (m_qty != 0) sumQty += CPubVars.FormatMoney(m_qty, 2) + "中";
            if (s_qty != 0) sumQty += CPubVars.FormatMoney(s_qty, 2) + "小";
            this.sum_quantity_unit_conv = sumQty;
            this.sum_item_name = "合计: " + MoneyToUpper(this.wholesale_amount.ToString());
            if (printTemplate != null) {
                SQLQueue QQ = new SQLQueue(cmd);
                if (!(printTemplate is string))
                {
                    printTemplate = JsonConvert.SerializeObject(printTemplate);
                }
                //
                if (printTemplate.Contains("\"print_count\"")) {
                    string table = "";
                    if (sheet_type == SHEET_TYPE.SHEET_MOVE_STORE) {
                        table = "sheet_status_move";
                    }
                    if (table != "") { 
                        sql = $"select sheet_print_count from {table} where company_id = {company_id} and sheet_id = {sheet_id};";
                        QQ.Enqueue("print_count", sql);
                    }
                }
                if (QQ.Count > 0) {
                    CMySbDataReader dr = await QQ.ExecuteReaderAsync();
                    while (QQ.Count > 0) { 
                        string tbl = QQ.Dequeue();
                        if (tbl == "print_count") {
                            dynamic rec = CDbDealer.Get1RecordFromDr(dr, false);
                            if (rec != null)
                            {
                                this.print_count = rec.sheet_print_count;
                            }
                            if (this.print_count == "") this.print_count = "1";
                            
                        }
                    }
                }
                QQ.Clear();
            }

        }
        public override async Task<string> BeforeRedAndChange(CMySbCommand cmd)
        {
            return await CheckBatch(cmd);
        }
        public async Task<string> CheckBatch(CMySbCommand cmd ,bool bAutoCommit = true)
        {
            string msg = "";
            string insertSql = "";
            string insertValue = "";
            string selectSql = "";
            string selectValue = "";
            Dictionary<string, dynamic> batchDic = new Dictionary<string, dynamic>();
            Dictionary<string, string> sheetRowBatch = new Dictionary<string, string>();
            try
            {

                foreach (SheetRowMove row in SheetRows)
                {
                    if (row.produce_date.IsInvalid() || row.produce_date == "无产期")
                    {
                        continue;
                    }
                    string key = row.produce_date + row.batch_no;
                    if (!batchDic.ContainsKey(key))
                    {
                        batchDic[key] = new { produce_date = row.produce_date, batch_no = row.batch_no };
                        if(selectValue!="")  selectValue += ",";
                        selectValue += $@"('{row.produce_date}','{row.batch_no}')";
                    }
                }
                if (selectValue != "") selectSql = $@"select * from info_item_batch where company_id = {company_id} and (substring(produce_date::text,1,10),batch_no) in ({selectValue});";
                if (selectSql != "")
                {
                    List<ExpandoObject> selectRec = await CDbDealer.GetRecordsFromSQLAsync(selectSql, cmd);
                    foreach (dynamic row in selectRec)
                    {
                        string produceDate = row.produce_date;
                        string batchNo = row.batch_no;
                        produceDate = produceDate.Substring(0, 10);
                        string key = produceDate + batchNo;
                        sheetRowBatch.Add(key, row.batch_id);
                        if (batchDic.ContainsKey(key)) batchDic.Remove(key);
                    }
                    foreach(KeyValuePair<string,dynamic> kv in batchDic)
                    {
                        string produceDate = kv.Value.produce_date;
                        string batchNo = kv.Value.batch_no;
                        if(insertValue!="")  insertValue += ",";
                        insertValue += $@"({company_id},'{produceDate}','{batchNo}')";
                    }
                    if (insertValue!="")//INSERT INTO
                    {
                        insertSql += $"insert into info_item_batch (company_id,produce_date,batch_no) values {insertValue} on CONFLICT(company_id,produce_date,batch_no) DO NOTHING RETURNING batch_id,produce_date,batch_no;";
                        List<ExpandoObject> insertRec = await CDbDealer.GetRecordsFromSQLAsync(insertSql, cmd);
                        foreach (dynamic row in insertRec)
                        {
                            string produceDate = row.produce_date;
                            string batchNo = row.batch_no;
                            produceDate = produceDate.Substring(0, 10);
                            string key = produceDate + batchNo;
                            sheetRowBatch.Add(key, row.batch_id);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                msg = "生产日期/批次错误";
                MyLogger.LogMsg(msg + e.Message + e.StackTrace + "SQL:" + cmd.CommandText, company_id, "produce_date");
            }
            if (msg == "")
            {
                foreach (SheetRowMove row in SheetRows)
                {
                    if (row.produce_date.IsInvalid() || row.produce_date == "无产期")
                    {
                        row.batch_id = "0";
                        continue;
                    }
                    string key = row.produce_date + row.batch_no;
                    if (sheetRowBatch.ContainsKey(key))
                    {
                        row.batch_id = sheetRowBatch[key];
                    }
                    else
                    {
                        msg = "生产日期/批次错误";
                    }
                }
            }
            return msg;
        }

    }
}
