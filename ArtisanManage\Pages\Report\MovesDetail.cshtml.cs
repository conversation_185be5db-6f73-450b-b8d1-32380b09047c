﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ArtisanManage.Pages.BaseInfo
{
    public class MovesDetailModel : PageQueryModel
    { 
        public MovesDetailModel(CMySbCommand cmd) : base(Services.MenuId.movesDetail)
        {
            this.cmd = cmd;
            this.PageTitle = "调拨明细表";
            DataItems = new Dictionary<string, DataItem>()
            {
                {"startDay",new DataItem(){Title="开始日期", FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time", CompareOperator=">=",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 00:00"}},
                {"endDay"  ,new DataItem(){Title="结束日期", FldArea="divHead",CtrlType="jqxDateTimeInput", SqlFld="sm.happen_time", CompareOperator="<",Value=CPubVars.GetDateText(DateTime.Now.Date)+" 23:59",
                    JSDealItemOnSelect=@"                        
                            var s=$('#endDay').jqxDateTimeInput('val').toString();
                            if(s!=''){
                               s=s.replace('00:00','23:59');           
                               $('#endDay').jqxDateTimeInput('val',s);
                            }
                    "
                }},
                {"from_branch_id",new DataItem(){Title="出仓",FldArea="divHead",ButtonUsage="list",CompareOperator="=",LabelFld="from_branch_name",
                    SqlForOptions ="select branch_id as v,branch_name as l from info_branch"}},
                {"from_branch_position",new DataItem(){Title="出仓库位",FldArea="divHead",ButtonUsage="list",CompareOperator="=",LabelFld="from_branch_position_name",
                    SqlForOptions ="select branch_position as v,branch_position_name as l from info_branch_position"}},
                {"item_brand", new DataItem(){Title = "品牌",FldArea="divHead", LabelFld = "brand_name", ButtonUsage = "list", CompareOperator="=",SqlFld="ip.item_brand",
                    SqlForOptions = CommonTool.selectBrands}},
                 {"item_class",new DataItem(){Title="类别",FldArea="divHead",LabelFld="class_name",CtrlType="jqxDropDownTree",MumSelectable=true,CompareOperator="like",SqlFld="ip.other_class",
                   SqlForOptions=CommonTool.selectClasses,MaxRecords="600"}},
                {"make_brief",new DataItem(){Title="整单备注",FldArea="divHead",CompareOperator="ilike" } },
                {"remark",new DataItem(){Title="明细备注",SqlFld="sd.remark", FldArea="divHead",CompareOperator="ilike" } },

                {"to_branch_id",  new DataItem(){Title="入仓",FldArea="divHead",ButtonUsage="list",CompareOperator="=",LabelFld="to_branch_name",
                    SqlForOptions ="select branch_id as v,branch_name as l from info_branch"}},
                {"to_branch_position",  new DataItem(){Title="入仓库位",FldArea="divHead",ButtonUsage="list",CompareOperator="=",LabelFld="to_branch_position_name",
                    SqlForOptions ="select branch_position as v,branch_position_name as l from info_branch_position"}},
                {"item_id",new DataItem(){Title="商品名称",FldArea="divHead",LabelFld="item_name",ButtonUsage="event",CompareOperator="=",QueryByLabelLikeIfIdEmpty=true,SqlFld="sd.item_id",DropDownWidth="300",Pinned=true, SearchFields=CommonTool.itemSearchFields, SqlForOptions =CommonTool.selectItemWithBarcode }},
                 {"status",new DataItem(){FldArea="divHead",Title="单据状态",LabelFld = "status_name",ButtonUsage = "list",CompareOperator="=",Value="approved",Label="已审核单据",
                        Source = @"[
                                   {v:'unapproved',l:'未审核单据',condition:""sm.approve_time is null""},
                                   {v:'approved',l:'已审核单据',condition:""sm.approve_time is not null and red_flag is null""},                        
                                   {v:'all',l:'所有(包含未审)',condition:""true""}]"
                }},
            };

            //
            Grids = new Dictionary<string, QueryGrid>()
            {
                {
                  "gridItems",  new QueryGrid()
                  {
                     ShowAggregates = true, Sortable=true,
                     Columns = new Dictionary<string, DataItem>()
                     {
                       {"sheet_id",new DataItem(){SqlFld="sm.sheet_id",Hidden=true }},
                       {"sheet_no",new DataItem(){Title="单据编号", Width="200",SqlFld="sm.sheet_no",Linkable=true ,Sortable=true} },
                       {"hapen_time", new DataItem(){Title="调拨时间",Width="200",SqlFld="sm.happen_time", Sortable=true} },
                       {"approve_time", new DataItem(){Title="审核时间",Width="200",SqlFld="sm.approve_time",Sortable=true } },
                       {"oper_name",    new DataItem(){Title="业务员",     Width="10%",SqlFld="io.oper_name"}},
                       {"from_branch_name",  new DataItem(){Title="出货仓库",   Width="150"}},
                       {"from_branch_position_name",  new DataItem(){Title="出货库位",   Width="150"}},
                       {"to_branch_position_name",    new DataItem(){Title="入货库位",   Width="150"}},
                       {"to_branch_name",    new DataItem(){Title="入货仓库",   Width="150"}},                 
                       {"item_name",    new DataItem(){Title="商品名称",   Width="200",SqlFld="ip.item_name"}},
                       {"barcode",new DataItem(){Title = "条码",Width="90" ,SqlFld="itu.barcode",Hidden=true} },
                       {"item_no",new DataItem(){Title="商品编号", Hidden=true, Width="100",}},
                       //{"quantity1",     new DataItem(){Title="数量(无单位)",  Hidden=true,  Width="80",SqlFld="round( (sd.quantity * sd.unit_factor ):: NUMERIC / ( T.b1 ->> 'f1' ) :: NUMERIC, 2 )"}},
                       //{"quantity",     new DataItem(){Title="数量",       Width="200",SqlFld="concat ( round( (sd.quantity * sd.unit_factor ) :: NUMERIC / ( T.b1 ->> 'f1' )  :: NUMERIC, 2 ), ( T.b1 ->> 'f2' ) ) ",}},
                       {"unit_no",     new DataItem() {Title="单位",Hidden=true, Width="70",SqlFld=" sd.unit_no ",CellsAlign="right",}},
                      // {"b_pure_qty",   new DataItem(){Title="大单位数", Hidden=true, ShowSum=true,Sortable=true, CellsAlign="right", Width="50",
                      //    SqlFld="(case when b_unit_factor>0  then round((sd.quantity * sd.unit_factor * sd.inout_flag * (-1) /b_unit_factor)::numeric,3) else 0 end)"
                      //}},
                      //{"b_unit_no",     new DataItem() {Title="大单位",Hidden=true, Width="60", CellsAlign="right",}},

                       {"s_quantity",   new DataItem(){Title="数量(小单位)", CellsAlign="right",   Width="150",ShowSum=true,Hidden=false,
                           SqlFld = "(sd.quantity * sd.unit_factor)::numeric"
                       }},


                       {"quantity",     new DataItem() {Title="数量",  Width="5%",
                           SqlFld="concat(round((sd.quantity)::numeric,2),sd.unit_no)",CellsAlign="right",
                           FuncDealMe=(value)=>{return value=="0"?"":value; },
                           FuncGetSumValue = (sumColumnValues) =>
                           {
                               string sQty ="";
                               if(sumColumnValues["quantity_b"]!="") sQty+= sumColumnValues["quantity_b"]+"大";
                               if(sumColumnValues["quantity_m"]!="") sQty+= sumColumnValues["quantity_m"]+"中";
                               if(sumColumnValues["quantity_s"]!="") sQty+= sumColumnValues["quantity_s"]+"小";
                               return sQty;
                           }

                       }},
                       {"qty_no_unit",     new DataItem() {Title="纯数量", Width="70",Hidden=true,

                           //SqlFld=" sd.quantity * sd.inout_flag *(-1) ",CellsAlign="right",
                           SqlFld=" round((sd.quantity)::numeric,2) ",CellsAlign="right",
                        FuncDealMe=(value)=>{return value=="0"?"":value; },
                       }},
                      {"quantity_b",   new DataItem(){Title="大数", Hidden=true,HideOnLoad = true,ShowSum=true, CellsAlign="right", Width="7%",
                          SqlFld="case when itu.unit_type ='b' then sd.quantity  else null end"
                      }},
                      {"quantity_m",   new DataItem(){Title="中数",Hidden=true,HideOnLoad = true, ShowSum=true, CellsAlign="right", Width="7%",
                          SqlFld="case when itu.unit_type ='m' then sd.quantity  else null end"
                      }},
                      {"quantity_s",   new DataItem(){Title="小数", Hidden=true,HideOnLoad = true,ShowSum=true, CellsAlign="right", Width="7%",
                          SqlFld="case when itu.unit_type ='s' then sd.quantity  else null end"
                      }},

                     


                         {"wholesale_amount",     new DataItem(){Title="批发金额",  Hidden=true,  Width="80",SqlFld="(case when sd.unit_factor=1 then round(( sd.quantity*sd.wholesale_price ) :: NUMERIC, 2 ) else  round(( sd.quantity*sd.wholesale_price ) :: NUMERIC, 2 )  end)"}},
                       {"cost_amount",     new DataItem(){Title="成本金额",  Hidden=true,  Width="80",SqlFld="round((sd.quantity * sd.unit_factor  * sd.cost_price_avg)::numeric, 2) "}},
                       {"buy_amount",     new DataItem(){Title="进价金额",  Hidden=true,  Width="80",SqlFld="round((sd.quantity * sd.unit_factor  * sd.buy_price)::numeric, 2)"}},
                       {"contract_amount",     new DataItem(){Title="承包金额",  Hidden=true,  Width="80",SqlFld="round((sd.quantity*sd.contract_price )::numeric, 2)"}},
                        {"remark",     new DataItem() {Title="明细备注",Hidden=true, Width="70",SqlFld=" sd.remark ",CellsAlign="right",}},
                     },
                     QueryFromSQL=@"
		FROM
			sheet_move_detail sd
		left join sheet_move_main sm on sd.sheet_id=sm.sheet_id and sm.company_id=~COMPANY_ID AND sm.company_id = sd.company_id
	LEFT JOIN info_item_prop ip ON ip.item_id = sd.item_id and ip.company_id = sd.company_id
	LEFT JOIN ( SELECT company_id,branch_id, branch_name AS from_branch_name FROM info_branch WHERE company_id = ~COMPANY_ID ) fb ON sm.from_branch_id = fb.branch_id AND sm.company_id = fb.company_id
	LEFT JOIN ( SELECT company_id,branch_position, branch_position_name AS from_branch_position_name FROM info_branch_position WHERE company_id = ~COMPANY_ID ) fbp ON sd.from_branch_position = fbp.branch_position AND sm.company_id = fbp.company_id
	LEFT JOIN ( SELECT company_id,branch_id, branch_name AS to_branch_name FROM info_branch WHERE company_id = ~COMPANY_ID ) tb ON sm.to_branch_id = tb.branch_id AND sm.company_id = fb.company_id
	LEFT JOIN ( SELECT company_id,branch_position, branch_position_name AS to_branch_position_name FROM info_branch_position WHERE company_id = ~COMPANY_ID ) tbp ON sd.to_branch_position = tbp.branch_position AND sm.company_id = fbp.company_id
    LEFT JOIN info_operator io on io.oper_id = sm.seller_id and io.company_id= ~COMPANY_ID
    LEFT JOIN info_item_multi_unit itu on itu.unit_no = sd.unit_no and itu.company_id = ~COMPANY_ID and itu.item_id = sd.item_id
	LEFT JOIN (
			SELECT
				item_id,
				s,
				M,
				b,
			( CASE WHEN b ->> 'f1' IS NULL THEN s ELSE b END ) b1 
		FROM
			crosstab ( 'select item_id,unit_type,row_to_json(row(unit_factor,unit_no)) as json from info_item_multi_unit where company_id= ~COMPANY_ID ORDER BY item_id',
			$$ VALUES ( 's' :: TEXT ), ( 'm' :: TEXT ), ( 'b' :: TEXT ) $$ ) AS errr ( item_id INT, s jsonb, M jsonb, b jsonb ) 
		) T ON T.item_id = sd.item_id 
where
	  
	 sm.red_flag IS NULL 
    AND sm.company_id = ~COMPANY_ID
	and sheet_no is not null
",
                     //QueryGroupBySQL=" group by sd.item_id,b_unit_no,b_unit_factor,ip.item_name,from_branch_name,to_branch_name,u.barcode",
                     QueryOrderSQL=" order by sm.happen_time desc, ip.item_name"
                  }
                } 
            }; 
        }
        public async Task OnGet()
        {  
            await InitGet(cmd);
            //m_classTreeStr =ClassEditModel.getClassTreeStr(); 
        }
        public override async Task OnPageInitedWithDataAndRight(CMySbCommand cmd)
        {
            bool seeInPrice = false;
            if (JsonOperRights.IsValid())
            {
                dynamic operRights = Newtonsoft.Json.JsonConvert.DeserializeObject(JsonOperRightsOrig);
                if (operRights?.delicacy?.seeInPrice?.value is not null)
                    seeInPrice = ((string)operRights.delicacy.seeInPrice.value).ToLower() == "true";
            }
            if (!seeInPrice)
            {
                var columns = await Grids["gridItems"].GetAllColumns();
                columns["buy_amount"].HideOnLoad = columns["buy_amount"].Hidden = true;
                columns["cost_amount"].HideOnLoad = columns["cost_amount"].Hidden = true;
            }

        }
    }



    [Route("api/[controller]/[action]")]
    public class MovesDetailController : QueryController
    { 
        public MovesDetailController(CMySbCommand cmd)
        {
            this.cmd = cmd;
        }


        [HttpGet]
        public async Task<string> GetDataItemOptions(string operKey, string dataItemName, string flds, string value, string availValues)
        {
            MovesDetailModel model = new MovesDetailModel(cmd);
            string data = await PageBaseModel.GetDataItemOptions(cmd,operKey, model.DataItems, dataItemName, flds, value, availValues);
            return data;
        }
        [HttpGet]
        public async Task<object> GetQueryRecords()
        {
            MovesDetailModel model = new MovesDetailModel(cmd);
            object records = await model.GetRecordFromQuerySQL(Request, cmd);
            return records;
        }

        [HttpPost]
        public async Task<ActionResult> ExportExcel()
        {
            MovesDetailModel model = new MovesDetailModel(cmd);
            return await model.ExportExcel(Request, cmd);
        }
    }
}

