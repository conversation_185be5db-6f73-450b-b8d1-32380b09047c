﻿@page
@model ArtisanManage.Pages.WorkFlow.CheckSheetsSheetModel
@{
    Layout = null;

}
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <title>CheckSheetsSheet</title>
    <!--<link href="~/NiceWidgets/NiceWidgets.css" rel="stylesheet" />-->
    <link href="~/css/DataForm.css" rel="stylesheet" />
    <script src="~/js/Vue.js"></script>
    <link href="~/lib/element-ui/theme/index.css" rel="stylesheet" />
    <script src="~/lib/element-ui/index.min.js"></script>
    <script type="text/javascript" src="~/jqwidgets/scripts/jquery-1.12.4.min.js"></script>
    <link rel="stylesheet" href="~/jqwidgets/jqwidgets/styles/jqx.base.css" type="text/css" />
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcore.js"></script>
    <link rel="stylesheet" href="~/MiniJsLib/MiniJsLibPC.css?v=@Html.Raw(Model.Version)">
    <script src="~/MiniJsLib/MiniJsLibPC.js?v=@Html.Raw(Model.Version)"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxdatetimeinput.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxcalendar.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxwindow.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/jqxtabs.js"></script>

    <link rel="stylesheet" href="~/MiniJsLib/jquery.dialog.css">
    <script src="~/MiniJsLib/jquery.dialog.js"></script>
    <link rel="stylesheet" type="text/css" href="~/css/CheckSheetsSheet.css?v=1" />


    <script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.js"></script>
    <script type="text/javascript" src="~/jqwidgets/jqwidgets/globalization/globalize.culture.zh-CN.js"></script>

    <style>
        * {
            font-family: "微软雅黑"
        }
        [v-cloak] {
            display: none;
        }

        ::-webkit-scrollbar {
            width: 16px;
            height: 16px;
            background-color: #000;
        }

        ::-webkit-scrollbar-track {
            background-color: #efefef;
        }

        ::-webkit-scrollbar-thumb {
            border-radius: 7px;
            -webkit-box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
            background-color: #00000033;
        }

        ::-webkit-scrollbar-corner {
            background-color: black;
        }

        .query_wrapper {
            width: 100%;
            display: flex;
            flex-direction:row;
            justify-content: space-between;
        }

        .pages_query {
            flex: 4;
            display: flex;
            margin-left: 20px;
            flex-wrap: wrap;
        }

        .query_item {
            display: flex;
            margin-left: 20px;
            margin-top: 3px;
        }

        .query_btn {
            flex: 1;
            display: flex;
            flex-direction:row;
            justify-content:flex-start;
        }

        .el-input--suffix .el-input__inner{
            padding-right:10px;
        }
        .inline-input input {
            width:125px;
        }

        #jqxdatetimeinputStart, #jqxdatetimeinputEnd {
            width:110px!important;
        }

        .jqx-max-size.jqx-position-relative input{
            width:110px!important;
        }

        .jqx-position-absolute.jqx-action-button.jqx-fill-state-normal.jqx-rc-r.jqx-default{
            left:90px!important;
        }
    </style>

</head>
<!--此页面要兼容XP的客户端,  ...  padStart等新语法不要用-->
<body>
    <div id="pages" class=""  v-cloak>
        <div class="pages_title" id="pagesTitle">
            {{title}}
        </div>
        <div class="query_wrapper">
            <div class="pages_query" id="pagesQuery">
                <div class="query_item">
                    <label class="item_name">
                        员工
                        <el-autocomplete size="medium" style="margin-left:5px" class="inline-input" clearable v-model="FormData.name" :fetch-suggestions="querySearch" placeholder="请输入员工姓名" @@select="handleSelect"></el-autocomplete>
                    </label>
                    <label class="item_depart">
                        部门
                        <el-autocomplete size="medium" style="margin-left:5px" class="inline-input" clearable v-model="FormData.deptname" :fetch-suggestions="querySearchByDepart" placeholder="请输入员工部门" @@select="handleSelect"></el-autocomplete>
                    </label>
                </div>
                <div class="query_item"><label class="item_name">开始时间</label><div id='jqxdatetimeinputStart' style="width: 100px"></div></div>
                <div class="query_item"><label class="item_name">结束时间</label><div id='jqxdatetimeinputEnd' style="width: 100px"></div></div>
                <div style=" display: flex; margin-left: 20px;">
                    <div class="dropdown">
                        <button class="dropbtn">{{dateType}}</button>
                        <div class="dropdown-content">
                            <div v-for="(item,index) in dateColumns" :key="index" @@click="selectDateType(item)">{{item.titles}}</div>
                        </div>
                    </div>
                </div>
                <div class="query_item">
                    <el-checkbox v-model="showNoDataWorker">显示无数据员工</el-checkbox>
                </div>
            </div>
            <div class="query_btn">
                <el-button type="danger" @@click="query" style="width:80px;margin-left:10px;font-size: 14px;display: flex; align-items: center;justify-content: center">查询</el-button>
                <!--<button @@click="cleanSearch" style="width:80px;margin-left:20px" >清空</button>-->
                <el-button type="info" @@click="showHistoryCheck" style="width:90px;margin-left:10px;font-size: 14px;display: flex; align-items: center;justify-content: center">查交账单</el-button>
             </div>
        </div>
        <div class="pages_content">
            <table class="dataTable" ref="table">
                <thead id="pagesContnetTableThead">
                    <tr>
                        <th align="left" style="width: 55px">序号</th>
                        <th align="left">员工</th>
                        <th align="left">拜访数/未交单数</th>
                        <th align="left">已交帐</th>
                        <th align="right">金额</th>
                    </tr>
                </thead>
                <tbody :style="{height: pagesTableHeight}">
                    <tr v-for="(item, index) in listData" :key="index" class="tableTd">
                        <td style="width: 55px">{{index+1}}</td>
                        <td><span class="showSummaryLink" @@click="showSummary(item)">{{item.seller_name}}</span></td>
                        <td>
                            <p>
                                <span v-if="item.v_count">{{item.v_count}}</span>
                                <span v-else>0</span>
                                /
                                <span v-if="item.s_count">{{item.s_count}}</span>
                                <span v-else>0</span>
                            </p>
                        </td>
                        <td>
                            @*<span style="margin-left:20px;color:#67C23A" v-if="item.s_count == 0 && item.al_s_count > 0">已交</span>
                            <span style="margin-left:20px;color: #f40" v-else-if="item.s_count > 0">未交</span>
                            <span style="margin-left:20px;" v-else-if="item.s_count == 0 && item.al_s_count == 0"></span>*@
                            <div>
                                @* <p v-for="(checkSheet,cidx) in item.checkSheetIDs" :key="cidx" @@click="onCheckSheetClick(checkSheet,item.seller_name)"  style="color:#f88;cursor:pointer"> *@
                                @*     已交{{item.checkSheetIDs.length>1?cidx+1:''}}  *@
                                @* </p>  *@
                                <div v-if="item.checkSheetIDs.length >= 1">
                                     <p v-if="item.checkSheetIDs.length > 1" style="color:#f88;margin-bottom:4px;">
                                         <el-popover
                                           placement="right"
                                           width="500"
                                           
                                           trigger="click">
                                             <el-table :data="item.checkSheetIDs" stripe>
                                                 <el-table-column
                                                     label="操作"
                                                     style="text-align: center"
                                                     width="80">
                                                     <template slot-scope="scope">
                                                         <el-button @@click="onCheckSheetClick(scope.row,item.seller_name,$event)" type="text" size="small" style="text-align: left">查看</el-button>
                                                     </template>
                                                 </el-table-column>
                                                 
                                                 <el-table-column width="100" property="name" label="交账人"></el-table-column>
                                                 <el-table-column width="100" property="check_money" label="交账金额"></el-table-column>
                                                 <el-table-column width="200" property="happen_time" label="交账时间"></el-table-column>
                                             </el-table>
                                           <el-button slot="reference" style="border: none;font-size: 16px;color:#f88;margin-bottom:4px;">{{item.checkSheetIDs.length}}次</el-button>
                                         </el-popover>
                                         
                                     </p>
                                       <p v-else @@click="onCheckSheetClick(item.checkSheetIDs[0],item.seller_name,$event)" style="color:#f88;margin-bottom:4px;">
                                           <el-button style="border: none;font-size: 16px;color:#f88;margin-bottom:4px;">1次</el-button>
                                       </p>
                                 </div>
                                
                            </div>
                        </td>
                        <td>{{fix(Number(item.total_amount))}}</td>
                    </tr>

                    <tr v-for="(totalItem,totalIndex) in total" class="tableTd" style="font-weight: bold">
                        <td style="width: 55px"></td>
                        <td>合计</td>
                        <td>
                            <p>
                                <span v-if="totalItem.v_tcount">{{totalItem.v_tcount}}</span>
                                <span v-else>0</span>
                                <span> / </span>
                                <span v-if="totalItem.s_tcount">{{totalItem.s_tcount}}</span>
                                <span v-else>0</span>
                            </p>
                        </td>
                        <td></td>
                        <td>{{fix(totalItem.total)}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <script>
        var g_operKey = '@Model.OperKey';
    </script>
    
    <script>
        var app = new Vue({
            el: "#pages",
            data() {
                return {
                    title: '交账单',
                    pagesTableHeight: '750px',
                    pageSize: 1000,
                    startRow: 0,
                    startDate: '',
                    endDate: '',
                    FormData: {
                        name: '',
                        deptname :''
                    },
                    listData: [],
                    listData1: [],
                    total: [],
                    restaurants: [],
                    departs:[],
                    queryData: {},
                    sheets: {},
                    dateType: '近7天未交',
                    dateColumns: [
                        {
                            titles: '今天未交',
                            ids: '1days'
                        },
                        {
                            titles: '昨天未交',
                            ids: 'yesterday'
                        },
                        {
                            titles: '前天未交',
                            ids: 'beforeYesterday'
                        },
                        {
                            titles: '近2天未交',
                            ids: '2days'
                        },
                        {
                            titles: '近3天未交',
                            ids: '3days'
                        },
                        {
                            titles: '近7天未交',
                            ids: '7days'
                        }
                    ],
                    showCheckSheetArr:[],
                    showCheckSheetFlag: false ,
                    showNoDataWorker:false,
                }
            },
            mounted() {
                console.log('mounted')
                this.onloadData()
                this.onloadSeller({ operKey: g_operKey})
                this.handleBodyHeight()
            },
            methods: {
                getDepartId(name) {
                   let arr= this.departs.filter((depart )=> {
                            return  depart.value===name
                    })
                    return arr[0]? arr[0].id : ''
                },
                handleBodyHeight() {
                    const pageHeight = document.documentElement.clientHeight;
                    const pagesTitle = $('#pagesTitle').outerHeight(true)
                    const pagesQuery = $('#pagesQuery').outerHeight(true)
                    const pagesContnetTableThead = $('#pagesContnetTableThead').outerHeight(true)
                    this.pagesTableHeight = pageHeight - pagesTitle - pagesQuery - pagesContnetTableThead - 40 + 'px'
                },
                formatDate(date) {
                    return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
                },
                handleSelect() {
                },
                querySearch(queryString, cb) {
                    let restaurants
                    if (this.FormData.deptname) {
                        let departID = this.getDepartId(this.FormData.deptname)
                        restaurants = this.restaurants.filter((restaurant) => {
                            return restaurant.depart_path.indexOf(departID) !== -1
                        })
                    } else {
                            restaurants = this.restaurants;
                    }
      
                    var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
                    // 调用 callback 返回建议列表的数据
                    cb(results);
                },
                  querySearchByDepart(queryString, cb) {
                    var departs = this.departs;
                    var results = queryString ? departs.filter(this.createFilterDepart(queryString)) : departs;
                    // 调用 callback 返回建议列表的数据
                    cb(results);
                },
                createFilter(queryString) {
                    return (restaurant) => {
                        return (restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                    };
                },
                 createFilterDepart(queryString) {
                    return (departs) => {
                        return (departs.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0);
                    };
                },
                computeDate(date, days) {
                    var d = new Date(date);
                    d.setDate(d.getDate() + days);    //如果加月就是d.getMonth(); 以此类推
                    var m = d.getMonth() + 1;
                    return d.getFullYear() + '-' + m + '-' + d.getDate();// + " " + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds();  //返回格式 2016-4-27 13:18:00 （可根据自己需要自行设定返回内容）
                },
                //初始化页面
                onloadData() {
                    /*
                    var startTime = '', endTime = ''
                    startTime = this.computeDate(new Date(), -7)
                    endTime = this.computeDate(new Date(), 0) + ' 23:59:59'
                    let params = {
                        sellerName: this.FormData.name,
                        sellerID: '',
                        startRow: this.startRow,
                        pageSize: this.pageSize,
                        startTime: startTime,
                        endTime: endTime,
                        operKey: g_operKey
                    }
                    console.log('before getCheckSheetList')
                    this.getCheckSheetList(params)
                    */                     
                    $("#jqxdatetimeinputStart").jqxDateTimeInput({ culture: 'zh-CN', width: '150px', height: '25px', formatString: "yyyy-MM-dd" });
                    //$("#jqxdatetimeinputStart").jqxDateTimeInput('setDate', new Date(this.computeDate(new Date(), -6)));//默认为7天，更改为记忆时间
                    //应该在这里执行获取记忆时间
                    this.fetchDateRemember().then(() => {

                        $("#jqxdatetimeinputEnd").jqxDateTimeInput({ culture: 'zh-CN', width: '150px', height: '25px', formatString: "yyyy-MM-dd" });
                        this.startDate = this.computeDate($("#jqxdatetimeinputStart").jqxDateTimeInput('getDate'), 0)
                        this.endDate = this.computeDate($("#jqxdatetimeinputEnd").jqxDateTimeInput('getDate'), 0) + ' 23:59:59'

                        $('#jqxdatetimeinputStart').on('change', event => {
                            var beginDate = this.computeDate(event.args.date, 0);
                            var endDate = this.endDate;
                            var d1 = new Date(beginDate.replace(/\-/g, "\/"));
                            var d2 = new Date(endDate.replace(/\-/g, "\/"));

                            if (beginDate != "" && endDate != "" && d1 > d2) {
                                bw.toast("开始时间不能大于结束时间！");
                                $("#jqxdatetimeinputStart").jqxDateTimeInput('setDate', new Date(d2.getFullYear(), d2.getMonth(), d2.getDate()))
                                this.startDate = this.computeDate(d2, 0);
                            } else {
                                this.startDate = this.computeDate(event.args.date, 0);
                                var diff = (d2 - d1) / (1000 * 60 * 60 * 24); // 转换成日期差值
                                //这里提交记忆时间
                                this.updateDateRemember(diff - 1);                                
                            }
                            this.dateType = "指定日期"
                        })

                        $('#jqxdatetimeinputEnd').on('change', event => {
                            var beginDate = this.startDate;
                            var endDate = this.computeDate(event.args.date, 0);
                            var d1 = new Date(beginDate.replace(/\-/g, "\/"));
                            var d2 = new Date(endDate.replace(/\-/g, "\/"));
                            if (beginDate != "" && endDate != "" && d1 > d2) {
                                bw.toast("结束时间不能小于开始时间！");
                                $("#jqxdatetimeinputEnd").jqxDateTimeInput('setDate', new Date(d1.getFullYear(), d1.getMonth(), d1.getDate()))
                                this.endDate = beginDate + ' 23:59:59';
                            } else {
                                this.endDate = this.computeDate(event.args.date, 0) + ' 23:59:59';
                            }
                            this.dateType = "指定日期"

                        })

                        this.query(); // fetchDateRemember 完成后执行 query
                    
                    }).catch((error) => {
                        console.error('Error during fetchDateRemember:', error);
                    });
                                    
                },
                // 首页查询
                query() {
                    this.listData = []
                    this.total = 0
                    let departID= this.getDepartId(this.FormData.deptname)
                    let params = {
                        sellerName: this.FormData.name,
                        departID : departID,
                        sellerID: '',
                        startRow: this.startRow,
                        pageSize: this.pageSize,
                        startTime: this.startDate,
                        endTime: this.endDate,
                        operKey: g_operKey,
                        showNoDataWorker:this.showNoDataWorker,
                    }
                    this.getCheckSheetList(params)
                },
                // 清空条件
                cleanSearch() {
                    this.FormData = {
                        name: ''
                    }
                    this.pageSize = 1000,
                        this.startRow = 0
                    $("#jqxdatetimeinputStart").jqxDateTimeInput('setDate', new Date(this.computeDate(new Date(), -6)));
                    $("#jqxdatetimeinputEnd").jqxDateTimeInput('setDate', new Date());

                    this.dateType = "近7天未交"
                },
                
                // 获取记忆时间
                fetchDateRemember() {
                    return new Promise((resolve, reject) => {
                        this.onloadDateRemember({ operKey: g_operKey }, resolve, reject);
                    });
                },
                onloadDateRemember(obj, resolve, reject) {
                    $.ajax({
                        url: '/AppApi/SheetCheckAcount/GetRememberOption', 
                        type: 'get',
                        data: obj,
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json',
                    }).then((res) => {
                        if (res.result === 'OK') {
                            var options = res.data && res.data.options ? JSON.parse(res.data.options) : null;                            
                            var dateTypeMapping = {
                                0: "今天未交",
                                1: "近2天未交",
                                2: "近3天未交",
                                6: "近7天未交"
                            };
                            if (options && options.page_CheckSheetsSheet && options.page_CheckSheetsSheet.dateSlot) {                                
                                var dateslot = options.page_CheckSheetsSheet.dateSlot;                                
                                // 将浮点数转换为整数
                                dateslot = Math.round(Math.abs(dateslot));
                        
                                this.dateType = dateTypeMapping[dateslot] || "指定日期";
                                console.log("dateslot处理后: ",dateslot);
                            } else {                       
                                var dateslot = 6; // 默认值
                                this.dateType = "近7天未交";
                                console.log("没找到，使用默认值");
                            }
                            $("#jqxdatetimeinputStart").jqxDateTimeInput('setDate', new Date(this.computeDate(new Date(), -dateslot)));//更改为记忆时间
                            resolve(); // 解决 Promise
                        }
                        else {
                            // 处理错误情况
                            console.error('Error onloadDateRemember');
                            reject('Error onloadDateRemember'); // 拒绝 Promise
                        }
                    });                    
                },
                // 提交记忆时间
                updateDateRemember(diffday) {
                    let params = {
                        operKey: g_operKey, 
                        diffDays: diffday   
                    };
                    this.postDateRemember(params);
                },
                postDateRemember(obj) {
                    $.ajax({
                        url: '/AppApi/SheetCheckAcount/RememberOption', 
                        type: 'POST',
                        data: JSON.stringify(obj),
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json',
                    }).then(function (res) {
                        // 处理成功响应
                        if (res.result === 'OK') {
                            console.log('日期保存成功');
                            // console.log('operKey:', res.key);
                            // console.log('day:' , res.day);
                        }
                        else {
                            // 处理错误情况
                            console.error('Error DateRemember:', res.msg);
                        }
                    });
                },
                
                // 选择特定时间快捷
                selectDateType(item) {
                    this.endDate = this.computeDate(new Date(), 0) + ' 23:59:59'
                    if (item.ids == '1days') {
                        this.startDate = this.computeDate(new Date(), 0)
                    }
                    else if (item.ids == 'yesterday') {
                        this.startDate = this.computeDate(new Date(), -1)
                        this.endDate = this.computeDate(new Date(), -1) + ' 23:59:59'
                    }
                    else if (item.ids == 'beforeYesterday') {
                        this.startDate = this.computeDate(new Date(), -2)
                        this.endDate = this.computeDate(new Date(), -2) + ' 23:59:59'
                    }
                    else if (item.ids == '2days') {
                        this.startDate = this.computeDate(new Date(), -1)
                    }
                    else if (item.ids == '3days') {
                        this.startDate = this.computeDate(new Date(), -2)
                    }
                    else if (item.ids == '7days') {
                        this.startDate = this.computeDate(new Date(), -6)
                    }
                    var d1 = new Date(this.startDate.replace(/\-/g, "\/"));
                    var d2 = new Date(this.endDate.replace(/\-/g, "\/"));
                    $("#jqxdatetimeinputStart").jqxDateTimeInput('setDate', new Date(d1.getFullYear(), d1.getMonth(), d1.getDate()))
                    $("#jqxdatetimeinputEnd").jqxDateTimeInput('setDate', new Date(d2.getFullYear(), d2.getMonth(), d2.getDate()))
                    this.dateType = item.titles
                },
                // 查询历史切换按钮
                showHistoryCheck() {
                    window.parent.newTabPage("历史交账单", "WorkFlow/CheckAccount/CheckSheetsSheetHistory")
                },
                myPadLeft(temp, len, charStr) {
                    var s = temp + '';
                    return new Array(len - s.length + 1).join(charStr, '') + s;
                },
                fix(amount) {
                    if (amount) amount = parseFloat(amount)
                    if (typeof (amount) == 'undefined') {
                        return amount
                    } else {
                        if (amount.toFixed)
                            amount = amount.toFixed(4)
                    }
                    amount = amount.toString()
                    if (amount.indexOf('.0000') > 0) {
                        amount = amount.replace('.0000', '')
                    }
                    var d = amount.indexOf('.')
                    if (d > 0) {
                        for (var i = 4; i >= 1; i--) {
                            var n = amount.lastIndexOf(this.myPadLeft('0', i, '0'))
                            if (n > d && n == amount.length - i) {
                                amount = amount.substr(0, amount.length - i)
                            }
                        }
                        d = amount.indexOf('.')
                        if (d == amount.length - 1) amount = amount.substr(0, amount.length - 1)
                    }
                    return amount
                },
                onloadSeller(obj) {
                    $.ajax({
                        url: '/AppApi/SheetCheckAcount/GetSaleOrOrderSaleHistory',
                        type: 'get',
                        data: obj,
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(function (res) {
                        if (res.result === 'OK') {
                            console.log(res)
                            app.restaurants = res.opers
                            app.departs = res.departs
                        }
                    });
                },
                // 获取交账单
                getCheckSheetList(obj) {
                    $.ajax({
                        url: '/AppApi/SheetCheckAcount/GetCheckSheetList',
                        type: 'get',
                        data: obj,
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(function (res) {
                        if (res.result === 'OK') {
                            app.listData = res.records || [];
                            app.listData1 = res.records || [];
                            app.listData.forEach(r=>{
                                let a
                                let arr=[]
                                if(r.al_s_check_sheet_ids){
                                    a= r.al_s_check_sheet_ids.split(',')
                                    arr.splice(arr.length,0,...a)
                                }
                                if(r.al_a_check_sheet_ids){
                                    a= r.al_a_check_sheet_ids.split(',')
                                    arr.splice(arr.length,0,...a)
                                }
                                 if(r.al_yf_check_sheet_ids){
                                    a= r.al_yf_check_sheet_ids.split(',')
                                    arr.splice(arr.length,0,...a)
                                }
                                
                                if(r.al_p_check_sheet_ids){
                                    a= r.al_p_check_sheet_ids.split(',')
                                    arr.splice(arr.length,0,...a)
                                }
                                if(r.al_f_check_sheet_ids){
                                    a= r.al_f_check_sheet_ids.split(',')
                                    arr.splice(arr.length,0,...a)
                                 }
                                 if(r.al_sr_check_sheet_ids){
                                    a= r.al_sr_check_sheet_ids.split(',')
                                    arr.splice(arr.length,0,...a)
                                 }
                                 arr=Array.from(new Set(arr))
                                 r.checkSheetIDs=[]
                                 arr.forEach(s=>{
                                    a=s.split('|')
                                    r.checkSheetIDs.push({sheet_id:a[0],getter_id:a[1],happen_time:a[2],check_money:a[3],name:r.seller_name}) 
                                 })
                                 r.checkSheetIDs.sort((a, b) => b.happen_time.localeCompare(a.happen_time));

                            })
                            
                           
                            // 过滤掉未交单数为0的数据
                            if(!this.showNoDataWorker){
                                app.listData = app.listData.filter(function(item) {
                                    return (item.s_count && parseInt(item.s_count) > 0);
                                })
                            }

                            app.listData1 = app.listData;
                           
                            
                            app.total = res.total
                        }

                    });
                },
                // 显示详情
                showSummary(item) {
                    let params = {
                        sellerID: item.seller_id,
                        sellerName: item.seller_name,
                        pageSize: this.pageSize,
                        startRow: this.startRow,
                        // dateType: this.queryData.dateTypeIds? this.queryData.dateTypeIds:''
                        startTime: this.startDate,
                        endTime: this.endDate,
                        operKey: g_operKey
                    }
                    this.queryData = params

                    window.g_showAccountTitle = item.seller_name
                    window.g_sellerID = item.seller_id
                    window.g_checkSheets_queryData=this.queryData
                    this.NewCheckSheet(this.queryData)
                    

                },
                // 获取详情数据
                NewCheckSheet(obj) {
                    console.log("app.sheets1",app.sheets);
                    $.ajax({
                        url: '/AppApi/SheetCheckAcount/NewCheckSheet',
                        type: 'get',
                        data: obj,
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(function (res) {
                        if (res.result === 'OK') {
                            var count = 0;
                            var checked = true
                            updateSheetsCheck(res.saleSheets || [], checked, '')
                            updateSheetsCheck(res.borrowSheets || [], checked, '')
                            updateSheetsCheck(res.prepaySheets || [], checked, 'prepaySheets')
                            updateSheetsCheck(res.pregetSheets || [], checked, '')
                            updateSheetsCheck(res.getArrearSheets || [], checked, 'getArrearSheets')
                            updateSheetsCheck(res.feeOutSheets || [], checked, '')
                            updateSheetsCheck(res.incomeSheets || [], checked, '')
                            app.sheets = res
                          /*
                            app.sheets = { 
                                sheet_id: '',
                                happen_time: '',
                                isRed: false,
                                startDate: app.startDate,
                                endDate: app.endDate
                            }*/

                            $.extend(app.sheets, res)

                            window.checkedSheetsCount = count;
                            window.g_sheet = app.sheets
                            function updateSheetsCheck(sheets, check, sheetFlag) {
                              let moneyInoutFlag = 1
                              if(sheetFlag === 'prepaySheets') {
                                moneyInoutFlag *= -1
                              }
                                sheets.forEach(function (sheet) {
                                     if (sheetFlag === 'getArrearSheets' || sheetFlag === 'prepaySheets') {
                                        sheet.prepay_amount = 0
                                        sheet.zc_amount = 0
                                        sheet.qtsr_amount = 0
                        
                        
                                        if (sheet.payway1_type === 'YS') {
                                          sheet.prepay_amount += Number(sheet.payway1_amount)
                                        } else if (sheet.payway1_type === 'ZC') {
                                          sheet.zc_amount += Number(sheet.payway1_amount)
                                        } else if (sheet.payway1_type === 'QTSR') {
                                          sheet.qtsr_amount += Number(sheet.payway1_amount)*moneyInoutFlag
                                        }
                        
                                        if (sheet.payway2_type === 'YS') {
                                          sheet.prepay_amount += Number(sheet.payway2_amount)
                                        } else if (sheet.payway2_type === 'ZC') {
                                          sheet.zc_amount += Number(sheet.payway2_amount)
                                        } else if (sheet.payway2_type === 'QTSR') {
                                          sheet.qtsr_amount += Number(sheet.payway2_amount)*moneyInoutFlag
                                        }
                                        if (sheet.payway3_type === 'YS') {
                                          sheet.prepay_amount += Number(sheet.payway3_amount)
                                        } else if (sheet.payway3_type === 'ZC') {
                                          sheet.zc_amount += Number(sheet.payway3_amount)
                                        } else if (sheet.payway3_type === 'QTSR') {
                                          sheet.qtsr_amount += Number(sheet.payway3_amount)*moneyInoutFlag
                                        }
                        
                        
                                        if(sheetFlag === 'getArrearSheets') {
                                          sheet.real_get_amount = Number(sheet.real_get_amount) -  Number(sheet.prepay_amount) -  Number(sheet.zc_amount) -  Number(sheet.qtsr_amount)
                                        }
                                        if(sheetFlag === 'prepaySheets') {
                                          sheet.real_get_amount = Number(sheet.real_get_amount) -  Number(sheet.left_amount) -  Number(sheet.disc_amount) -  Number(sheet.qtsr_amount)
                                        }
                                      }
                                    sheet.isChecked = check
                                    if (sheet.isChecked)
                                        count++
                                })
                            }
                            window.parent.newTabPage("个人交账单", "WorkFlow/CheckAccount/ShowAccount",window)

                        }
                    });
                },
                onCheckSheetClick(checkSheet,sellerName,e){
                   e.stopPropagation()
                    let obj = {
                        sheetID: checkSheet.sheet_id,
                        operKey: g_operKey,
                        happenTime: checkSheet.happen_time,
                        getterID: checkSheet.getter_id,
                        dateTypeIds:window.dateTypeIds== 'alldays' ? '1days':window.dateTypeIds
                    }

                    window.summCheckSheet = obj
                    window.g_showAccountTitle = sellerName
                    this.loadCheckSheet(obj)
                },
                showCheckSheetIDs(checkSheetIDs) {
                  this.showCheckSheetArr = checkSheetIDs
                  this.showCheckSheetPopup()
                },
                showCheckSheetPopup(){
                  this.showCheckSheetFlag = true
                },
                closeCheckSheetPopup() {
                  this.showCheckSheetFlag = false
                 // this.showCheckSheetArr = []
                },
                loadCheckSheet(obj) {
                    $.ajax({
                        url: '/AppApi/SheetCheckAcount/LoadCheckSheet',
                        type: 'get',
                        data: obj,
                        contentType: "application/json;charset=UTF-8",
                        dataType: 'json'
                    }).then(function (res) {
                        if (res.result === 'OK') {
                            var count = 0;
                            var checked = true
                            updateSheetsCheck(res.saleSheets || [], checked)
                            updateSheetsCheck(res.borrowSheets || [], checked)
                            updateSheetsCheck(res.prepaySheets || [], checked)
                            updateSheetsCheck(res.pregetSheets || [], checked)
                            updateSheetsCheck(res.getArrearSheets || [], checked)
                            updateSheetsCheck(res.feeOutSheets || [], checked)
                            updateSheetsCheck(res.incomeSheets || [], checked)
                            
                            //app.sheets = res
                             /*app.sheets.sheet_id = obj.sheetID
                             app.sheets.happen_time =  obj.happenTime
                             app.sheets.isRed = false
                             app.sheets.startDate = app.startDate
                            app.sheets.endDate = app.endDate
                            */
                            // app.sheets = {
                            //     ...res,
                            //     sheet_id: obj.sheetID,
                            //     happen_time: obj.happenTime,
                            //     isRed: false,
                            //     startDate: app.startDate,
                            //     endDate: app.endDate
                            // }
                            window.checkedSheetsCount = count;
                            window.g_sheet = res;// app.sheets
                            function updateSheetsCheck(sheets, check) {
                                sheets.forEach(function (sheet) {
                                    sheet.isChecked = check
                                    if (sheet.isChecked)
                                        count++
                                })
                            }
                            console.log("history", window.g_sheet);
                            window.parent.newTabPage("历史交账单详情", "WorkFlow/CheckAccount/ShowAccount", window)
                        }
                    })
                },

            }
        })
      
     
    </script>
</body>
</html>