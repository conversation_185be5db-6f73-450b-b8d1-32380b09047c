﻿using ArtisanManage.Models;
using ArtisanManage.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;

namespace ArtisanManage.Pages
{
    public class WorkTabModel : PageModel
    {
        public string OperKey { get; private set; }
		public string BaiduKey = CPubVars.BaiduKey;
		public void OnGet(string operKey)
        {
            OperKey = operKey;
        }
    }

    [Route("api/[controller]/[action]")]
    public class WorkTabController : YjController
    {
        private readonly IHttpClientFactory _httpClientFactory;
        CMySbCommand cmd;
        public WorkTabController(CMySbCommand cmd, IHttpClientFactory httpClientFactory)
        {
            cmd.ActiveDatabase = "Report"; _httpClientFactory = httpClientFactory;
            this.cmd = cmd;
        }
      
      
        [HttpGet]
        public async Task<Object> GetWeatherInfo(string cityCode)
        {
            string url = @$"http://api.map.baidu.com/weather/v1/?ak=Qa6pXVRjlsnQrI8mNyNxHvTck4E3gndl&&data_type=all&&district_id={cityCode}";

            using (HttpClient client = CPubVars.GetHttpClientFromFactory(_httpClientFactory))
            {
                var response = await client.GetAsync(url);

                var res = await response.Content.ReadAsStringAsync();
                  
                return Json(res);
            } 
            
        }

        [HttpGet]

        public async Task<object> GetCompanyAddressInfo(string operKey)
        {
            Security.GetInfoFromOperKey(operKey, out string companyID);
            var sql = $@"SELECT province,city,county,company_id FROM g_company WHERE company_id={companyID}";
            object records = await CDbDealer.Get1RecordFromSQLAsync(sql, cmd);

            return Json(records);
        }
        [HttpGet]
        //get销量数额列表
        public async Task<object> GetSaleTrendList()
        {
            DateTime Today = DateTime.Now;
            var limit = DateTime.DaysInMonth(Today.Year, Today.Month);
            var sql = $@"
SELECT COALESCE(a.interval,b.t) as INTERVAL,COALESCE(net_amount,0) AS net_amount 
FROM 
(
    SELECT  round( SUM (money_inout_flag * total_amount)::numeric,2) AS net_amount,to_char( happen_time, 'MM-dd' ) AS INTERVAL
			          
    FROM sheet_sale_main sm 
     
    WHERE company_id = {Token.CompanyID} AND approve_time IS NOT NULL AND red_flag IS NULL 
          AND ( happen_time between '{DateTime.Now.AddDays(1 - DateTime.Now.Day).Date.ToString("yyyy-MM-dd")} 00:00:00' and '{DateTime.Now.AddDays(1 - DateTime.Now.Day).Date.AddMonths(1).AddSeconds(-1).ToString("yyyy-MM-dd")} 23:59:59') 
    GROUP BY INTERVAL
) a
full join 
(
    select to_char(b, 'MM-DD') as t ,b as ty
    from generate_series(to_timestamp('{DateTime.Now.AddDays(1 - DateTime.Now.Day).Date.ToString("yyyy-MM-dd")}', 'YYYY-MM-DD'), 
                        to_timestamp('{DateTime.Now.AddDays(1 - DateTime.Now.Day).Date.AddMonths(1).AddSeconds(-1).ToString("yyyy-MM-dd")}', 'YYYY-MM-DD'), '1 days') as b
) b on a.interval = b.t order by ty desc";
            object records = await CDbDealer.GetRecordsFromSQLAsync(sql, cmd);
            return records;
        }

        [HttpGet]
        public async Task<ActionResult> GetPerformanceList([FromQuery] string operKey)
        {
            if (string.IsNullOrWhiteSpace(operKey))
            {
                return BadRequest();
            }

            Security.GetInfoFromOperKey(operKey, out string companyId, out string operId);
            DateTime Today = DateTime.Now;
            string monthFirstDay=Today.Year.ToString() + "-" + Today.Month.ToString() + "-" + Today.Day.ToString();

          //  var limit = DateTime.DaysInMonth(Today.Year, Today.Month);

            var condi = $"where m.company_id = {companyId}  and approve_time is not null and red_flag is null ";
            var sql1 = @$"
select oper_name,
   sum(case when quantity*inout_flag*(-1)>=0 then inout_flag*(-1)*sub_amount else 0 end) sale_sum,
   abs(sum(case when quantity*inout_flag*(-1)<0 then inout_flag*(-1)*sub_amount else 0 end)) return_sum,
   sum(case when sub_amount = 0 then quantity * orig_price else 0 end) give_sum
from sheet_sale_main sm
left join sheet_sale_detail sd on sm.company_id = {companyId} and sm.sheet_id = sd.sheet_id and sd.company_id = {companyId}
left join info_operator o on sm.seller_id = o.oper_id and o.company_id = {companyId}
where sm.company_id= {companyId} and sm.approve_time is not null and sm.red_flag is null and sm.seller_id is not null and sm.happen_time>'{monthFirstDay}' and sd.happen_time>'{monthFirstDay}'  
group by oper_name order by oper_name";
          
            /*var sql2 = @$"
select oper_name,abs(sum(case when quantity*inout_flag*(-1)<0 then inout_flag*(-1)*sub_amount else 0 end)) sum
from sheet_sale_main sm
left join sheet_sale_detail sd on sm.company_id = {companyId}  and sd.company_id = {companyId} and sm.sheet_id = sd.sheet_id
left join info_operator o on sm.seller_id = o.oper_id and o.company_id = {companyId}
where sm.company_id= {companyId} and sm.approve_time is not null and sm.red_flag is null and sm.seller_id is not null and sm.happen_time>'{monthFirstDay}'
group by oper_name order by oper_name";
            var sql3 = @$"
select oper_name,sum(case when sub_amount = 0 and wholesale_price is not null then quantity*d.unit_factor*wholesale_price else 0 end)
from sheet_sale_main sm 
left join sheet_sale_detail d on sm.sheet_id = d.sheet_id and d.company_id = {companyId}
left join info_operator o on sm.seller_id = o.oper_id and o.company_id = {companyId}
left join info_item_multi_unit u on d.item_id = u.item_id and u.company_id = {companyId} and unit_type='s'
where sm.company_id = {companyId} and red_flag is null and approve_time is not null and sm.happen_time>'{monthFirstDay}'
group by oper_name order by oper_name";
            */

            List<ExpandoObject> records = await CDbDealer.GetRecordsFromSQLAsync(sql1, cmd);


           // List<ExpandoObject> records1 = await CDbDealer.GetRecordsFromSQLAsync(sql1, cmd);
            //List<ExpandoObject> records2 = await CDbDealer.GetRecordsFromSQLAsync(sql2, cmd);
           // List<ExpandoObject> records3 = await CDbDealer.GetRecordsFromSQLAsync(sql3, cmd);
            return Json(new { 销售金额 = records, 退货金额 = records, 赠送金额 = records });
        }

        [HttpGet]
        public async Task<JsonResult> GetSaleOrderCount([FromQuery] string operKey, [FromQuery] bool isLastMonth)
        {
            var sql = isLastMonth ? @$"select count(sheet_id) from sheet_sale_order_main 
                            {Where(false, true)} and sheet_type = 'XD';
                            select count(sheet_id) from sheet_sale_order_main {Where(true)} and sheet_type = 'XD';" :
                            @$"select count(sheet_id) from sheet_sale_order_main {Where(false)} and sheet_type = 'XD';
                            select count(sheet_id) from sheet_sale_order_main {Where(true)} and sheet_type = 'XD';";

            cmd.CommandText = sql;
            var dr = await cmd.ExecuteReaderAsync();
            string count_M = "0";
            string count_D = "0";
            while (dr.Read())
            {
                count_M = dr[0].ToString();
            }
            dr.NextResult();
            while (dr.Read())
            {
                count_D = dr[0].ToString();
            }
            dr.Close();
            return Json(new { count_M, count_D });
        }
        [HttpGet]
        public async Task<JsonResult> GetSaleOrderSum([FromQuery] string operKey, [FromQuery] bool isLastMonth)
        {
            var sql = isLastMonth ?
                @$"select sum(total_amount * money_inout_flag) from sheet_sale_order_main {Where(false, true)} 
                            select sum(total_amount * money_inout_flag) from sheet_sale_order_main {Where(true)};" :

                @$"select sum(total_amount * money_inout_flag) from sheet_sale_order_main {Where(false)};
                select sum(total_amount * money_inout_flag) from sheet_sale_order_main {Where(true)};";

            cmd.CommandText = sql;
            var dr = await cmd.ExecuteReaderAsync();
            string sum_M = "0";
            string sum_D = "0";
            while (dr.Read())
            {
                sum_M = dr[0].ToString();
            }
            dr.NextResult();
            while (dr.Read())
            {
                sum_D = dr[0].ToString();
            }
            dr.Close();
            return Json(new { sum_M, sum_D });
        }

        [HttpGet]
        public async Task<JsonResult> GetSaleSum([FromQuery] string operKey, [FromQuery] bool isLastMonth)
        {
            var sql = isLastMonth ? @$" SELECT sum(sm.total_amount*sm.money_inout_flag) as amount_M FROM sheet_sale_main sm {Where(false, true)};
                            SELECT sum(sm.total_amount*sm.money_inout_flag) as amount_D FROM sheet_sale_main sm {Where(true)} ;" :
                            @$"SELECT sum(sm.total_amount*sm.money_inout_flag) as amount_M FROM sheet_sale_main sm {Where(false)};
                            SELECT sum(sm.total_amount*sm.money_inout_flag) as amount_D FROM sheet_sale_main sm {Where(true)} ;"; ;
            cmd.CommandText = sql;
            var dr = await cmd.ExecuteReaderAsync();
            string count_M = "0";
            string count_D = "0";
            while (dr.Read())
            {
                count_M = dr[0].ToString();
            }
            dr.NextResult();
            while (dr.Read())
            {
                count_D = dr[0].ToString();
            }
            dr.Close();
            return Json(new { count_M, count_D });
        }

        [HttpGet]
        public async Task<JsonResult> GetFeeOutSum([FromQuery] string operKey, [FromQuery] bool isLastMonth)
        {
            Security.GetInfoFromOperKey(operKey, out string companyId);
            var sql = isLastMonth ? @$"SELECT sum(total_amount*money_inout_flag) as amount_M FROM sheet_fee_out_main {Where(false, true)} and sheet_type='ZC'  ;
                            SELECT sum(total_amount*money_inout_flag) as amount_D FROM sheet_fee_out_main {Where(true)}  and sheet_type='ZC' " :
                            @$"SELECT sum(amount_M) as amount_M FROM 
                                    (
                                    SELECT sum(total_amount*money_inout_flag) as amount_M FROM
                                        sheet_fee_out_main {Where(false)} and sheet_type='ZC'
                                                        UNION
                                                        SELECT		
                                                        CASE		
                                                        WHEN payway1_id = cs.sub_id THEN	SUM ( -M.payway1_amount * M.money_inout_flag ) 	WHEN payway2_id = cs.sub_id THEN	SUM (  -M.payway2_amount * M.money_inout_flag ) 		END amount_M
                                                        FROM	sheet_sale_main	M 
                                                        LEFT JOIN cw_subject cs ON cs.company_id = M.company_id 	AND ( M.payway1_id = cs.sub_id OR M.payway2_id = cs.sub_id )
       	                                              where happen_time >= '{DateTime.Now.GetMonthStart()}' and  M.company_id = {companyId}  and red_flag is null and approve_time is not null	and cs.sub_type = 'ZC' GROUP BY 
				                                         payway1_id,payway2_id,sub_id
                                     ) T;
                            SELECT sum(amount_D) as amount_D FROM 
                            (
                                      SELECT sum(total_amount*money_inout_flag) as amount_D FROM sheet_fee_out_main {Where(true)} and sheet_type='ZC'
                                                     UNION
                                                        SELECT		
                                                        CASE		
                                                        WHEN payway1_id = cs.sub_id THEN	SUM ( -M.payway1_amount * M.money_inout_flag ) 	WHEN payway2_id = cs.sub_id THEN	SUM (  -M.payway2_amount * M.money_inout_flag ) 		END amount_D
                                                        FROM	sheet_sale_main	M 
                                                        LEFT JOIN cw_subject cs ON cs.company_id = M.company_id 	AND ( M.payway1_id = cs.sub_id OR M.payway2_id = cs.sub_id )
       	                                              where happen_time between '{DateTime.Today}' and '{DateTime.Today.AddDays(1)}' and  M.company_id = {companyId}  and red_flag is null and approve_time is not null	and cs.sub_type = 'ZC' GROUP BY 
				                                         payway1_id,payway2_id,sub_id
                                    ) t
                           ;";
            cmd.CommandText = sql;
            var dr = await cmd.ExecuteReaderAsync();
            string count_M = "0";
            string count_D = "0";
            while (dr.Read())
            {
                count_M = dr[0].ToString();
            }
            dr.NextResult();
            while (dr.Read())
            {
                count_D = dr[0].ToString();
            }
            dr.Close();
            return Json(new { count_M, count_D });
        }

        [HttpGet]
        public async Task<JsonResult> GetProfitSum([FromQuery] string operKey, [FromQuery] bool isLastMonth)
        {
            var sql = "";
            if (isLastMonth)
            {
                sql = @$"SELECT (SELECT sum(total_amount*money_inout_flag) FROM sheet_sale_main {Where(false, true)}) + (SELECT sum(total_amount*money_inout_flag) FROM sheet_fee_out_main {Where(false, true)}) as amount_M;
                         SELECT (SELECT sum(total_amount*money_inout_flag) FROM sheet_sale_main {Where(true)}) + (SELECT sum(total_amount*money_inout_flag) FROM sheet_fee_out_main {Where(true)}) as amount_D;";
            }
            else
            {
                sql = @$"SELECT (SELECT sum(total_amount*money_inout_flag) FROM sheet_sale_main {Where(false)}) + (SELECT sum(total_amount*money_inout_flag) FROM sheet_fee_out_main {Where(false)}) as amount_M;
                         SELECT (SELECT sum(total_amount*money_inout_flag) FROM sheet_sale_main {Where(true)}) + (SELECT sum(total_amount*money_inout_flag) FROM sheet_fee_out_main {Where(true)}) as amount_D;";

            }

            cmd.CommandText = sql;
            var dr = await cmd.ExecuteReaderAsync();
            string count_M = "0";
            string count_D = "0";
            while (dr.Read())
            {
                count_M = dr[0].ToString();
            }
            dr.NextResult();
            while (dr.Read())
            {
                count_D = dr[0].ToString();
            }
            dr.Close();
            return Json(new { count_M, count_D });
        }

        [HttpGet]
        public async Task<JsonResult> GetIpAddress()
        {
            var ipAddress = HttpContext.Connection.RemoteIpAddress.ToString();
            return Json(new { ipAddress });
        }

        //[HttpGet]
        //public async Task<JsonResult> GetTotalSaleSheet()
        //{
        //    var sql = $"select count(*) from sheet_sale_main where approve_time is null and company_id = {Token.CompanyID}";
        //    cmd.CommandText = sql;
        //    var dr = await cmd.ExecuteReaderAsync();
        //    string count_X = "0";
        //    while (dr.Read())
        //    {
        //        count_X = dr[0].ToString();
        //    }
        //    dr.NextResult();
        //    return Json(new { count_X});
        //}

        [HttpGet]
        public async Task<JsonResult> GetTotalSheetNum([FromQuery] string flowExistMove = "True")
        {
            DateTime Today = DateTime.Now;
            var condi = $"where approve_time is null and company_id = {Token.CompanyID}";
           
            var happentime =
                $"happen_time between '{Today.AddDays(-15).ToString("yyyy-MM-dd")} 00:00:00' and '{Today.ToString("yyyy-MM-dd")} 23:59:59'";
            var total = $"select count(*)";
            //销售单
            var sqlx = @$"{total} from sheet_sale_main {condi} and money_inout_flag >= 0 and red_flag is null and {happentime}";
            //销售订单
            var sqlxd = @$"{total} from sheet_sale_order_main m 
	                    where approve_time is null and sheet_type = 'XD' AND m.company_id = {Token.CompanyID} and m.happen_time between '{Today.AddDays(-15).ToString("yyyy-MM-dd")} 00:00:00' and '{Today.ToString("yyyy-MM-dd")} 23:59:59' and coalesce(m.is_del, false) = false and m.is_del is null ";
            //调拨
            var sqldb = $"{total} from sheet_move_main {condi} and {happentime}";
            //采购
            var sqlc = $"{total} from sheet_buy_main {condi} and {happentime}";
            //支出
            var sqlzc = $"{total} from sheet_fee_out_main {condi} and {happentime} and sheet_type = 'ZC'";
            //收款
            var sqlsk = $"{total} from sheet_get_arrears_main {condi} and {happentime} and sheet_type='SK'";
            //盘点
            var sqlpd = $"{total} from sheet_inventory_main {condi} and {happentime}";
            //退货单
            var sqlt = $@"{total} from sheet_sale_main m 
	                        where approve_time is null and sheet_type = 'T' AND m.company_id = {Token.CompanyID} and m.happen_time between '{Today.AddDays(-15).ToString("yyyy-MM-dd")} 00:00:00' and '{Today.ToString("yyyy-MM-dd")} 23:59:59'";
            //退货订单
            var sqltd = @$"{total} from sheet_sale_order_main m 
	                    where approve_time is null and sheet_type = 'TD' AND m.company_id = {Token.CompanyID} and m.happen_time between '{Today.AddDays(-15).ToString("yyyy-MM-dd")} 00:00:00' and '{Today.ToString("yyyy-MM-dd")} 23:59:59' and m.is_del is null";
            //退货订单
            var sqldh = @$"{total} from sheet_prepay
	                    where approve_time is null and sheet_type = 'DH' AND company_id = {Token.CompanyID} and happen_time between '{Today.AddDays(-15).ToString("yyyy-MM-dd")} 00:00:00' and '{Today.ToString("yyyy-MM-dd")} 23:59:59'";
            var order_status = "";
            var isQuery = true;
            if (flowExistMove == "False")
            {
                order_status = "dd";
                isQuery = false;
            }
            else
            {
                order_status = "zc";
                isQuery = true;
            }
            //待装车
            var sqldzc =
                @$"select count(som.sheet_id) 
                    from sheet_sale_order_main som  
                    LEFT JOIN sheet_status_order oss on oss.company_id = som.company_id and oss.sheet_id = som.sheet_id
                    LEFT JOIN sheet_sale_main sm on sm.company_id = som.company_id and sm.order_sheet_id = som.sheet_id and sm.happen_time >= '{Today.AddDays(-15).ToString("yyyy-MM-dd")}'
                    where som.company_id = {Token.CompanyID} and {isQuery} and som.happen_time between '{Today.AddDays(-15).ToString("yyyy-MM-dd")} 00:00:00' and '{Today.ToString("yyyy-MM-dd")} 23:59:59' and(oss.order_status is null or oss.order_status in ('xd', 'dd')) and som.red_flag is null and som.approve_time is not null and sm.order_sheet_id is null";
            //待转单
            var sqlzd =
                 @$"select count(som.sheet_id) 
                    from sheet_sale_order_main som  
                    LEFT JOIN sheet_status_order oss on oss.company_id = som.company_id and oss.sheet_id = som.sheet_id 
                    LEFT JOIN sheet_sale_main sm on sm.company_id = som.company_id and sm.order_sheet_id = som.sheet_id and sm.happen_time >= '{Today.AddDays(-15).ToString("yyyy-MM-dd")}' and sm.red_flag is null 
                    where som.company_id = {Token.CompanyID} and {isQuery} and som.happen_time between '{Today.AddDays(-15).ToString("yyyy-MM-dd")} 00:00:00' and '{Today.ToString("yyyy-MM-dd")} 23:59:59' and COALESCE(oss.order_status ,'xd') in ('xd', 'dd','zc','fh') and som.red_flag is null and som.approve_time is not null and sm.order_sheet_id is null ";
            List<ExpandoObject> records1 = await CDbDealer.GetRecordsFromSQLAsync(sqlx, cmd);
            List<ExpandoObject> records2 = await CDbDealer.GetRecordsFromSQLAsync(sqlxd, cmd);
            List<ExpandoObject> records3 = await CDbDealer.GetRecordsFromSQLAsync(sqldb, cmd);
            List<ExpandoObject> records4 = await CDbDealer.GetRecordsFromSQLAsync(sqlc, cmd);
            List<ExpandoObject> records5 = await CDbDealer.GetRecordsFromSQLAsync(sqlzc, cmd);
            List<ExpandoObject> records6 = await CDbDealer.GetRecordsFromSQLAsync(sqlsk, cmd);
            List<ExpandoObject> records7 = await CDbDealer.GetRecordsFromSQLAsync(sqlpd, cmd);
            List<ExpandoObject> records8 = await CDbDealer.GetRecordsFromSQLAsync(sqlt, cmd);
            List<ExpandoObject> records9 = await CDbDealer.GetRecordsFromSQLAsync(sqltd, cmd);
            List<ExpandoObject> records10 = await CDbDealer.GetRecordsFromSQLAsync(sqldzc, cmd);
            List<ExpandoObject> records11 = await CDbDealer.GetRecordsFromSQLAsync(sqlzd, cmd);
            List<ExpandoObject> records12 = await CDbDealer.GetRecordsFromSQLAsync(sqldh, cmd);

            return Json(new
            {
                saleCount = records1,
                saleordercount = records2,
                dbsheet = records3,
                cgsheet = records4,
                feeout = records5,
                sksheet = records6,
                pdsheet = records7,
                saleruturnsheet = records8,
                salereturnordersheet = records9,
                zcsheet = records10,
                zdsheet = records11,
                dhsheet = records12
            });
        }
        private string Where(bool today, bool lastMonth = false)
        {
            if (lastMonth)
            {
                var lastMonthStart = DateTime.Now.AddMonths(-1).GetMonthStart();
                var lastMonthEnd = DateTime.Now.AddDays(-DateTime.Now.Day);
                var condition = $"WHERE company_id = {Token.CompanyID} and " + $"happen_time between '{lastMonthStart}' and '{lastMonthEnd:yyyy-MM-dd} 23:59:59' and" +
                                $" red_flag is null and approve_time is not null ";
                return condition;
            }
            else
            {
                var condition = $"WHERE company_id = {Token.CompanyID} and " + (today
                    ? $"happen_time >= '{DateTime.Today}'"
                    : $"happen_time >= '{DateTime.Now.GetMonthStart()}'") + $" and red_flag is null and approve_time is not null ";
                return condition;
            }
        }
    }
}